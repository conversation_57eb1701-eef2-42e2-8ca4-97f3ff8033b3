// paymentHooks.ts
// Custom React hooks for payment logic

import { useState } from 'react';
import { TrialInfo, PaymentMethod } from './paymentTypes';

export function useTrial(initialValue?: TrialInfo) {
  const [trialInfo, setTrialInfo] = useState<TrialInfo | undefined>(initialValue);
  const [hasShownTrialCard, setHasShownTrialCard] = useState(false);
  return { trialInfo, setTrialInfo, hasShownTrialCard, setHasShownTrialCard };
}

export function usePaymentMethods(initialValue?: PaymentMethod) {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>(initialValue || null);
  return { selectedPaymentMethod, setSelectedPaymentMethod };
}
// Add other hooks as needed 