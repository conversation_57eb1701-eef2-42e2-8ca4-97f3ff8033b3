"use client"

import React from 'react';

interface CancellationPolicyVisualProps {
  policyName: string; // 'Flexibles', 'Dur', 'Modérées', or 'Strictes'
  checkInDate: Date;
  bookingDate?: Date; // Date when booking was made (for Strictes policy which needs booking time)
}

interface CutoffDates {
  fullRefundDate: Date;
  partialRefundDate: Date | null;
  noRefundDate: Date;
}

const CancellationPolicyVisual: React.FC<CancellationPolicyVisualProps> = ({ 
  policyName, 
  checkInDate, 
  bookingDate = new Date() 
}) => {
  // Calculate the cutoff dates based on policy type
  const cutoffDates = calculateCutoffDates(policyName, checkInDate, bookingDate);
  
  // Format dates for display
  const formatDate = (date: Date) => {
    // Format as "Apr 15" style
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };
  
  return (
    <div className="mt-8 mb-6 bg-blue-50 p-5 rounded-lg">
      <div className="max-w-md mx-auto">
        {/* Policy labels */}
        <div className="flex justify-between text-sm mb-2">
          <div className="text-center">
            Remboursement<br/>intégral
          </div>
          <div className="text-center">
            Remboursement<br/>partiel
          </div>
          <div className="text-center">
            Aucun<br/>remboursement
          </div>
        </div>
        
        {/* Timeline visualization */}
        <div className="relative mt-2">
          {/* Horizontal line - positioned to cut through the center of the circles */}
          <div className="h-0.5 bg-booking-orange w-full absolute top-[8px]"></div>
          
          {/* Dots for key dates */}
          <div className="flex justify-between relative h-5">
            {/* Today dot - filled */}
            <div className="flex flex-col items-center">
              <div className="w-4 h-4 rounded-full bg-booking-orange z-10"></div>
            </div>
            
            {/* First cutoff date dot - filled */}
            <div className="flex flex-col items-center">
              <div className="w-4 h-4 rounded-full bg-booking-orange z-10"></div>
            </div>
            
            {/* Second cutoff date dot - outlined */}
            {cutoffDates.partialRefundDate && (
              <div className="flex flex-col items-center">
                <div className="w-4 h-4 rounded-full bg-blue-50 border-[1px] border-booking-orange z-10"></div>
              </div>
            )}
            
            {/* Departure dot - outlined */}
            <div className="flex flex-col items-center">
              <div className="w-4 h-4 rounded-full bg-blue-50 border-[1px] border-booking-orange z-10"></div>
            </div>
          </div>
          
          {/* Date labels */}
          <div className="flex justify-between text-sm mt-1">
            <div className="text-center">
              Aujourd&apos;hui
            </div>
            <div className="text-center">
              {formatDate(cutoffDates.fullRefundDate)}
            </div>
            {cutoffDates.partialRefundDate && (
              <div className="text-center">
                {formatDate(cutoffDates.partialRefundDate)}
              </div>
            )}
            <div className="text-center">
              Départ
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper function to calculate cutoff dates based on policy type
function calculateCutoffDates(policyName: string, checkInDate: Date, bookingDate: Date): CutoffDates {
  // Create date copies to avoid mutation
  const checkIn = new Date(checkInDate);
  
  // Default result structure
  const result: CutoffDates = {
    fullRefundDate: new Date(),
    partialRefundDate: null,
    noRefundDate: new Date(checkIn),
  };
  
  switch(policyName) {
    case 'Flexibles':
      // Full refund until 2 days before arrival
      result.fullRefundDate = new Date(checkIn);
      result.fullRefundDate.setDate(checkIn.getDate() - 2);
      
      // No partial refund period for Flexibles
      result.partialRefundDate = null;
      result.noRefundDate = new Date(result.fullRefundDate);
      result.noRefundDate.setDate(result.fullRefundDate.getDate() + 1);
      break;
      
    case 'Modérées':
      // Full refund until 7 days before arrival
      result.fullRefundDate = new Date(checkIn);
      result.fullRefundDate.setDate(checkIn.getDate() - 7);
      
      // Partial refund from 7 days until check-in
      result.partialRefundDate = new Date(checkIn);
      result.partialRefundDate.setDate(checkIn.getDate() - 1);
      
      result.noRefundDate = new Date(checkIn);
      break;
      
    case 'Dur':
      // Full refund if cancelled at least 30 days before arrival
      result.fullRefundDate = new Date(checkIn);
      result.fullRefundDate.setDate(checkIn.getDate() - 30);
      
      // Partial refund from 30 days until check-in
      result.partialRefundDate = new Date(checkIn);
      result.partialRefundDate.setDate(checkIn.getDate() - 1);
      
      result.noRefundDate = new Date(checkIn);
      break;
      
    case 'Strictes':
      // Full refund if cancelled within 48 hours of booking and at least 14 days before check-in
      const bookingPlus48Hours = new Date(bookingDate);
      bookingPlus48Hours.setHours(bookingPlus48Hours.getHours() + 48);
      
      const checkInMinus14Days = new Date(checkIn);
      checkInMinus14Days.setDate(checkIn.getDate() - 14);
      
      // Use the earlier of the two dates
      result.fullRefundDate = new Date(
        Math.min(bookingPlus48Hours.getTime(), checkInMinus14Days.getTime())
      );
      
      // Partial refund until check-in
      result.partialRefundDate = new Date(checkIn);
      result.partialRefundDate.setDate(checkIn.getDate() - 1);
      
      result.noRefundDate = new Date(checkIn);
      break;
      
    default:
      // Default to Strictes as fallback
      const defaultBookingPlus48Hours = new Date(bookingDate);
      defaultBookingPlus48Hours.setHours(defaultBookingPlus48Hours.getHours() + 48);
      
      const defaultCheckInMinus14Days = new Date(checkIn);
      defaultCheckInMinus14Days.setDate(checkIn.getDate() - 14);
      
      result.fullRefundDate = new Date(
        Math.min(defaultBookingPlus48Hours.getTime(), defaultCheckInMinus14Days.getTime())
      );
      
      result.partialRefundDate = new Date(checkIn);
      result.partialRefundDate.setDate(checkIn.getDate() - 1);
      
      result.noRefundDate = new Date(checkIn);
  }
  
  return result;
}

export default CancellationPolicyVisual; 