import { formatDate, formatCurrency } from './formatters';

type Invoice = {
    id: string;
    invoice_number: string;
    start_date: string;
    end_date: string;
    subscription_plan: string;
    payment_amount: number;
    listings: {
        id: string;
        title: string;
    };
    profile?: {
        fullname: string;
        phone_number: string;
        email: string;
    };
};

/**
 * Generates a CSV string from invoice data
 * @param invoices Array of invoices to include in the CSV
 * @returns CSV string
 */
export function generateInvoiceCSV(invoices: Invoice[]): string {
    // Define CSV headers
    const headers = [
        'N° Facture',
        'Date début',
        'Date fin',
        'Hébergement',
        'Plan',
        'Montant',
        'Client',
        'Email',
        'Téléphone'
    ];

    // Convert invoices to CSV rows
    const rows = invoices.map(invoice => [
        invoice.invoice_number,
        formatDate(invoice.start_date),
        formatDate(invoice.end_date),
        invoice.listings?.title || 'N/A',
        invoice.subscription_plan,
        formatCurrency(invoice.payment_amount),
        invoice.profile?.fullname || 'N/A',
        invoice.profile?.email || 'N/A',
        invoice.profile?.phone_number || 'N/A'
    ]);

    // Combine headers and rows
    const csvContent = [
        headers.join(','),
        ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');

    return csvContent;
}