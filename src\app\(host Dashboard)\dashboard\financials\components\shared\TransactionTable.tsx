'use client';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import TransactionRow, { Transaction } from './TransactionRow';

interface TransactionTableProps {
  transactions: Transaction[];
  onRowClick?: (transaction: Transaction) => void;
}

const TransactionTable: React.FC<TransactionTableProps> = ({ 
  transactions,
  onRowClick
}) => {
  const [activeRowId, setActiveRowId] = useState<string | null>(null);
  const router = useRouter();

  const handleRowClick = (transaction: Transaction) => {
    setActiveRowId(transaction.id);
    if (onRowClick) {
      onRowClick(transaction);
    } else {
      router.push(`/dashboard/financials/transaction-details?id=${transaction.id}`);
    }
  };

  return (
    <div className="bg-[#F6FAFF] rounded-lg overflow-hidden border border-[#E8EAED]">
      {/* Table Header */}
      <div className="grid grid-cols-3 bg-[#EAF4FF] px-6 py-3 text-[12px] font-semibold text-gray-700">
        <div className="text-left">N° réservation</div>
        <div className="text-center">Montant</div>
        <div className="pl-2">Statut</div>
      </div>

      {/* Table Rows */}
      <div>
        {transactions.length > 0 ? (
          transactions.map((transaction) => (
            <TransactionRow
              key={transaction.id}
              transaction={transaction}
              highlight={transaction.id === activeRowId}
              onClick={() => handleRowClick(transaction)}
            />
          ))
        ) : (
          <div className="py-8 text-center text-gray-500">
            Aucune transaction trouvée
          </div>
        )}
      </div>
    </div>
  );
};

export default TransactionTable; 