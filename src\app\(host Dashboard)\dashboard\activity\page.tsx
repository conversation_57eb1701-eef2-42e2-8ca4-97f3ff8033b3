import { getRecentAuditLogs } from "@/app/actions/get-audit-logs";
import { createClient } from "@/utils/supabase/server";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

export default async function ActivityPage() {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user } } = await supabase.auth.getUser();
  
  // Fetch more audit logs (25 instead of just 5)
  const auditLogs = await getRecentAuditLogs(25, user?.id);
  
  // Function to format the activity message
  const formatActivityMessage = (log: any) => {
    const entityName = log.entity_name || `élément #${log.record_id}`;
    const tableName = log.table_name.replace(/_/g, ' ');
    
    // Format based on operation and table
    switch (log.operation) {
      case "INSERT":
        return `Création de ${tableName}: "${entityName}"`;
      case "UPDATE":
        if (log.table_name === 'listings' && log.changed_fields?.status) {
          const status = log.changed_fields.status.new;
          switch (status) {
            case "active":
              return `Annonce "${entityName}" activée`;
            case "pending":
              return `Annonce "${entityName}" mise en attente d'approbation`;
            case "declined":
              return `Annonce "${entityName}" refusée`;
            default:
              return `Statut de l'annonce "${entityName}" modifié: ${status}`;
          }
        } else if (log.table_name === 'bookings' && log.changed_fields?.status) {
          const status = log.changed_fields.status.new;
          switch (status) {
            case "confirmed":
              return `Réservation pour "${entityName}" confirmée`;
            case "canceled":
              return `Réservation pour "${entityName}" annulée`;
            default:
              return `Statut de réservation pour "${entityName}" modifié: ${status}`;
          }
        }
        return `Mise à jour de ${tableName}: "${entityName}"`;
      case "DELETE":
        return `Suppression de ${tableName}: "${entityName}"`;
      default:
        return `Action sur ${tableName}: "${entityName}"`;
    }
  };
  
  // Group logs by date for better organization
  const groupedLogs: { [date: string]: typeof auditLogs } = {};
  
  auditLogs.forEach(log => {
    const date = format(new Date(log.timestamp), 'yyyy-MM-dd');
    if (!groupedLogs[date]) {
      groupedLogs[date] = [];
    }
    groupedLogs[date].push(log);
  });
  
  // Sort dates in descending order
  const sortedDates = Object.keys(groupedLogs).sort((a, b) => b.localeCompare(a));
  
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8 flex items-center">
        <h1 className="text-2xl font-bold">Historique des activités</h1>
      </div>
      
      {sortedDates.length === 0 ? (
        <div className="text-center py-12 text-gray-500">
          Aucune activité à afficher
        </div>
      ) : (
        sortedDates.map(date => (
          <div key={date} className="mb-10">
            <h2 className="text-lg font-semibold mb-4 text-gray-700 border-b pb-2">
              {format(new Date(date), 'EEEE dd MMMM yyyy', { locale: fr })}
            </h2>
            
            <div className="space-y-4">
              {groupedLogs[date].map(log => (
                <div key={log.id} className="bg-white rounded-lg p-5 shadow-sm border">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium text-lg">{formatActivityMessage(log)}</h3>
                      <p className="text-sm text-gray-500 mt-1">
                        {format(new Date(log.timestamp), 'HH:mm')} • 
                        {log.user_data?.fullname ? ` Par ${log.user_data.fullname}` : ' Système'}
                      </p>
                    </div>
                    <div className="bg-gray-100 px-3 py-1 rounded-full text-xs font-medium uppercase">
                      {log.operation}
                    </div>
                  </div>
                  
                  {log.operation === 'UPDATE' && log.changed_fields && Object.keys(log.changed_fields).length > 0 && (
                    <div className="mt-4">
                      <p className="text-sm font-medium text-gray-700 mb-2">Champs modifiés:</p>
                      <div className="bg-gray-50 p-3 rounded text-xs font-mono overflow-x-auto">
                        {Object.entries(log.changed_fields)
                          .filter(([_, value]: [string, any]) => {
                            // Skip empty objects or when both old and new values are empty
                            if (!value || typeof value !== 'object') return false;
                            if (Object.keys(value).length === 0) return false;
                            
                            // Skip if both old and new are empty objects
                            const oldValueEmpty = !value.old || (typeof value.old === 'object' && Object.keys(value.old).length === 0);
                            const newValueEmpty = !value.new || (typeof value.new === 'object' && Object.keys(value.new).length === 0);
                            return !(oldValueEmpty && newValueEmpty);
                          })
                          .map(([key, value]: [string, any]) => (
                          <div key={key} className="mb-1">
                            <span className="text-purple-600">{key === 'nightly_rate' ? 'prix par nuit:' : `${key}:`}</span> 
                            <span className="text-red-500 line-through ml-1">
                              {JSON.stringify(value.old)}
                            </span>
                            <span className="text-green-600 ml-1">
                              {JSON.stringify(value.new)}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))
      )}
    </div>
  );
} 