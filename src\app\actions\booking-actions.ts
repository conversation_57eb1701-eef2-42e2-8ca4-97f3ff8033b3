"use server"

import { createBookingRequest } from "@/app/actions/create-booking"
import { initializeConversation } from "@/app/actions/initialize-conversation"
import { createClient } from "@/utils/supabase/server"

export async function createBookingAndConversation({
                                                       listingId,
                                                       startDate,
                                                       endDate,
                                                       numGuests,
                                                       totalPrice,
                                                       hostId,
                                                       initialMessage,
                                                       affiliateCodeId,
                                                   }: {
    listingId: string
    startDate: Date
    endDate: Date
    numGuests: number
    totalPrice: number
    hostId: string
    initialMessage?: string
    affiliateCodeId?: string
}) {
    try {
        // First create the booking request
        const bookingResponse = await createBookingRequest({
            listingId,
            startDate,
            endDate,
            numGuests,
            totalPrice,
            hostId,
        })

        if (!bookingResponse.success || !bookingResponse.data) {
            return bookingResponse
        }

        // Handle affiliate code if provided
        if (affiliateCodeId) {
            try {
                const supabase = await createClient()
                await supabase
                    .from('booking_affiliate_codes')
                    .insert({
                        booking_id: bookingResponse.data.id,
                        affiliate_code_id: affiliateCodeId
                    })
            } catch (error) {
                console.error("Error handling affiliate code:", error);
            }
        }

        // When creating the conversation, use the provided initialMessage if available
        // or fall back to a default message
        const message = initialMessage || "Je suis intéressé par votre logement.";

        // Then initialize conversation with the new booking ID
        const conversationResponse = await initializeConversation({
            hostId,
            listingId,
            bookingId: bookingResponse.data.id,
            checkIn: startDate,
            checkOut: endDate,
            guests: numGuests,
            initialMessage: message,
        })

        if (!conversationResponse.success) {
            throw new Error("Échec de l'initialisation de la conversation")
        }

        return {
            success: true,
            conversationId: conversationResponse.data.id,
            bookingId: bookingResponse.data.id
        }
    } catch (error: any) {
        console.error("Error creating booking and conversation:", error)
        return { success: false, error: error.message }
    }
}
