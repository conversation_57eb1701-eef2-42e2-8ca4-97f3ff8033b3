import React, { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { X } from 'lucide-react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import {
  modalSectionVariants,
  modalElementVariants
} from '../animationVariants';

interface SecureReservationModalProps {
  isOpen: boolean;
  onClose: () => void;
  hasPaymentMethod: boolean;
  setIsPaymentModalOpen: (open: boolean) => void;
}

const SecureReservationModal: React.FC<SecureReservationModalProps> = ({
  isOpen,
  onClose,
  hasPaymentMethod,
  setIsPaymentModalOpen
}) => {
  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" />
        </Transition.Child>
        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded bg-white shadow-xl transition-all max-h-[90vh] overflow-y-auto">
                <motion.div className="p-4 sm:p-6 md:p-8" variants={modalSectionVariants} initial="hidden" animate="visible" exit="exit">
                  <div className="absolute right-4 top-4">
                    <button
                      type="button"
                      className="text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-200 p-1.5 rounded-full"
                      onClick={onClose}
                    >
                      <X className="h-5 w-5" />
                    </button>
                  </div>
                  <motion.div variants={modalElementVariants}>
                    <div className="text-center mb-6">
                      <div className="flex justify-center mb-3">
                        <div className="w-14 h-14 flex items-center justify-center">
                          <Image
                            src="https://api.almindharbooking.com/storage/v1/object/public/add-listing//add_listing_thunder.svg"
                            alt="Réservation Sécurisée"
                            width={64}
                            height={64}
                            className="object-contain"
                          />
                        </div>
                      </div>
                      <Dialog.Title as="h2" className="text-xl font-bold text-[#1A1A1A] mb-2">
                        Réservation Sécurisée
                      </Dialog.Title>
                      <div className="w-16 h-1 bg-gray-200 mx-auto mb-4"></div>
                    </div>
                  </motion.div>
                  <motion.div variants={modalElementVariants}> 
                    <div className="bg-gray-50 rounded border border-gray-200 p-6 mb-6">
                      <h3 className="text-lg font-semibold text-[#1A1A1A] mb-4">Information sur la Réservation Sécurisée</h3>
                      <div className="space-y-4">
                        <p className="text-neutral-500 dark:text-neutral-400">
                          Avec la Réservation Sécurisée, vous bénéficiez de :
                        </p>
                        <ul className="list-disc pl-5 text-gray-600 space-y-2">
                          <li>Réservations instantanées sans délai d&apos;attente</li>
                          <li>Paiements sécurisés traités automatiquement</li>
                          <li>Une commission de seulement 5% sur chaque réservation confirmée</li>
                          <li>Pas d&apos;abonnement mensuel à payer</li>
                          <li>Support client prioritaire</li>
                        </ul>
                        <p className="text-gray-700">
                          Une fois votre annonce publiée, les voyageurs pourront réserver instantanément et vous recevrez votre paiement directement après leur séjour.
                        </p>
                        {hasPaymentMethod && (
                          <div className="mt-4 p-4 bg-green-50 rounded border border-green-100">
                            <div className="flex items-start">
                              <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-green-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586l-1.72-1.72a.75.75 0 00-1.414 1.414L7.586 12l-1.72 1.72a.75.75 0 101.414 1.414L10 13.414l1.72 1.72a.75.75 0 001.414-1.414L10.414 12z" clipRule="evenodd" />
                                </svg>
                              </div>
                              <div className="ml-3">
                                <p className="text-sm font-medium text-green-800">
                                  Vous avez déjà configuré une méthode de paiement
                                </p>
                                <p className="mt-1 text-sm text-green-700">
                                  Vos paramètres sont correctement enregistrés. Vous pouvez modifier votre méthode de paiement à tout moment.
                                </p>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                  <motion.div variants={modalElementVariants}> 
                    <div className="flex justify-center">
                      <button
                        type="button"
                        className="inline-flex justify-center rounded border border-transparent bg-orange-500 px-6 py-3 text-base font-medium text-white hover:bg-orange-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2"
                        onClick={() => {
                          setIsPaymentModalOpen(true);
                          onClose();
                        }}
                        disabled={false}
                      >
                        {hasPaymentMethod ? 'Modifier mes paramètres de paiement' : 'Configurer mes paiements'}
                      </button>
                    </div>
                  </motion.div>
                </motion.div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default SecureReservationModal; 