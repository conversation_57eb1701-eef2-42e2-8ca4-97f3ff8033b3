'use client';
import React, { useState, useEffect, forwardRef } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { Checkbox } from '@radix-ui/react-checkbox';
import { RadioGroup, RadioGroupItem } from '@radix-ui/react-radio-group';
import { XMarkIcon, ChevronDownIcon, CheckIcon } from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';
import { periodOptions } from '../shared/filterOptions';

const CustomRadio = forwardRef<HTMLSpanElement, { checked: boolean }>(
    ({ checked }, ref) => (
        <span
            ref={ref}
            className={`w-[18px] h-[18px] flex items-center justify-center rounded-full transition-colors
                ${checked ? 'bg-[#EA5911]' : 'bg-gray-200'}`}
        >
            {checked && <span className="w-3 h-3 bg-[#EA5911] rounded-full block" />}
        </span>
    )
);
CustomRadio.displayName = 'CustomRadio';

const CustomCheckbox = forwardRef<HTMLSpanElement, { checked: boolean }>(
    ({ checked }, ref) => (
        <span
            ref={ref}
            className={`w-[20px] h-[20px] flex items-center justify-center rounded-[6px] border transition-colors
                ${checked ? 'bg-[#EA5911] border-[#EA5911]' : 'bg-white border-gray-300'}`}
        >
            {checked && <CheckIcon className="w-4 h-4 text-white" />}
        </span>
    )
);
CustomCheckbox.displayName = 'CustomCheckbox';

interface FiltersBarProps {
    selectedProperties: string[];
    selectedPeriod: string;
    onPropertyChange: (propertyId: string, checked: boolean) => void;
    onPeriodChange: (period: string) => void;
    statusOptions?: { value: string; label: string }[];
    selectedStatuses?: string[];
    onStatusChange?: (status: string, checked: boolean) => void;
}

const FiltersBar: React.FC<FiltersBarProps> = ({
    selectedProperties,
    selectedPeriod,
    onPropertyChange,
    onPeriodChange,
    statusOptions,
    selectedStatuses = [],
    onStatusChange
}) => {
    const [listings, setListings] = useState<{ id: string; title: string }[]>([]);
    const [isLoadingListings, setIsLoadingListings] = useState(true);
    const [listingsError, setListingsError] = useState<string | null>(null);
    const [propertyOpen, setPropertyOpen] = useState(false);
    const [periodOpen, setPeriodOpen] = useState(false);
    const [statusOpen, setStatusOpen] = useState(false);

    // Fetch listings on mount
    useEffect(() => {
        setIsLoadingListings(true);
        setListingsError(null);
        fetch('/api/financials/host-listings')
            .then(res => {
                if (!res.ok) throw new Error('Failed to fetch listings');
                return res.json();
            })
            .then(data => {
                setListings(Array.isArray(data) ? data : []);
            })
            .catch(err => {
                setListingsError('Erreur lors du chargement des annonces');
                setListings([]);
            })
            .finally(() => setIsLoadingListings(false));
    }, []);

    // When property modal opens, clear selection
    const handlePropertyOpenChange = (open: boolean) => {
        setPropertyOpen(open);
    };

    // Status filter modal open/close
    const handleStatusOpenChange = (open: boolean) => {
        setStatusOpen(open);
    };

    return (
        <div className="flex gap-3 w-full mb-4">
            {/* Property Filter Button */}
            <Dialog.Root open={propertyOpen} onOpenChange={handlePropertyOpenChange}>
                <Dialog.Trigger asChild>
                    <button
                        className="flex items-center gap-2 px-4 py-2 rounded-full border border-[#E8EAED] bg-transparent text-sm font-semibold text-black focus:outline-none"
                        type="button"
                    >
                        Propriété
                        <ChevronDownIcon className="w-4 h-4" />
                    </button>
                </Dialog.Trigger>
                <AnimatePresence>
                    {propertyOpen && (
                        <Dialog.Portal forceMount>
                            <Dialog.Overlay asChild>
                                <motion.div
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    exit={{ opacity: 0 }}
                                    transition={{ duration: 0.15 }}
                                    className="fixed inset-0 bg-black/30 z-40 backdrop-blur-[3px]"
                                />
                            </Dialog.Overlay>
                            <Dialog.Content asChild>
                                <motion.div
                                    initial={{ y: '100%' }}
                                    animate={{ y: 0 }}
                                    exit={{ y: '100%' }}
                                    transition={{ type: 'spring', stiffness: 400, damping: 32 }}
                                    className="fixed bottom-0 left-0 right-0 z-50 bg-white rounded-t-2xl border-t border-[#E8EAED] p-0 max-h-[70vh] flex flex-col"
                                    style={{ boxShadow: '0 -2px 16px rgba(0,0,0,0.08)' }}
                                >
                                    <div className="relative flex items-center justify-center py-4 border-b border-[#E8EAED]">
                                        <Dialog.Title className="text-center font-semibold text-[20px] text-black w-full">Filter by Annonces</Dialog.Title>
                                        <Dialog.Description className="sr-only">
                                            Select one or more properties to filter your financial data.
                                        </Dialog.Description>
                                        <Dialog.Close asChild>
                                            <button className="absolute right-4 top-1/2 -translate-y-1/2 w-[27px] h-[27px] rounded-full border border-black flex items-center justify-center p-0 bg-white">
                                                <XMarkIcon className="w-4 h-4 text-black" strokeWidth={2} />
                                            </button>
                                        </Dialog.Close>
                                    </div>
                                    <div className="flex flex-col space-y-4 px-2 py-2 mt-7">
                                        {isLoadingListings ? (
                                            <div className="text-center text-gray-500 py-4">Chargement des annonces...</div>
                                        ) : listingsError ? (
                                            <div className="text-center text-red-500 py-4">{listingsError}</div>
                                        ) : listings.length === 0 ? (
                                            <div className="text-center text-gray-400 py-4">Aucune annonce trouvée</div>
                                        ) : (
                                            listings.map((option) => (
                                                <motion.label
                                                    key={option.id}
                                                    className="relative flex items-center justify-between cursor-pointer rounded-[8px] overflow-hidden select-none py-2 px-3"
                                                    whileTap={{ backgroundColor: '#F3F4F6' }}
                                                >
                                                    <span
                                                        className="text-[14px] font-medium text-black overflow-hidden text-ellipsis whitespace-nowrap max-w-[70vw]"
                                                        title={option.title}
                                                    >
                                                        {option.title}
                                                    </span>
                                                    <div className="flex items-center justify-center">
                                                        <Checkbox
                                                            id={`checkbox-${option.id}`}
                                                            checked={selectedProperties.includes(option.id)}
                                                            onCheckedChange={(checked) => {
                                                                onPropertyChange(option.id, checked as boolean);
                                                            }}
                                                            className="sr-only"
                                                        />
                                                        <div
                                                            className={`w-[20px] h-[20px] flex items-center justify-center rounded-[6px] border transition-colors
                                                                ${selectedProperties.includes(option.id) ? 'bg-[#EA5911] border-[#EA5911]' : 'bg-white border-gray-300'}`}
                                                        >
                                                            {selectedProperties.includes(option.id) && <CheckIcon className="w-4 h-4 text-white" />}
                                                        </div>
                                                    </div>
                                                </motion.label>
                                            ))
                                        )}
                                    </div>
                                    <div className="border-t border-[#E8EAED] mt-6 px-4 py-3 flex justify-end">
                                        <button
                                            className="bg-[#EA5911] text-white rounded-[8px] text-[14px] font-semibold px-4 py-[9px]"
                                            onClick={() => setPropertyOpen(false)}
                                        >
                                            Appliquer
                                        </button>
                                    </div>
                                </motion.div>
                            </Dialog.Content>
                        </Dialog.Portal>
                    )}
                </AnimatePresence>
            </Dialog.Root>

            {/* Period Filter Button */}
            <Dialog.Root open={periodOpen} onOpenChange={setPeriodOpen}>
                <Dialog.Trigger asChild>
                    <button
                        className="flex items-center gap-2 px-4 py-2 rounded-full border border-[#E8EAED] bg-transparent text-sm font-semibold text-black focus:outline-none"
                        type="button"
                    >
                        Période
                        <ChevronDownIcon className="w-4 h-4" />
                    </button>
                </Dialog.Trigger>
                <AnimatePresence>
                    {periodOpen && (
                        <Dialog.Portal forceMount>
                            <Dialog.Overlay asChild>
                                <motion.div
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    exit={{ opacity: 0 }}
                                    transition={{ duration: 0.15 }}
                                    className="fixed inset-0 bg-black/30 z-40 backdrop-blur-[3px]"
                                />
                            </Dialog.Overlay>
                            <Dialog.Content asChild>
                                <motion.div
                                    initial={{ y: '100%' }}
                                    animate={{ y: 0 }}
                                    exit={{ y: '100%' }}
                                    transition={{ type: 'spring', stiffness: 400, damping: 32 }}
                                    className="fixed bottom-0 left-0 right-0 z-50 bg-white rounded-t-2xl border-t border-[#E8EAED] p-0 max-h-[70vh] flex flex-col"
                                    style={{ boxShadow: '0 -2px 16px rgba(0,0,0,0.08)' }}
                                >
                                    <div className="relative flex items-center justify-center py-4 border-b border-[#E8EAED]">
                                        <Dialog.Title className="text-center font-semibold text-[20px] text-black w-full">Filter by date</Dialog.Title>
                                        <Dialog.Description className="sr-only">
                                            Select a date range to filter your financial data.
                                        </Dialog.Description>
                                        <Dialog.Close asChild>
                                            <button className="absolute right-4 top-1/2 -translate-y-1/2 w-[27px] h-[27px] rounded-full border border-black flex items-center justify-center p-0 bg-white">
                                                <XMarkIcon className="w-4 h-4 text-black" strokeWidth={2} />
                                            </button>
                                        </Dialog.Close>
                                    </div>
                                    <RadioGroup
                                        value={selectedPeriod}
                                        onValueChange={onPeriodChange}
                                        className="flex flex-col space-y-4 px-2 py-2 mt-7"
                                    >
                                        {periodOptions.map((option) => (
                                            <motion.label
                                                key={option.value}
                                                className="relative flex items-center justify-between cursor-pointer rounded-[8px] overflow-hidden select-none py-2 px-3"
                                                whileTap={{ backgroundColor: '#F3F4F6' }}
                                                onClick={() => onPeriodChange(option.value)}
                                            >
                                                <span className="text-[14px] font-medium text-black">{option.label}</span>
                                                <RadioGroupItem
                                                    value={option.value}
                                                    className="appearance-none"
                                                    asChild
                                                >
                                                    <CustomRadio checked={selectedPeriod === option.value} />
                                                </RadioGroupItem>
                                            </motion.label>
                                        ))}
                                    </RadioGroup>
                                    <div className="border-t border-[#E8EAED] mt-6 px-4 py-3 flex justify-end">
                                        <button
                                            className="bg-[#EA5911] text-white rounded-[8px] text-[14px] font-semibold px-4 py-[9px]"
                                            onClick={() => setPeriodOpen(false)}
                                        >
                                            Appliquer
                                        </button>
                                    </div>
                                </motion.div>
                            </Dialog.Content>
                        </Dialog.Portal>
                    )}
                </AnimatePresence>
            </Dialog.Root>

            {/* Status Filter Button (if options provided) */}
            {statusOptions && statusOptions.length > 0 && (
                <Dialog.Root open={statusOpen} onOpenChange={handleStatusOpenChange}>
                    <Dialog.Trigger asChild>
                        <button
                            className="flex items-center gap-2 px-4 py-2 rounded-full border border-[#E8EAED] bg-transparent text-sm font-semibold text-black focus:outline-none"
                            type="button"
                        >
                            Statut
                            <ChevronDownIcon className="w-4 h-4" />
                        </button>
                    </Dialog.Trigger>
                    <AnimatePresence>
                        {statusOpen && (
                            <Dialog.Portal forceMount>
                                <Dialog.Overlay asChild>
                                    <motion.div
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        exit={{ opacity: 0 }}
                                        transition={{ duration: 0.15 }}
                                        className="fixed inset-0 bg-black/30 z-40 backdrop-blur-[3px]"
                                    />
                                </Dialog.Overlay>
                                <Dialog.Content asChild>
                                    <motion.div
                                        initial={{ y: '100%' }}
                                        animate={{ y: 0 }}
                                        exit={{ y: '100%' }}
                                        transition={{ type: 'spring', stiffness: 400, damping: 32 }}
                                        className="fixed bottom-0 left-0 right-0 z-50 bg-white rounded-t-2xl border-t border-[#E8EAED] p-0 max-h-[70vh] flex flex-col"
                                        style={{ boxShadow: '0 -2px 16px rgba(0,0,0,0.08)' }}
                                    >
                                        <div className="relative flex items-center justify-center py-4 border-b border-[#E8EAED]">
                                            <Dialog.Title className="text-center font-semibold text-[20px] text-black w-full">Filtrer par statut</Dialog.Title>
                                            <Dialog.Description className="sr-only">
                                                Sélectionnez un ou plusieurs statuts pour filtrer vos transactions.
                                            </Dialog.Description>
                                            <Dialog.Close asChild>
                                                <button className="absolute right-4 top-1/2 -translate-y-1/2 w-[27px] h-[27px] rounded-full border border-black flex items-center justify-center p-0 bg-white">
                                                    <XMarkIcon className="w-4 h-4 text-black" strokeWidth={2} />
                                                </button>
                                            </Dialog.Close>
                                        </div>
                                        <div className="flex flex-col space-y-4 px-2 py-2 mt-7">
                                            {statusOptions.map((option) => (
                                                <motion.label
                                                    key={option.value}
                                                    className="relative flex items-center justify-between cursor-pointer rounded-[8px] overflow-hidden select-none py-2 px-3"
                                                    whileTap={{ backgroundColor: '#F3F4F6' }}
                                                >
                                                    <span className="text-[14px] font-medium text-black overflow-hidden text-ellipsis whitespace-nowrap max-w-[70vw]" title={option.label}>
                                                        {option.label}
                                                    </span>
                                                    <div className="flex items-center justify-center">
                                                        <Checkbox
                                                            id={`status-checkbox-${option.value}`}
                                                            checked={selectedStatuses.includes(option.value)}
                                                            onCheckedChange={(checked) => {
                                                                if (onStatusChange) onStatusChange(option.value, checked as boolean);
                                                            }}
                                                            className="sr-only"
                                                        />
                                                        <div
                                                            className={`w-[20px] h-[20px] flex items-center justify-center rounded-[6px] border transition-colors
                                                                ${selectedStatuses.includes(option.value) ? 'bg-[#EA5911] border-[#EA5911]' : 'bg-white border-gray-300'}`}
                                                        >
                                                            {selectedStatuses.includes(option.value) && <CheckIcon className="w-4 h-4 text-white" />}
                                                        </div>
                                                    </div>
                                                </motion.label>
                                            ))}
                                        </div>
                                        <div className="border-t border-[#E8EAED] mt-6 px-4 py-3 flex justify-end">
                                            <button
                                                className="bg-[#EA5911] text-white rounded-[8px] text-[14px] font-semibold px-4 py-[9px]"
                                                onClick={() => setStatusOpen(false)}
                                            >
                                                Appliquer
                                            </button>
                                        </div>
                                    </motion.div>
                                </Dialog.Content>
                            </Dialog.Portal>
                        )}
                    </AnimatePresence>
                </Dialog.Root>
            )}
        </div>
    );
};

export default FiltersBar; 