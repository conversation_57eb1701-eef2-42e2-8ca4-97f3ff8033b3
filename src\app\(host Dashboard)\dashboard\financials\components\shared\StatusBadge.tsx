'use client';
import React from 'react';

interface StatusBadgeProps {
    status: string;
    size?: 'small' | 'normal';
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, size = 'normal' }) => {
    let backgroundColor = '';
    let textColor = '';
    let borderColor = '';
    let label = status;

    // Define styles based on status
    switch (status.toLowerCase()) {
        // "Paiement prévu" statuses
        case 'confirmer':
            backgroundColor = '#E6F0FF';
            textColor = '#3787FF';
            borderColor = '#365DF5';
            break;
        case 'en attente':
            backgroundColor = '#FFF8E6';
            textColor = '#F0AD00';
            borderColor = '#DEC06B';
            break;
        case 'en cours':
            backgroundColor = '#FFF8E6';  // Light yellow
            textColor = '#F0AD00';        // Yellow/gold
            borderColor = '#E2E1A2';
            break;
        case 'remboursé':
            backgroundColor = '#E6F0FF';
            textColor = '#3787FF';        // Gray
            borderColor = '#365DF5';
            break;

        // "Payé" statuses
        case 'disponible':
            backgroundColor = '#E6F0FF';  // Light blue
            textColor = '#3787FF';        // Blue
            borderColor = '#365DF5';
            break;
        case 'effectué':
            backgroundColor = '#E6F0FF';  // Light blue
            textColor = '#3787FF';        // Blue
            borderColor = '#365DF5';
            break;
        case 'refusé':
            backgroundColor = '#FFE6E6';  // Light red
            textColor = '#FF3737';        // Red
            borderColor = '#FF000080';
            break;

        // Default/fallback
        case 'payé':
            backgroundColor = '#E6FFE9';  // Light green
            textColor = '#2E9C41';        // Green
            borderColor = '#2E9C41';      // Green
            break;
        default:
            backgroundColor = '#EEEEF1';
            textColor = '#6C757E';
            borderColor = '#6C757E';
    }

    // Determine size-specific classes
    const sizeClasses = size === 'small'
        ? "px-1.5 py-0 text-[10px] font-medium"
        : "px-2 py-1 text-xs font-medium";

    return (
        <div
            className={`inline-flex justify-center items-center rounded-full border ${sizeClasses}`}
            style={{
                backgroundColor,
                color: textColor,
                borderColor
            }}
        >
            {label}
        </div>
    );
};

export default StatusBadge; 