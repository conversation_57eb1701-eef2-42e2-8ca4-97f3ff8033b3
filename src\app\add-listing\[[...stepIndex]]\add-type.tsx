"use client";

import React, { useEffect, useState, useRef } from "react";
import CommonLayout from "./CommonLayout";
import Image from "next/image";
import { motion } from "framer-motion";
import { useFormContext } from "../FormContext";

const PlaceholderIcon = () => (
    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#A3A3A3" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="3" y="10" width="18" height="11" rx="2" /><path d="M7 21V7a5 5 0 0 1 10 0v14" /></svg>
);

const PageAddType = () => {
    const [types, setTypes] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [roomTypes, setRoomTypes] = useState<any[]>([]);
    const [roomTypesLoading, setRoomTypesLoading] = useState(true);
    const [roomTypesError, setRoomTypesError] = useState<string | null>(null);
    const { formData, setFormData, formErrors, setFormErrors, validateStep } = useFormContext();

    // Ref for the second section to scroll to
    const secondSectionRef = useRef<HTMLDivElement>(null);

    // Function to scroll to the second section
    const scrollToSecondSection = () => {
        if (secondSectionRef.current) {
            // Adjust offset based on screen size - less offset on mobile for better UX
            const isMobile = window.innerWidth < 768;
            const yOffset = isMobile ? -50 : -100; // Less offset on mobile
            const element = secondSectionRef.current;
            const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset;

            window.scrollTo({
                top: y,
                behavior: 'smooth'
            });
        }
    };

    useEffect(() => {
        fetch("/api/listing/types")
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    setTypes(data.types);
                } else {
                    setError("Failed to fetch types");
                }
                setLoading(false);
            })
            .catch(err => {
                setError("Error fetching types");
                setLoading(false);
            });
    }, []);

    useEffect(() => {
        fetch("/api/listing/room-types")
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    setRoomTypes(data.room_types);
                } else {
                    setRoomTypesError("Failed to fetch room types");
                }
                setRoomTypesLoading(false);
            })
            .catch(err => {
                setRoomTypesError("Error fetching room types");
                setRoomTypesLoading(false);
            });
    }, []);

    // Validate on mount if values exist (e.g., from draft)
    useEffect(() => {
        if (formData.propertyTypeId || formData.roomTypeId) {
            validateStep("add-type");
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // useEffect to validate when propertyTypeId or roomTypeId changes
    useEffect(() => {
        if (formData.propertyTypeId || formData.roomTypeId) {
            validateStep("add-type");
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [formData.propertyTypeId, formData.roomTypeId]);

    const handleSelectType = (typeId: string) => {
        setFormData(prev => ({ ...prev, propertyTypeId: typeId }));
        setFormErrors({});

        // Scroll to the second section after a short delay to let the selection animation complete
        setTimeout(() => {
            scrollToSecondSection();
        }, 300);
    };
    const handleSelectRoomType = (roomTypeId: string) => {
        setFormData(prev => ({ ...prev, roomTypeId }));
        setFormErrors({});
    };

    return (
        <CommonLayout params={{ stepIndex: "add-type" }}>
            <div className="flex flex-col min-h-[70vh] !pb-[80px] md:pb-0 hide-scrollbar md:pt-12 2xl:pt-24 sm:items-center justify-center  md:py-0">
                {loading && <p>Chargement...</p>}
                {error && <p className="text-red-500">{error}</p>}
                {!loading && !error && (
                    <>
                        <h2 className="font-bold text-gray-900 text-2xl md:text-3xl text-left mb-10">
                            Parmi ces catégories, laquelle correspond le mieux à votre logement ?
                        </h2>
                        <motion.div
                            className="grid grid-cols-1 md:grid-cols-3 gap-x-4 gap-y-6 w-full px-4 md:gap-8 md:px-0 md:max-w-2xl md:mx-auto"
                            initial="hidden"
                            animate="visible"
                            variants={{
                                visible: { transition: { staggerChildren: 0.08 } },
                                hidden: {},
                            }}
                        >
                            {types.map((type: any) => (
                                <motion.button
                                    key={type.id}
                                    type="button"
                                    aria-pressed={formData.propertyTypeId === type.id}
                                    onClick={() => handleSelectType(type.id)}
                                    className={`border rounded-lg p-4 !py-3 flex flex-col items-start justify-center transition w-full
                                        ${formData.propertyTypeId === type.id
                                            ? 'border-[#EA580F]'
                                            : 'border-gray-200'}
                                        hover:border-orange-400 focus:border-orange-500`
                                    }
                                    style={formData.propertyTypeId === type.id ? { backgroundColor: '#EA580E26' } : undefined}
                                    variants={{
                                        hidden: { opacity: 0, y: 60 },
                                        visible: { opacity: 1, y: 0, transition: { duration: 0.08, ease: "circOut" } },
                                    }}
                                >
                                    <div className="mb-2">
                                        {type.icons ? (
                                            <Image src={type.icons} alt={type.name} width={32} height={32} unoptimized />
                                        ) : (
                                            <PlaceholderIcon />
                                        )}
                                    </div>
                                    <span className="font-medium text-gray-900 text-base">{type.name}</span>
                                </motion.button>
                            ))}
                        </motion.div>
                        {formErrors.propertyTypeId && (
                            <div
                                className="mt-6 flex items-center justify-center"
                                role="alert"
                                aria-live="assertive"
                            >
                                <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full max-w-md">
                                    <svg
                                        className="h-5 w-5 text-red-500 mr-2 flex-shrink-0"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                        aria-hidden="true"
                                    >
                                        <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth="2"
                                            d="M12 8v4m0 4h.01"
                                        />
                                    </svg>
                                    <span className="text-sm text-red-700 font-medium">{formErrors.propertyTypeId}</span>
                                </div>
                            </div>
                        )}
                        <div ref={secondSectionRef}>
                            <h2 className="font-bold text-gray-900 text-2xl md:text-3xl text-left mt-12 mb-8">
                                Que proposez-vous : un logement entier ou  une chambre privée
                            </h2>
                        </div>
                        <motion.div
                            className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-6 w-full px-4 md:px-0 md:max-w-2xl md:mx-auto"
                            initial="hidden"
                            animate="visible"
                            variants={{
                                visible: { transition: { staggerChildren: 0.08 } },
                                hidden: {},
                            }}
                        >
                            {roomTypes.map((room: any) => (
                                <motion.button
                                    key={room.id}
                                    type="button"
                                    aria-pressed={formData.roomTypeId === room.id}
                                    onClick={() => handleSelectRoomType(room.id)}
                                    className={`border rounded-lg flex flex-row items-center justify-between transition w-full max-w-xl mx-auto px-3 !py-4 md:py-6
                                        ${formData.roomTypeId === room.id
                                            ? 'border-[#EA580F] bg-[#EA580E26]'
                                            : 'border-gray-200'}
                                        hover:border-orange-400 focus:border-orange-500`}
                                    style={{ minHeight: 96 }}
                                    variants={{
                                        hidden: { opacity: 0, y: 60 },
                                        visible: { opacity: 1, y: 0, transition: { duration: 0.08, ease: "circOut" } },
                                    }}
                                >
                                    <div className="flex flex-col items-start text-left">
                                        <span className="font-bold text-gray-900 text-base mb-1">{room.name}</span>
                                        <span className="text-gray-500 text-sm leading-tight">
                                            {room.name.toLowerCase().includes('chambre')
                                                ? "Les voyageurs disposent d'une chambre privée dans votre logement."
                                                : "Les voyageurs disposent du logement dans son intégralité ."}
                                        </span>
                                    </div>
                                    <div className="ml-4 flex-shrink-0 flex items-center justify-center">
                                        {room.icons ? (
                                            <Image src={room.icons} alt={room.name} width={32} height={32} unoptimized />
                                        ) : (
                                            <PlaceholderIcon />
                                        )}
                                    </div>
                                </motion.button>
                            ))}
                        </motion.div>
                        {formErrors.roomTypeId && (
                            <div
                                className="mt-6 flex items-center justify-center"
                                role="alert"
                                aria-live="assertive"
                            >
                                <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full max-w-md">
                                    <svg
                                        className="h-5 w-5 text-red-500 mr-2 flex-shrink-0"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                        aria-hidden="true"
                                    >
                                        <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth="2"
                                            d="M12 8v4m0 4h.01"
                                        />
                                    </svg>
                                    <span className="text-sm text-red-700 font-medium">{formErrors.roomTypeId}</span>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </div>
        </CommonLayout>
    );
};

export default PageAddType;