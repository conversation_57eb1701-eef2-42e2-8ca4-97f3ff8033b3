'use client';

import React from 'react';
import Image from 'next/image';
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON>itle,
    DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { formatDate, formatCurrency } from '../utils/formatters';
import { Download, CheckCircle } from "lucide-react";

// Invoice type to match the data we receive
type Invoice = {
    id: string;
    invoice_number: string;
    start_date: string;
    end_date: string;
    subscription_plan: string;
    payment_amount: number;
    listings: {
        id: string;
        title: string;
    };
    profile?: {
        fullname: string;
        phone_number: string;
        email: string;
    };
};

type InvoiceDetailModalProps = {
    invoice: Invoice | null;
    isOpen: boolean;
    onClose: () => void;
    onDownload: (invoice: Invoice) => void;
};

const InvoiceDetailModal = ({
    invoice,
    isOpen,
    onClose,
    onDownload
}: InvoiceDetailModalProps) => {
    if (!invoice) return null;

    // Custom currency formatter to ensure price and currency are on the same line
    const formatInvoiceCurrency = (amount: number): React.ReactNode => {
        return (
            <span className="whitespace-nowrap">{amount.toFixed(2)} TND</span>
        );
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[680px] p-0 overflow-auto max-h-[90vh]">
                <div className="p-6 sm:p-8">
                    {/* Header with title and logo */}
                    <div className="flex justify-between items-center mb-8">
                        <h2 className="text-2xl font-bold text-primary">Facture</h2>
                        <div className="w-32 h-12 relative">
                            <Image
                                src="https://api.almindharbooking.com/storage/v1/object/public/facturation//AlmindharBooking_Logo.svg"
                                alt="Almindhar Booking Logo"
                                className="object-contain"
                                width={128}
                                height={48}
                            />
                        </div>
                    </div>

                    {/* Company and Invoice Details - Top Section */}
                    <div className="flex flex-col sm:flex-row justify-between mb-8 gap-6">
                        <div className="space-y-1">
                            <h3 className="font-bold text-lg text-primary">Almindhar Booking</h3>
                            <p className="text-sm text-gray-600">Rue 14 Janvier Immo Hadj Kacem 4ème étage</p>
                            <p className="text-sm text-gray-600">Hammam Sousse, Tunisie</p>
                            <p className="text-sm text-gray-600"><EMAIL></p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-md self-start">
                            <p className="text-sm mb-2">
                                <span className="font-semibold text-gray-700">Date d&apos;émission:</span> {formatDate(invoice.start_date)}
                            </p>
                            <p className="text-sm mb-2">
                                <span className="font-semibold text-gray-700">N° Facture:</span> {invoice.invoice_number}
                            </p>
                            <p className="text-sm flex items-center">
                                <span className="font-semibold text-gray-700 mr-2">Statut:</span> 
                                <span className="text-green-600 font-semibold flex items-center">
                                    <CheckCircle size={16} className="mr-1" /> Payé
                                </span>
                            </p>
                        </div>
                    </div>

                    {/* Client Info */}
                    <div className="bg-gray-50 p-5 rounded-md mb-8 border-l-4 border-primary">
                        <h3 className="font-bold mb-3 text-primary">Facturé à:</h3>
                        <p className="font-medium text-base mb-1">{invoice.profile?.fullname || 'Client'}</p>
                        <p className="text-sm text-gray-600 mb-1">{invoice.profile?.email || 'N/A'}</p>
                        <p className="text-sm text-gray-600">{invoice.profile?.phone_number || 'N/A'}</p>
                    </div>

                    {/* Invoice Details */}
                    <div className="mb-8">
                        <h3 className="font-bold mb-3 text-primary">Détails:</h3>
                        <div className="border rounded-md overflow-hidden shadow-sm">
                            <table className="w-full">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Période</th>
                                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Montant</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr className="border-t">
                                        <td className="px-4 py-4 text-sm">
                                            <span className="font-medium">Hébergement:</span> 
                                            <div className="inline-block max-w-[250px] break-words mt-1">
                                                {invoice.listings?.title || 'N/A'}
                                            </div>
                                        </td>
                                        <td className="px-4 py-4 text-sm">
                                            <div className="text-gray-600 whitespace-nowrap">
                                                {formatDate(invoice.start_date)}
                                            </div>
                                            <div className="text-gray-600 whitespace-nowrap">
                                                - {formatDate(invoice.end_date)}
                                            </div>
                                        </td>
                                        <td className="px-4 py-4 text-sm font-medium">
                                            {invoice.subscription_plan}
                                        </td>
                                        <td className="px-4 py-4 text-sm text-right font-medium whitespace-nowrap">
                                            {formatInvoiceCurrency(invoice.payment_amount)}
                                        </td>
                                    </tr>
                                </tbody>
                                <tfoot className="bg-gray-50">
                                    <tr className="border-t border-gray-200">
                                        <td colSpan={3} className="px-4 py-4 text-sm font-bold text-left">Total:</td>
                                        <td className="px-4 py-4 text-right font-bold text-lg text-primary whitespace-nowrap">
                                            {formatInvoiceCurrency(invoice.payment_amount)}
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>

                    {/* Payment Info */}
                    <div className="mb-8 bg-gray-50 p-5 rounded-md">
                        <h3 className="font-bold mb-3 text-primary">Informations de paiement:</h3>
                        <p className="text-sm mb-1">Cette facture est marquée comme payée.</p>
                        <p className="text-sm font-medium">Méthode de paiement: <span className="text-primary">Paiement en ligne</span></p>
                    </div>

                    {/* Note */}
                    <div className="text-sm text-gray-600 mb-8 p-4 border border-gray-100 rounded-md bg-gray-50/50 italic">
                        <p>Merci pour votre confiance. Pour toute question concernant cette facture, veuillez nous contacter à <span className="text-primary not-italic font-medium"><EMAIL></span>.</p>
                    </div>

                    {/* Download button */}
                    <div className="w-full">
                        <Button
                            onClick={() => onDownload(invoice)}
                            className="w-full py-6 text-base"
                            variant="default"
                        >
                            <Download size={18} className="mr-2" />
                            Télécharger PDF
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default InvoiceDetailModal;