import { createClient } from "@/utils/supabase/server";

export type AuditLogEntry = {
  id: number;
  table_name: string;
  record_id: string;
  operation: 'INSERT' | 'UPDATE' | 'DELETE';
  old_data: any;
  new_data: any;
  changed_fields: Record<string, { old: any; new: any }>;
  user_id: string | null;
  timestamp: string;
  // Additional data for UI display
  entity_name?: string;
  entity_data?: any;
  user_data?: any;
};

export async function getRecentAuditLogs(limit = 10, userId?: string) {
  const supabase = await createClient();
  
  // List of tables we're interested in
  const relevantTables = [
    'listings',
    'listing_pricing',
    'listing_cancellation_policy',
    'listing_amenities',
    'listing_house_rules',
    'bookings',
    'profiles'
  ];
  
  // First get the audit logs for relevant tables
  let query = supabase
    .from("audit_logs")
    .select("*")
    .in("table_name", relevantTables)
    .order("timestamp", { ascending: false });
  
  // Filter by user if provided  
  if (userId) {
    query = query.or(`user_id.eq.${userId}`);
  }
  
  const { data: logs, error } = await query.limit(limit);
  
  if (error) {
    console.error("Error fetching audit logs:", error);
    return [];
  }
  
  // Enrich logs with additional data
  const enrichedLogs: AuditLogEntry[] = [];
  
  for (const log of logs) {
    const enrichedLog = { ...log } as AuditLogEntry;
    
    // Fetch entity data based on table_name
    if (['listings', 'listing_pricing', 'listing_cancellation_policy', 'listing_amenities', 'listing_house_rules'].includes(log.table_name)) {
      let listingId = log.record_id;
      
      // For related tables, get the listing_id from the data
      if (log.table_name !== 'listings') {
        listingId = log.new_data?.listing_id || log.old_data?.listing_id;
      }
      
      if (listingId) {
        const { data: listing } = await supabase
          .from("listings")
          .select("id, title, status")
          .eq("id", listingId)
          .single();
        
        if (listing) {
          enrichedLog.entity_name = listing.title;
          enrichedLog.entity_data = listing;
        }
      }
    } else if (log.table_name === 'bookings') {
      // For bookings, get both the booking and listing info
      const { data: booking } = await supabase
        .from("bookings")
        .select(`
          id, 
          status,
          listing_id,
          listings:listings(id, title)
        `)
        .eq("id", log.record_id)
        .single();
      
      if (booking && booking.listings) {
        // Access the listing title with proper type handling
        let listingTitle = '';
        
        if (Array.isArray(booking.listings) && booking.listings.length > 0) {
          listingTitle = booking.listings[0]?.title;
        } else if (typeof booking.listings === 'object' && 'title' in booking.listings) {
          listingTitle = (booking.listings as { title: string }).title;
        }
        
        enrichedLog.entity_name = listingTitle || `Réservation #${log.record_id}`;
        enrichedLog.entity_data = booking;
      }
    } else if (log.table_name === 'profiles') {
      // For profiles, get user info
      const { data: profile } = await supabase
        .from("profiles")
        .select("id, fullname")
        .eq("id", log.record_id)
        .single();
      
      if (profile) {
        enrichedLog.entity_name = profile.fullname;
        enrichedLog.entity_data = profile;
      }
    }
    
    // Get user who made the change
    if (log.user_id) {
      const { data: user } = await supabase
        .from("profiles")
        .select("id, fullname, avatar_url")
        .eq("id", log.user_id)
        .single();
      
      if (user) {
        enrichedLog.user_data = user;
      }
    }
    
    enrichedLogs.push(enrichedLog);
  }
  
  return enrichedLogs;
} 