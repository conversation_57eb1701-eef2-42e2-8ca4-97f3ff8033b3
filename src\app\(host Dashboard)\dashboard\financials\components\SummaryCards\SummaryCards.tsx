'use client';
import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { FinanceSummary } from '../../types/financeTypes';
import { formatTND } from '../../utils/format';

interface SummaryCardsProps {
  selectedProperties: string[];
  selectedPeriod: string;
}

const SummaryCards: React.FC<SummaryCardsProps> = ({ selectedProperties, selectedPeriod }) => {
  const [summary, setSummary] = useState<FinanceSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    setError(null);
    const params = new URLSearchParams();
    if (selectedProperties.length > 0) params.set('property', selectedProperties.join(','));
    if (selectedPeriod && selectedPeriod !== 'all') params.set('period', selectedPeriod);
    const url = `/api/financials/summary${params.toString() ? `?${params.toString()}` : ''}`;
    fetch(url)
      .then(res => {
        if (!res.ok) throw new Error('Erreur lors du chargement des données');
        return res.json();
      })
      .then(data => setSummary(data))
      .catch(() => setError('Erreur lors du chargement des données'))
      .finally(() => setLoading(false));
  }, [selectedProperties, selectedPeriod]);

  const formatDate = (dateStr: string | null) => {
    if (!dateStr) return '--';
    const date = new Date(dateStr);
    return date.toLocaleDateString('fr-FR', { day: '2-digit', month: 'short', year: 'numeric' });
  };

  return (
    <>
      <div className="flex flex-row gap-1 w-full mt-6">
        {/* Revenus Card */}
        <div className="flex-1 bg-white border border-[#FED8AB] rounded-xl px-2 py-4 flex flex-col min-w-[0]">
          <div className="flex items-center gap-1 justify-start min-w-0">
            <Image src="https://api.almindharbooking.com/storage/v1/object/public/finances//finance_revenue.svg" alt="Revenus" width={28} height={28} className="object-contain" unoptimized />
            <span className="text-[0.6875rem] font-semibold text-black whitespace-nowrap flex items-center h-[28px]">Revenus</span>
          </div>
          <span className="block text-center text-[clamp(1.1rem,2.5vw,1.5rem)] font-bold text-black mt-2 whitespace-nowrap">
            {loading ? '...' : error ? '--' : formatTND(summary?.totalEarnings ?? 0)}
          </span>
        </div>
        {/* Prochain paiement Card */}
        <div className="flex-1 bg-white border border-[#FED8AB] rounded-xl px-2 py-4 flex flex-col min-w-[0]">
          <div className="flex items-center gap-1 justify-start min-w-0">
            <Image src="https://api.almindharbooking.com/storage/v1/object/public/finances//finance_prochaine_payement.svg" alt="Prochain paiement" width={28} height={28} className="object-contain" unoptimized />
            <span className="text-[0.6875rem] font-semibold text-black whitespace-nowrap flex items-center h-[28px]">Prochaine paiement</span>
          </div>
          <span className="block text-center text-[clamp(1.1rem,2.5vw,1.5rem)] font-bold text-black mt-2 whitespace-nowrap">
            {loading ? '...' : error ? '--' : formatDate(summary?.nextPaymentDate ?? null)}
          </span>
        </div>
      </div>
      {/* Revenus summary text block */}
      <div className="mt-6">
        <div className="text-[1.5rem] font-normal text-black">Revenus</div>
        <div className="flex items-baseline gap-2 mt-1">
          <span className="text-[1.5rem] font-bold text-[#EA5911] bg-[#FFFAF5] rounded-full px-4 py-1">
            {loading ? '...' : error ? '--' : formatTND(summary?.totalEarnings ?? 0)}
          </span>
        </div>
        <div className="text-black text-[1.1rem] mt-1">depuis janvier</div>
      </div>
    </>
  );
};

export default SummaryCards; 