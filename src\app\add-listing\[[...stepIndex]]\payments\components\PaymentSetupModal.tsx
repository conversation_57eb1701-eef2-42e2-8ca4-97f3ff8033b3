import React, { Fragment, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import {
  formStepVariants,
  formFieldVariants
} from '../animationVariants';

// Animation variants for mobile modal
const mobileModalVariants = {
  hidden: { y: '100%', opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 500,
      damping: 35,
      mass: 0.8
    }
  },
  exit: {
    y: '100%',
    opacity: 0,
    transition: {
      type: "spring",
      stiffness: 500,
      damping: 35,
      mass: 0.8
    }
  },
};

interface PaymentSetupModalProps {
  isOpen: boolean;
  onClose: () => void;
  paymentStep: 'methods' | 'bankForm' | 'd17Form' | 'success';
  selectedPaymentMethod: string | null;
  setSelectedPaymentMethod: (method: string) => void;
  hasPaymentMethod: boolean;
  accountType: 'courant' | 'epargne';
  setAccountType: (type: 'courant' | 'epargne') => void;
  accountName: string;
  setAccountName: (name: string) => void;
  bankName: string;
  setBankName: (name: string) => void;
  accountNumber: string;
  setAccountNumber: (num: string) => void;
  confirmAccountNumber: string;
  setConfirmAccountNumber: (num: string) => void;
  d17PhoneNumber: string;
  setD17PhoneNumber: (num: string) => void;
  confirmD17PhoneNumber: string;
  setConfirmD17PhoneNumber: (num: string) => void;
  d17Error: string;
  setD17Error: (err: string) => void;
  bankError: string;
  setBankError: (err: string) => void;
  isSaving: boolean;
  handlePaymentMethodSelect: (method: string) => void;
  handleSaveBankAccount: () => void;
  handleSaveD17: () => void;
  handleRIBInput: (value: string, setter: (value: string) => void) => void;
  successMessage: string;
  setPaymentStep: (step: 'methods' | 'bankForm' | 'd17Form' | 'success') => void;
}

const PaymentSetupModal: React.FC<PaymentSetupModalProps> = ({
  isOpen,
  onClose,
  paymentStep,
  selectedPaymentMethod,
  setSelectedPaymentMethod,
  hasPaymentMethod,
  accountType,
  setAccountType,
  accountName,
  setAccountName,
  bankName,
  setBankName,
  accountNumber,
  setAccountNumber,
  confirmAccountNumber,
  setConfirmAccountNumber,
  d17PhoneNumber,
  setD17PhoneNumber,
  confirmD17PhoneNumber,
  setConfirmD17PhoneNumber,
  d17Error,
  setD17Error,
  bankError,
  setBankError,
  isSaving,
  handlePaymentMethodSelect,
  handleSaveBankAccount,
  handleSaveD17,
  handleRIBInput,
  successMessage,
  setPaymentStep
}) => {
  // Disable body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      // Save current scroll position
      const scrollY = window.scrollY;
      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = '100%';
    } else {
      // Restore scroll position
      const scrollY = document.body.style.top;
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
      if (scrollY) {
        window.scrollTo(0, parseInt(scrollY || '0') * -1);
      }
    }

    // Cleanup function to restore scroll when component unmounts
    return () => {
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
    };
  }, [isOpen]);
  return (
    <AnimatePresence>
      {isOpen && (
        <Dialog as="div" className="relative z-50" open={isOpen} onClose={onClose}>
          {/* Custom backdrop */}
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm z-40"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            onClick={onClose}
          />

          {/* Mobile-responsive modal content */}
          <div className="fixed inset-0 z-50 flex items-end sm:items-center sm:justify-center p-0 sm:p-4">
            <motion.div
              variants={mobileModalVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              className="w-full max-w-xl mx-auto bg-white rounded-t-2xl sm:rounded-2xl shadow-xl overflow-hidden sm:relative sm:max-h-[90vh]"
            >
              <div className="p-4 sm:p-6">
                <div className="absolute right-3 top-3">
                  <button
                    type="button"
                    className="text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-200 p-1.5 rounded-full"
                    onClick={onClose}
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>

                <div className="mt-2 max-h-[70vh] sm:max-h-[80vh] overflow-y-auto overflow-x-hidden pb-2">
                  <AnimatePresence mode="wait">
                    {paymentStep === 'methods' && (
                      <motion.div
                        key="methodsStep"
                        className="space-y-6"
                        variants={formStepVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                      >
                        <div className="text-center space-y-4">
                          <Dialog.Title as="h3" className="text-lg sm:text-xl font-semibold leading-6 text-gray-900 mt-2">
                            {hasPaymentMethod ? 'Modifier votre méthode de paiement' : 'Ajoutons une méthode de paiement'}
                          </Dialog.Title>

                          <p className="text-sm text-gray-500">
                            {hasPaymentMethod
                              ? 'Vous pouvez modifier votre méthode de paiement existante ou en choisir une nouvelle.'
                              : 'Pour commencer, dites-nous où vous souhaitez que nous envoyions votre argent'}
                          </p>

                          {hasPaymentMethod && (
                            <div className="p-3 bg-blue-50 rounded border border-blue-100">
                              <p className="text-sm text-blue-700">
                                Vous avez déjà configuré une méthode de paiement. Vous pouvez la modifier ci-dessous ou continuer avec les paramètres existants.
                              </p>
                            </div>
                          )}
                        </div>

                        <div className="space-y-4">
                          <div className="relative">
                            <div className="flex flex-col space-y-4">
                              {/* Bank Account Option */}
                              <div
                                className={`flex items-center space-x-4 rounded border p-3 sm:p-4 cursor-pointer transition-colors ${selectedPaymentMethod === 'bank' ? 'border-orange-500 bg-orange-50' : 'border-gray-200 hover:border-gray-300'}`}
                                onClick={() => handlePaymentMethodSelect('bank')}
                              >
                                <div className="flex items-center space-x-2 sm:space-x-3">
                                  <div className="flex-shrink-0">
                                    <svg className="h-5 w-5 sm:h-6 sm:w-6 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 6l2-2h14l2 2M5 6v14a2 2 0 002 2h10a2 2 0 002-2V6M8 6V4h8v2" />
                                    </svg>
                                  </div>
                                  <div className="flex-grow">
                                    <p className="text-sm font-medium text-gray-900">Compte bancaire</p>
                                    <div className="text-xs text-gray-500 space-y-1">
                                      <p>3 à 5 jours ouvrables</p>
                                      <p>Sans frais</p>
                                    </div>
                                  </div>
                                </div>
                                <div className="flex-shrink-0">
                                  <div className={`h-5 w-5 sm:h-6 sm:w-6 rounded-full border-2 flex items-center justify-center ${
                                    selectedPaymentMethod === 'bank' ? 'border-orange-500 bg-orange-500' : 'border-gray-300'
                                  }`}>
                                    {selectedPaymentMethod === 'bank' && (
                                      <svg className="h-3 w-3 sm:h-4 sm:w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                      </svg>
                                    )}
                                  </div>
                                </div>
                              </div>

                              {/* D17 Option */}
                              <div
                                className={`flex items-center space-x-4 rounded border p-3 sm:p-4 cursor-pointer transition-colors ${selectedPaymentMethod === 'd17' ? 'border-orange-500 bg-orange-50' : 'border-gray-200 hover:border-gray-300'}`}
                                onClick={() => handlePaymentMethodSelect('d17')}
                              >
                                <div className="flex items-center space-x-2 sm:space-x-3">
                                  <div className="flex-shrink-0">
                                    <svg className="h-5 w-5 sm:h-6 sm:w-6 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                  </div>
                                  <div className="flex-grow">
                                    <p className="text-sm font-medium text-gray-900">e-Dinar</p>
                                    <div className="text-xs text-gray-500 space-y-1">
                                      <p>1 jour ouvrable</p>
                                      <p>Frais D17</p>
                                    </div>
                                  </div>
                                </div>
                                <div className="flex-shrink-0">
                                  <div className={`h-5 w-5 sm:h-6 sm:w-6 rounded-full border-2 flex items-center justify-center ${
                                    selectedPaymentMethod === 'd17' ? 'border-orange-500 bg-orange-500' : 'border-gray-300'
                                  }`}>
                                    {selectedPaymentMethod === 'd17' && (
                                      <svg className="h-3 w-3 sm:h-4 sm:w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                      </svg>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="pt-4 flex justify-end border-t border-gray-100">
                          <button
                            type="button"
                            className="inline-flex justify-center rounded border border-transparent bg-orange-500 px-4 py-2 text-sm font-medium text-white hover:bg-orange-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2 transition-colors"
                            onClick={() => {
                              if (selectedPaymentMethod === 'bank') {
                                setPaymentStep('bankForm');
                              } else if (selectedPaymentMethod === 'd17') {
                                setPaymentStep('d17Form');
                              }
                            }}
                          >
                            Suivant
                          </button>
                        </div>
                      </motion.div>
                    )}
                    {paymentStep === 'bankForm' && (
                      <motion.div
                        key="bankFormStep"
                        className="space-y-6"
                        variants={formStepVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                      >
                        <Dialog.Title as="h3" className="text-xl font-semibold leading-6 text-gray-900 mb-3 text-center">
                          Informations du compte bancaire
                        </Dialog.Title>
                        <motion.div variants={formFieldVariants}>
                          <div className="ml-2 space-y-4">
                            <label className="block text-sm font-medium text-gray-700">
                              S&apos;agit-il d&apos;un compte courant ou d&apos;un compte d&apos;épargne ?
                            </label>
                            <div className="flex items-center gap-4 sm:gap-6">
                              <div className="flex items-center">
                                <input
                                  id="courant"
                                  name="accountType"
                                  type="checkbox"
                                  checked={accountType === 'courant'}
                                  onChange={() => setAccountType('courant')}
                                  className="h-5 w-5 text-[#EA580C] border-gray-300 rounded-[2px]"
                                />
                                <label htmlFor="courant" className="ml-2 text-sm font-medium text-gray-700">Courant</label>
                              </div>
                              <div className="flex items-center">
                                <input
                                  id="epargne"
                                  name="accountType"
                                  type="checkbox"
                                  checked={accountType === 'epargne'}
                                  onChange={() => setAccountType('epargne')}
                                  className="h-5 w-5 text-[#EA580C] border-gray-300 rounded-[2px]"
                                />
                                <label htmlFor="epargne" className="ml-2 text-sm font-medium text-gray-700">Épargne</label>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                        <motion.div variants={formFieldVariants}>
                          <div className="space-y-1">
                            <label htmlFor="accountName" className="block text-sm font-medium text-gray-700">
                              Nom du titulaire du compte
                            </label>
                            <input
                              type="text"
                              id="accountName"
                              name="accountName"
                              value={accountName}
                              onChange={(e) => setAccountName(e.target.value)}
                              className={`mt-1 block w-full rounded shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm ${bankError && !accountName ? 'border-red-300' : 'border-gray-300'}`}
                              placeholder="Nom complet tel qu'il apparaît sur votre compte bancaire"
                            />
                          </div>
                        </motion.div>
                        <motion.div variants={formFieldVariants}>
                          <div className="space-y-1">
                            <label htmlFor="bankName" className="block text-sm font-medium text-gray-700">
                              Nom de la banque
                            </label>
                            <input
                              type="text"
                              id="bankName"
                              name="bankName"
                              value={bankName}
                              onChange={(e) => setBankName(e.target.value)}
                              className={`mt-1 block w-full rounded shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm ${bankError && !bankName ? 'border-red-300' : 'border-gray-300'}`}
                              placeholder="Nom de votre banque"
                            />
                          </div>
                        </motion.div>
                        <motion.div variants={formFieldVariants}>
                          <div className="space-y-1">
                            <label htmlFor="accountNumber" className="block text-sm font-medium text-gray-700">
                              RIB (Relevé d&apos;Identité Bancaire)
                            </label>
                            <input
                              type="text"
                              id="accountNumber"
                              name="accountNumber"
                              value={accountNumber}
                              onChange={(e) => handleRIBInput(e.target.value, setAccountNumber)}
                              className={`mt-1 block w-full rounded shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm ${bankError && accountNumber !== confirmAccountNumber ? 'border-red-300' : 'border-gray-300'}`}
                              placeholder="XX XXX XXXXXXXXXXXXX XX"
                            />
                            <p className="mt-1 text-xs text-gray-500">
                              Format: 2 chiffres + 3 chiffres + 13 chiffres + 2 chiffres
                            </p>
                          </div>
                        </motion.div>
                        <motion.div variants={formFieldVariants}>
                          <div className="space-y-1">
                            <label htmlFor="confirmAccountNumber" className="block text-sm font-medium text-gray-700">
                              Confirmer le RIB
                            </label>
                            <input
                              type="text"
                              id="confirmAccountNumber"
                              name="confirmAccountNumber"
                              value={confirmAccountNumber}
                              onChange={(e) => handleRIBInput(e.target.value, setConfirmAccountNumber)}
                              className={`mt-1 block w-full rounded shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm ${bankError && accountNumber !== confirmAccountNumber ? 'border-red-300' : 'border-gray-300'}`}
                              placeholder="XX XXX XXXXXXXXXXXXX XX"
                            />
                            <p className="mt-1 text-xs text-gray-500">
                              Veuillez saisir à nouveau votre RIB pour confirmation
                            </p>
                          </div>
                        </motion.div>

                        {bankError && (
                          <motion.div variants={formFieldVariants} className="rounded bg-red-50 p-3">
                            <div className="flex items-center">
                              <div className="text-red-500">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5" fill="none" />
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 8v4m0 4h.01" />
                                </svg>
                              </div>
                              <div className="ml-3">
                                <p className="text-sm text-red-500">{bankError}</p>
                              </div>
                            </div>
                          </motion.div>
                        )}

                        <div className="pt-4 flex flex-col sm:flex-row sm:justify-between space-y-3 sm:space-y-0 border-t border-gray-100 mt-4">
                          <button
                            type="button"
                            className="px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 focus:outline-none"
                            onClick={() => setPaymentStep('methods')}
                          >
                            Annuler
                          </button>
                          <button
                            type="button"
                            className={`inline-flex justify-center rounded border border-transparent bg-[#EA580F] px-4 py-2 text-sm font-medium text-white hover:bg-orange-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2 transition-colors ${isSaving ? 'opacity-75 cursor-not-allowed' : ''}`}
                            onClick={handleSaveBankAccount}
                            disabled={isSaving}
                          >
                            {isSaving ? (
                              <span className="flex items-center">
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Enregistrement...
                              </span>
                            ) : (
                              'Enregistrer'
                            )}
                          </button>
                        </div>
                      </motion.div>
                    )}
                    {paymentStep === 'd17Form' && (
                      <motion.div
                        key="d17FormStep"
                        className="space-y-6"
                        variants={formStepVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                      >
                        <Dialog.Title as="h3" className="text-xl font-semibold leading-6 text-gray-900 mb-3 text-center">
                          Ajouter le numéro de téléphone D17
                        </Dialog.Title>
                        <motion.div variants={formFieldVariants}>
                          <div className="space-y-2">
                            <label htmlFor="d17PhoneNumber" className="block text-sm font-medium text-gray-700">
                              Numéro de téléphone D17
                            </label>
                            <input
                              type="text"
                              id="d17PhoneNumber"
                              name="d17PhoneNumber"
                              value={d17PhoneNumber}
                              onChange={(e) => {
                                const value = e.target.value.replace(/\D/g, '').slice(0, 8);
                                setD17PhoneNumber(value);
                                if (d17Error) setD17Error('');
                              }}
                              className={`mt-1 block w-full rounded shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm ${d17Error ? 'border-red-300' : 'border-gray-300'}`}
                              placeholder="Numéro de téléphone D17 (8 chiffres)"
                            />
                            <p className="mt-1 text-xs text-gray-500">
                              Entrez le numéro de téléphone associé à votre compte D17
                            </p>
                          </div>
                        </motion.div>
                        <motion.div variants={formFieldVariants}>
                          <div className="space-y-2">
                            <label htmlFor="confirmD17PhoneNumber" className="block text-sm font-medium text-gray-700">
                              Confirmer le numéro de téléphone D17
                            </label>
                            <input
                              type="text"
                              id="confirmD17PhoneNumber"
                              name="confirmD17PhoneNumber"
                              value={confirmD17PhoneNumber}
                              onChange={(e) => {
                                const value = e.target.value.replace(/\D/g, '').slice(0, 8);
                                setConfirmD17PhoneNumber(value);
                                if (d17Error) setD17Error('');
                              }}
                              className={`mt-1 block w-full rounded shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm ${d17Error ? 'border-red-300' : 'border-gray-300'}`}
                              placeholder="Confirmer le numéro (8 chiffres)"
                            />
                          </div>
                        </motion.div>

                        {d17Error && (
                          <motion.div variants={formFieldVariants} className="rounded bg-red-50 p-3">
                            <div className="flex items-center">
                              <div className="text-red-500">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
                                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5" fill="none" />
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 8v4m0 4h.01" />
                                </svg>
                              </div>
                              <div className="ml-3">
                                <p className="text-sm text-red-500">{d17Error}</p>
                              </div>
                            </div>
                          </motion.div>
                        )}

                        <div className="mt-4 flex flex-col sm:flex-row sm:justify-end space-y-3 sm:space-y-0 sm:space-x-3">
                          <button
                            type="button"
                            className="inline-flex justify-center rounded border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2 transition-colors"
                            onClick={() => setPaymentStep('methods')}
                          >
                            Annuler
                          </button>
                          <button
                            type="button"
                            className={`inline-flex justify-center rounded border border-transparent bg-[#EA580F] px-4 py-2 text-sm font-medium text-white hover:bg-orange-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2 transition-colors ${isSaving ? 'opacity-75 cursor-not-allowed' : ''}`}
                            onClick={handleSaveD17}
                            disabled={isSaving}
                          >
                            {isSaving ? (
                              <span className="flex items-center">
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Enregistrement...
                              </span>
                            ) : (
                              'Enregistrer'
                            )}
                          </button>
                        </div>
                      </motion.div>
                    )}
                    {paymentStep === 'success' && (
                      <motion.div
                        key="successStep"
                        className="space-y-4"
                        variants={formStepVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                      >
                        <Dialog.Title as="h3" className="text-xl font-semibold leading-6 text-gray-900 mb-3 text-center">
                          Informations enregistrées avec succès
                        </Dialog.Title>
                        <div className="space-y-4">
                          <div className="flex justify-center mb-4">
                            <div className="h-16 w-16 bg-green-100 rounded-full flex items-center justify-center text-green-500">
                              <svg className="h-10 w-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                              </svg>
                            </div>
                          </div>
                          <p className="text-center text-gray-600">
                            {successMessage}
                          </p>
                          <div className="flex justify-center mt-6">
                            <button
                              type="button"
                              className="inline-flex justify-center rounded border border-transparent bg-orange-500 px-4 py-2 text-sm font-medium text-white hover:bg-orange-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2"
                              onClick={onClose}
                            >
                              Fermer
                            </button>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </motion.div>
          </div>
        </Dialog>
      )}
    </AnimatePresence>
  );
};

export default PaymentSetupModal;