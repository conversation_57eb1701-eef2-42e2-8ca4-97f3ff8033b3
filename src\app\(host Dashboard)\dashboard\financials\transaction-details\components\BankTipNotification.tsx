'use client';
import React from 'react';

const BankTipNotification: React.FC = () => {
  return (
    <div className="flex items-start gap-2 p-3 rounded-lg bg-white border border-gray-100 shadow-sm mt-2 mb-4">
      <div className="flex-shrink-0 text-red-500 mt-0.5">
        {/* Red lightbulb/tip icon */}
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2C8.13 2 5 5.13 5 9C5 11.38 6.19 13.47 8 14.74V17C8 17.55 8.45 18 9 18H15C15.55 18 16 17.55 16 17V14.74C17.81 13.47 19 11.38 19 9C19 5.13 15.87 2 12 2Z" fill="currentColor" />
          <path d="M9 20H15V21C15 21.55 14.55 22 14 22H10C9.45 22 9 21.55 9 21V20Z" fill="currentColor" />
        </svg>
      </div>
      <div className="text-xs text-gray-600">
        <span className="font-semibold text-red-500">Conseil :</span> Vérifiez vos relevés ou contactez votre banque si le montant n&apos;apparaît pas après ce délai
      </div>
    </div>
  );
};

export default BankTipNotification; 