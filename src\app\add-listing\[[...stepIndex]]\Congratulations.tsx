'use client';

import React from "react";
import CommonLayout from "./CommonLayout";
import Image from "next/image";
import Confetti from "react-confetti";
import { useWindowSize } from "react-use";

const Congratulations = () => {
  const { width, height } = useWindowSize();

  return (
    <CommonLayout params={{ stepIndex: 'congratulations' }}>
      {/* <PERSON><PERSON>tti overlays the viewport, does not affect layout */}
      <div className="fixed inset-0 z-0 pointer-events-none">
        <Confetti
          width={width}
          height={height}
          numberOfPieces={180}
          recycle={false}
          gravity={0.25}
          style={{ position: 'absolute', top: 0, left: 0 }}
        />
      </div>
      {/* Main content */}
      <div className="relative z-10 flex flex-col items-center justify-center py-12 min-h-[60vh]">
        <Image
          src="https://api.almindharbooking.com/storage/v1/object/public/add-listing//Congratulations.png"
          alt="Félicitations"
          width={120}
          height={120}
          priority
          className="mb-6 drop-shadow-xl"
        />
        <h2 className="text-4xl md:text-5xl font-extrabold text-[#EA580E] mb-3 text-center drop-shadow-lg">
          Félicitations !
        </h2>
        <div className="flex flex-col items-center space-y-2 max-w-[90vw] sm:max-w-md px-4">
          <p className="text-base md:text-lg text-neutral-800 text-center leading-relaxed font-medium">
            Excellent, félicitations pour avoir terminé l&apos;annonce.
          </p>
          <p className="text-base md:text-lg text-neutral-800 text-center leading-relaxed font-medium">
            Elle est en attente de validation pour publication.
          </p>
          <p className="text-base md:text-lg text-neutral-800 text-center leading-relaxed font-medium">
            Merci de faire confiance à Almindhar Booking !
          </p>
        </div>
      </div>
    </CommonLayout>
  );
};

export default Congratulations;
