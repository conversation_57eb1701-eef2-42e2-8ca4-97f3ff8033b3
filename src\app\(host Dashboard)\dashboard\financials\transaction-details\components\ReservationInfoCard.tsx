import React from 'react';
import StatusBadge from '../../components/shared/StatusBadge';
import { formatDateRange } from '../../utils/format';
import { PAYOUT_STATUS_UI_MAP } from '../../utils/payoutStatusMap';
import Image from 'next/image';

interface ReservationInfoCardProps {
  reservationNumber: string;
  status: string;
  guestName: string;
  guestAvatarUrl?: string;
  startDate?: string;
  endDate?: string;
  listingTitle?: string;
}

const ReservationInfoCard: React.FC<ReservationInfoCardProps> = ({
  reservationNumber,
  status,
  guestName,
  guestAvatarUrl,
  startDate,
  endDate,
  listingTitle,
}) => {
  const statusLabel = PAYOUT_STATUS_UI_MAP[status as keyof typeof PAYOUT_STATUS_UI_MAP]?.label || status;
  return (
    <div className="bg-white rounded-2xl border border-[#E8EAED] p-4 w-full">
      <div className="flex items-center gap-4 mb-3">
        <span className="bg-[#F3F4F6] text-gray-500 text-xs font-medium rounded-lg px-2 py-1">
          {reservationNumber ? `#BK ${reservationNumber.toUpperCase()}` : ''}
        </span>
        <StatusBadge status={statusLabel} size="small" />
      </div>
      <div className="flex flex-col items-start mb-2">
        <div className="w-[60px] h-[60px] rounded-full border-4 flex items-center justify-center mb-2 overflow-hidden" style={{ borderColor: 'rgba(250, 100, 76, 0.5)' }}>
          {guestAvatarUrl ? (
            <Image src={guestAvatarUrl} alt={guestName} width={60} height={60} className="object-cover rounded-full" />
          ) : (
            <span className="text-xl font-semibold text-gray-700 bg-white rounded-full w-full h-full flex items-center justify-center">
              {guestName?.[0]?.toUpperCase() || 'A'}
            </span>
          )}
        </div>
        <div className="flex flex-col items-start">
          <div className="text-[18px] font-semibold text-black leading-tight">{guestName}</div>
          <div className="flex items-center gap-4 text-[14px] text-gray-500 font-normal">
            <span>Réservation</span>
            <span>{formatDateRange(startDate, endDate)}</span>
          </div>
          <div className="text-[14px] text-gray-500 font-normal w-full break-all break-words whitespace-pre-line">{listingTitle}</div>
        </div>
      </div>
    </div>
  );
};

export default ReservationInfoCard;
