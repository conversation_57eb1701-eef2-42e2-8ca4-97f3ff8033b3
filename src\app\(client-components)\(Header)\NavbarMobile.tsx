"use client"

import Link from "next/link"
import Image from "next/image"
import logoImage from "@/images/booking_png.png"
import {ChevronLeftIcon} from "@heroicons/react/24/outline";
import {SearchParams, useSearch} from "@/app/(stay-listings)/SearchContext"
import {Fragment, useEffect, useState} from "react";
import {
    Dialog,
    DialogPanel,
    Tab,
    TabGroup,
    TabList,
    TabPanel,
    TabPanels,
    Transition,
    TransitionChild
} from "@headlessui/react";
import {XMarkIcon} from "@heroicons/react/24/solid";
import StaySearchForm from "@/app/(client-components)/(HeroSearchForm2Mobile)/(stay-search-form)/StaySearchForm";
import ButtonSubmit from "@/app/(client-components)/(HeroSearchForm2Mobile)/ButtonSubmit";
import {useTimeoutFn} from "react-use";

const formatDate = (date: Date) => {
    date = new Date(date)
    return date.toLocaleDateString("fr-FR", { day: "numeric", month: "short" })
}

const NavbarMobile = () => {
    const { searchParams, setSearchParams } = useSearch()
    const [mobileView, setMobileView] = useState<boolean | null>(null)
    const [showModal, setShowModal] = useState(false)
    const [tempSearchParams, setTempSearchParams] = useState(searchParams)
    const [showDialog, setShowDialog] = useState(false)
    const [, , resetIsShowingDialog] = useTimeoutFn(() => setShowDialog(true), 1)
    const [menuIsOpen, setMenuIsOpen] = useState(false)

    // Add event listeners for menu open/close
    useEffect(() => {
        const handleMenuOpen = () => setMenuIsOpen(true)
        const handleMenuClose = () => setMenuIsOpen(false)
        
        document.addEventListener('menuOpen', handleMenuOpen)
        document.addEventListener('menuClose', handleMenuClose)
        
        return () => {
            document.removeEventListener('menuOpen', handleMenuOpen)
            document.removeEventListener('menuClose', handleMenuClose)
        }
    }, [])

    // Handle updated search params
    const handleSearchChange = (newParams: any) => {
        setTempSearchParams(newParams)
    }

    const handleClearAll = () => {
        const clearedParams: SearchParams = {
            location: "",
            checkIn: null,
            checkOut: null,
            guests: { guestAdults: 0, guestChildren: 0, guestInfants: 0 },
            propertyTypes: [],
            propertyCategories: [],
            amenities: [],
            priceRange: [0, 1000],
            beds: 0,
            bedrooms: 0,
            bathrooms: 0,
            kitchens: 0,
            mapBounds: null,
            paymentType: null,
            roomTypes: [],
            minRating: 0
        }
        setSearchParams(clearedParams)
        setTempSearchParams(clearedParams)
        setShowDialog(false)
        resetIsShowingDialog()
    }

    // Compute dynamic fields
    const dateRange = searchParams.checkIn && searchParams.checkOut
        ? `${formatDate(searchParams.checkIn)} – ${formatDate(searchParams.checkOut)}`
        : "Arrivé"

    const totalGuests =
        (searchParams.guests.guestAdults || 0) +
        (searchParams.guests.guestChildren || 0) +
        (searchParams.guests.guestInfants || 0)

    const guestsText = totalGuests > 0
        ? `${totalGuests} ${totalGuests === 1 ? "invité" : "invités"}`
        : "Invités"

    useEffect(() => {
        // Check if we're on mobile
        const checkMobile = () => {
            setMobileView(window.innerWidth < 768)
        }

        // Initial check
        checkMobile()

        // Add resize listener
        window.addEventListener("resize", checkMobile)

        // Cleanup
        return () => window.removeEventListener("resize", checkMobile)
    }, [])

    function closeModal() {
        setShowModal(false)
    }

    function openModal() {
        setTempSearchParams(searchParams)
        setShowModal(true)
    }

    const handleButtonClick = () => {
        openModal()
    }

    return (
        <div className={`HeroSearchForm2Mobile ${menuIsOpen ? 'hidden' : ''}`}>
        <button onClick={handleButtonClick} className="w-full my-4">
        { mobileView ? (<nav className="NormalMobileNavbar p-4 bg-white dark:bg-neutral-900">
            <div className="flex items-center justify-between">
                {/* Left placeholder */}
                <div className="w-1/4" />
                {/* Logo centered – clicking it pushes to the home page */}
                <div className="w-2/5 text-center">
                    <Link href="/">
                        <Image src={logoImage} alt="logo" className="w-full" priority />
                    </Link>
                </div>
                {/* Profile picture on the right */}
                <div className="w-1/4 flex justify-end text-neutral-500 dark:text-neutral-300/90">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth="1.5"
                        stroke="currentColor"
                        aria-hidden="true"
                        data-slot="icon"
                        className="w-6 h-6"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                        />
                    </svg>
                </div>
            </div>
        </nav>) : (<div className="relative flex items-center space-x-4 rounded-lg border border-neutral-200 px-4 py-2 shadow-lg dark:border-neutral-600 max-w-lg mx-4">
            <div className="w-2/5 flex justify-between items-center">
                {/* Left arrow icon with click redirect */}
                <ChevronLeftIcon
                    className="h-4 w-4 cursor-pointer"
                    onClick={(e) => {
                        e.stopPropagation() // Prevents triggering openModal
                        window.location.href = "/" // Redirects to the home page
                    }}
                />

                {/* Location (value from searchParams if exists, else placeholder) */}
                <span className="text-sm font-medium line-clamp-1">
                    {searchParams.location || "Localisation"}
                </span>
            </div>

            <div className="w-2/5 flex justify-center border-x-2 border-neutral-200">
                {/* Date range (formatted if both dates exist, else placeholder) */}
                <span className="text-sm font-medium line-clamp-1">{dateRange}</span>
            </div>

            <div className="w-1/5 flex justify-center">
                {/* Travelers (computed total or placeholder) */}
                <span className="text-sm font-medium line-clamp-1">{guestsText}</span>
            </div>
        </div>)
        }
        </button>

            <Transition appear show={showModal} as={Fragment}>
                <Dialog as="div" className="HeroSearchFormMobile__Dialog z-max relative" onClose={closeModal}>
                    <div className="fixed inset-0 bg-neutral-100 dark:bg-neutral-900">
                        <div className="flex h-full">
                            <TransitionChild
                                as={Fragment}
                                enter="ease-out transition-transform"
                                enterFrom="opacity-0 translate-y-52"
                                enterTo="opacity-100 translate-y-0"
                                leave="ease-in transition-transform"
                                leaveFrom="opacity-100 translate-y-0"
                                leaveTo="opacity-0 translate-y-52"
                            >
                                <DialogPanel className="w-full">
                                    {showDialog && (
                                        <TabGroup manual className="relative flex h-full flex-1 flex-col justify-between overflow-hidden">
                                            <div className="absolute left-4 top-4">
                                                <button onClick={closeModal}>
                                                    <XMarkIcon className="h-5 w-5 text-black dark:text-white" />
                                                </button>
                                            </div>
                                            <TabList className="flex w-full justify-center space-x-6 pt-12 text-sm font-semibold text-neutral-500 dark:text-neutral-400 sm:space-x-8 sm:text-base">
                                                {["Séjour"].map((item, index) => (
                                                    <Tab key={index} as={Fragment}>
                                                        {({ selected }) => (
                                                            <div className="relative select-none outline-none focus:outline-none focus-visible:ring-0">
                                                                <div className={`${selected ? "text-black dark:text-white" : ""}`}>
                                                                    {item}
                                                                </div>
                                                                {selected && (
                                                                    <span className="absolute inset-x-0 top-full border-b-2 border-black dark:border-white"></span>
                                                                )}
                                                            </div>
                                                        )}
                                                    </Tab>
                                                ))}
                                            </TabList>
                                            <div className="flex flex-1 overflow-hidden px-1.5 pt-3 sm:px-4">
                                                <TabPanels className="hiddenScrollbar flex-1 overflow-y-auto py-4">
                                                    <TabPanel>
                                                        <div className="animate-[myblur_0.4s_ease-in-out] transition-opacity">
                                                            <StaySearchForm onSearchChange={handleSearchChange} />
                                                        </div>
                                                    </TabPanel>
                                                </TabPanels>
                                            </div>
                                            <div className="flex justify-between border-t border-neutral-200 bg-white px-4 py-3 dark:border-neutral-700 dark:bg-neutral-900">
                                                <button
                                                    type="button"
                                                    className="flex-shrink-0 font-semibold underline"
                                                    onClick={handleClearAll}
                                                >
                                                    Effacer tout
                                                </button>
                                                <ButtonSubmit
                                                    className="bg-booking-orange"
                                                    onSubmit={closeModal}
                                                    searchFilters={tempSearchParams}
                                                />
                                            </div>
                                        </TabGroup>
                                    )}
                                </DialogPanel>
                            </TransitionChild>
                        </div>
                    </div>
                </Dialog>
            </Transition>
        </div>
    )
}

export default NavbarMobile
