"use client"

import { useState, useRef, useEffect } from "react"
import { createClient } from "@/utils/supabase/client"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Upload, Camera, AlertCircle, Info, Check, X, FileText, Image as ImageIcon, CreditCard, FileCheck } from "lucide-react"
import Image from "next/image"


type VerificationState = "pending" | "verified" | "rejected" | "not_submitted"

// Define the available ID types
type IdType = "cin" | "passport" | "permit"

interface IdentityVerificationFormProps {
  onFormSubmitted: (status: VerificationState) => void
}



export function IdentityVerificationForm({ onFormSubmitted }: IdentityVerificationFormProps) {
  const [loading, setLoading] = useState(false)
  const [idType, setIdType] = useState<IdType | null>(null)
  const [idNumber, setIdNumber] = useState("")
  const [idNumberError, setIdNumberError] = useState<string | null>(null)
  const [idFrontFile, setIdFrontFile] = useState<File | null>(null)
  const [idBackFile, setIdBackFile] = useState<File | null>(null)
  const [selfieFile, setSelfieFile] = useState<File | null>(null)
  const [idFrontPreview, setIdFrontPreview] = useState<string | null>(null)
  const [idBackPreview, setIdBackPreview] = useState<string | null>(null)
  const [selfiePreview, setSelfiePreview] = useState<string | null>(null)
  const [idFrontError, setIdFrontError] = useState<string | null>(null)
  const [idBackError, setIdBackError] = useState<string | null>(null)
  const [selfieError, setSelfieError] = useState<string | null>(null)
  const [currentStep, setCurrentStep] = useState(0)
  const [submissionComplete, setSubmissionComplete] = useState(false)
  const idFrontRef = useRef<HTMLInputElement>(null)
  const idBackRef = useRef<HTMLInputElement>(null)
  const selfieRef = useRef<HTMLInputElement>(null)
  const router = useRouter()
  const { toast } = useToast()

  // Get the total number of steps based on the ID type
  const getTotalSteps = () => {
    // Passport doesn't need a back image
    if (idType === "passport") {
      return 4; // ID type, front, selfie, submit
    }
    return 5; // ID type, front, back, selfie, submit
  }

  // Get the current progress percentage
  const getProgressPercentage = () => {
    const totalSteps = getTotalSteps();
    return `${(currentStep / (totalSteps - 1)) * 100}%`;
  }

  // Check if back image is required based on ID type
  const isBackImageRequired = () => {
    return idType !== "passport";
  }

  // Reset errors when files change
  useEffect(() => {
    if (idFrontFile) setIdFrontError(null)
  }, [idFrontFile])

  useEffect(() => {
    if (idBackFile) setIdBackError(null)
  }, [idBackFile])

  useEffect(() => {
    if (selfieFile) setSelfieError(null)
  }, [selfieFile])

  // Generate preview URLs when files are selected
  useEffect(() => {
    if (idFrontFile) {
      const objectUrl = URL.createObjectURL(idFrontFile)
      setIdFrontPreview(objectUrl)
      return () => URL.revokeObjectURL(objectUrl)
    }
  }, [idFrontFile])

  useEffect(() => {
    if (idBackFile) {
      const objectUrl = URL.createObjectURL(idBackFile)
      setIdBackPreview(objectUrl)
      return () => URL.revokeObjectURL(objectUrl)
    }
  }, [idBackFile])

  useEffect(() => {
    if (selfieFile) {
      const objectUrl = URL.createObjectURL(selfieFile)
      setSelfiePreview(objectUrl)
      return () => URL.revokeObjectURL(objectUrl)
    }
  }, [selfieFile])

  // Validate ID number based on type
  const validateIdNumber = (number: string, type: IdType | null): boolean => {
    if (!number || !type) return false;
    
    switch (type) {
      case "permit":
        // Tunisian permit format: 2 numbers, slash, 6 numbers (e.g., "00/123456")
        return /^\d{2}\/\d{6}$/.test(number);
      case "passport":
        // Tunisian passport format: 1 letter followed by 6 numbers (e.g., "A123456")
        return /^[A-Z]\d{6}$/.test(number);
      case "cin":
        // Tunisian CIN format: exactly 8 digits
        return /^\d{8}$/.test(number);
      default:
        return false;
    }
  }

  // Format permit number automatically (add slash after 2 digits)
  const formatPermitNumber = (value: string): string => {
    // Remove any non-digit characters except slash
    const cleaned = value.replace(/[^\d\/]/g, '');
    
    // Handle the case when user is deleting the slash
    if (value.length < idNumber.length && idNumber.includes('/') && !value.includes('/')) {
      return cleaned;
    }
    
    // If we have a slash, ensure we only have 2 digits before and max 6 digits after
    if (cleaned.includes('/')) {
      const parts = cleaned.split('/');
      const beforeSlash = parts[0].slice(0, 2); // Limit to 2 digits before slash
      const afterSlash = parts[1].slice(0, 6);  // Limit to 6 digits after slash
      return `${beforeSlash}/${afterSlash}`;
    }
    
    // Limit to 2 digits before adding slash
    if (cleaned.length > 2) {
      const beforeSlash = cleaned.substring(0, 2);
      const afterSlash = cleaned.substring(2).slice(0, 6); // Limit to 6 digits after position 2
      return `${beforeSlash}/${afterSlash}`;
    }
    
    return cleaned;
  }

  // Format passport number (1 letter followed by 6 numbers)
  const formatPassportNumber = (value: string): string => {
    // If empty, return empty
    if (!value) return '';
    
    // Extract the first character if it's a letter
    let letter = '';
    if (/^[A-Za-z]/.test(value)) {
      letter = value.charAt(0).toUpperCase();
    }
    
    // Extract up to 6 digits after the letter
    const digits = value.replace(/[^0-9]/g, '').slice(0, 6);
    
    // If we have a letter, combine with digits
    if (letter) {
      return letter + digits;
    }
    
    // If no letter yet but we have digits, keep only the digits
    // (user might type digits first, then add the letter)
    return digits;
  }

  // Format CIN number (exactly 8 digits)
  const formatCinNumber = (value: string): string => {
    // Remove any non-digit characters
    const cleaned = value.replace(/[^\d]/g, '');
    
    // Limit to 8 digits
    return cleaned.slice(0, 8);
  }

  // Handle ID number change
  const handleIdNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    
    // Format the value if it's a permit
    if (idType === "permit") {
      const formattedValue = formatPermitNumber(value);
      setIdNumber(formattedValue);
    } else if (idType === "passport") {
      const formattedValue = formatPassportNumber(value);
      setIdNumber(formattedValue);
    } else if (idType === "cin") {
      const formattedValue = formatCinNumber(value);
      setIdNumber(formattedValue);
    } else {
      setIdNumber(value);
    }
    
    // Clear error when typing
    setIdNumberError(null);
  }

  // Validate ID number and show error if invalid
  const validateAndShowIdNumberError = (): boolean => {
    if (!idNumber) {
      setIdNumberError("Veuillez saisir un numéro de pièce d'identité");
      return false;
    }
    
    if (!validateIdNumber(idNumber, idType)) {
      if (idType === "permit") {
        setIdNumberError("Le format du permis doit être: 2 chiffres/6 chiffres (ex: 00/123456)");
      } else if (idType === "passport") {
        setIdNumberError("Le format du passeport doit être: 1 lettre suivie de 6 chiffres (ex: A123456)");
      } else if (idType === "cin") {
        setIdNumberError("Le numéro de CIN doit contenir exactement 8 chiffres");
      } else {
        setIdNumberError("Le numéro saisi n'est pas valide");
      }
      return false;
    }
    
    return true;
  }

  // Handle file selection
  const handleFileChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    setFile: React.Dispatch<React.SetStateAction<File | null>>,
    setError: React.Dispatch<React.SetStateAction<string | null>>
  ) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError("Le fichier est trop volumineux. Taille maximale: 5 Mo")
      return
    }

    // Validate file type
    if (!["image/jpeg", "image/png", "image/jpg"].includes(file.type)) {
      setError("Format de fichier non pris en charge. Utilisez JPG ou PNG")
      return
    }

    setFile(file)
    
    // Move to next step if not on the last step
    // For selfie upload (last step), don't auto-advance
    const isSelfieStep = currentStep === (isBackImageRequired() ? 3 : 2)
    if (currentStep < getTotalSteps() - 1 && !isSelfieStep) {
      setCurrentStep(prev => prev + 1)
    }
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate all required fields
    if (!idType) {
      toast({
        title: "Erreur",
        description: "Veuillez sélectionner un type de pièce d'identité",
        variant: "destructive",
      })
      setCurrentStep(0)
      return
    }

    if (!validateAndShowIdNumberError()) {
      setCurrentStep(0)
      return
    }

    // Validate all required files
    if (!idFrontFile) {
      setIdFrontError("Veuillez télécharger le recto de votre pièce d'identité")
      setCurrentStep(1)
      return
    }

    if (isBackImageRequired() && !idBackFile) {
      setIdBackError("Veuillez télécharger le verso de votre pièce d'identité")
      setCurrentStep(2)
      return
    }

    if (!selfieFile) {
      setSelfieError("Veuillez télécharger un selfie avec votre pièce d'identité")
      setCurrentStep(isBackImageRequired() ? 3 : 2)
      return
    }

    setLoading(true)

    try {
      // Validate required fields
      if (!idType) {
        throw new Error("Veuillez sélectionner un type de pièce d'identité")
      }

      if (!idNumber) {
        throw new Error("Veuillez entrer le numéro de votre pièce d'identité")
      }

      if (!idFrontFile) {
        throw new Error("Veuillez télécharger le recto de votre pièce d'identité")
      }

      if (isBackImageRequired() && !idBackFile) {
        throw new Error("Veuillez télécharger le verso de votre pièce d'identité")
      }

      if (!selfieFile) {
        throw new Error("Veuillez télécharger une photo de vous avec votre pièce d'identité")
      }

      const supabase = createClient()
      
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      
      if (userError || !user) {
        throw new Error("Vous devez être connecté pour soumettre une demande de vérification")
      }

      // Check if user already has a verification record
      const { data: existingVerifications, error: checkError } = await supabase
        .from("identity_verifications")
        .select("id, front_image_path, back_image_path, additional_image_path, status")
        .eq("user_id", user.id)
        .limit(1)

      if (checkError) {
        console.error("Error checking existing verifications:", checkError)
        throw new Error("Erreur lors de la vérification des demandes existantes")
      }

      // If user already has a verification record, delete the existing files first
      const isUpdate = existingVerifications && existingVerifications.length > 0
      
      if (isUpdate) {
        const existingVerification = existingVerifications[0]
        console.log("Existing verification found, checking status:", existingVerification.status)
        
        // Always allow resubmission if there's an existing verification
        console.log("Deleting old files before uploading new ones")
        
        // Delete front image if it exists
        if (existingVerification.front_image_path) {
          const { error: deleteFrontError } = await supabase.storage
            .from("identity-documents")
            .remove([existingVerification.front_image_path])
          
          if (deleteFrontError) {
            console.warn("Error deleting front image:", deleteFrontError)
            // Continue anyway, don't block the process if deletion fails
          }
        }
        
        // Delete back image if it exists and is different from front image
        if (existingVerification.back_image_path && 
            existingVerification.back_image_path !== existingVerification.front_image_path) {
          const { error: deleteBackError } = await supabase.storage
            .from("identity-documents")
            .remove([existingVerification.back_image_path])
          
          if (deleteBackError) {
            console.warn("Error deleting back image:", deleteBackError)
            // Continue anyway, don't block the process if deletion fails
          }
        }
        
        // Delete selfie/additional image if it exists
        if (existingVerification.additional_image_path) {
          const { error: deleteSelfieError } = await supabase.storage
            .from("identity-documents")
            .remove([existingVerification.additional_image_path])
          
          if (deleteSelfieError) {
            console.warn("Error deleting selfie image:", deleteSelfieError)
            // Continue anyway, don't block the process if deletion fails
          }
        }
      }

      // Define the storage bucket name correctly
      const STORAGE_BUCKET = "identity-documents"  

      // Upload ID front
      const idFrontPath = `${user.id}/identity_verification/id_front.${idFrontFile.name.split('.').pop()}`
      const { error: idFrontUploadError } = await supabase.storage
        .from(STORAGE_BUCKET)
        .upload(idFrontPath, idFrontFile, { upsert: true })

      if (idFrontUploadError) {
        console.error("Front ID upload error:", idFrontUploadError)
        throw new Error("Erreur lors du téléchargement du recto de la pièce d'identité")
      }

      // Upload ID back if required
      let backImagePath = "";
      let idBackPath = "";  // Declare idBackPath outside the if block
      if (isBackImageRequired() && idBackFile) {
        idBackPath = `${user.id}/identity_verification/id_back.${idBackFile.name.split('.').pop()}`
        const { error: idBackUploadError } = await supabase.storage
          .from(STORAGE_BUCKET)
          .upload(idBackPath, idBackFile, { upsert: true })

        if (idBackUploadError) {
          console.error("Back ID upload error:", idBackUploadError)
          throw new Error("Erreur lors du téléchargement du verso de la pièce d'identité")
        }

        backImagePath = idBackPath;
      } else {
        // For passport or when back image is not provided, use front image as back image
        // This ensures the NOT NULL constraint is satisfied
        backImagePath = idFrontPath;
      }

      // Upload selfie
      const selfiePath = `${user.id}/identity_verification/selfie.${selfieFile.name.split('.').pop()}`
      const { error: selfieUploadError } = await supabase.storage
        .from(STORAGE_BUCKET)
        .upload(selfiePath, selfieFile, { upsert: true })

      if (selfieUploadError) {
        console.error("Selfie upload error:", selfieUploadError)
        throw new Error("Erreur lors du téléchargement du selfie")
      }

      // Create or update verification record using the API endpoint (which will use supabaseAdmin to bypass RLS)
      const response = await fetch("/api/identity-verification/submit", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          isUpdate,
          userId: user.id,
          verificationData: {
            user_id: user.id,
            id_type: idType,
            id_number: idNumber,
            front_image_path: idFrontPath,  
            back_image_path: backImagePath,  
            additional_image_path: selfiePath,  
            status: "pending"
          }
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error("API error:", errorData)
        throw new Error(errorData.error || "Erreur lors de la soumission de la demande de vérification")
      }

      // Show success message and set submission complete
      setSubmissionComplete(true)

      // Notify parent component
      onFormSubmitted("pending")
    } catch (error: any) {
      console.error("Error submitting verification:", error)
      toast({
        title: "Erreur",
        description: error.message || "Une erreur s'est produite lors de la soumission de votre demande",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Navigate to previous step
  const goToPreviousStep = () => {
    if (currentStep > 0) {
      // Skip back image step for passport
      if (currentStep === 3 && idType === "passport") {
        setCurrentStep(1)
      } else {
        setCurrentStep(prev => prev - 1)
      }
    }
  }

  // Navigate to next step
  const goToNextStep = () => {
    if (currentStep < getTotalSteps() - 1) {
      // Validate current step before proceeding
      if (currentStep === 0) {
        if (!idType) {
          toast({
            title: "Erreur",
            description: "Veuillez sélectionner un type de pièce d'identité",
            variant: "destructive",
          })
          return
        }
        
        if (!validateAndShowIdNumberError()) {
          return
        }
      }
      
      if (currentStep === 1 && !idFrontFile) {
        setIdFrontError("Veuillez télécharger le recto de votre pièce d'identité")
        return
      }
      
      if (currentStep === 2 && !idBackFile && isBackImageRequired()) {
        setIdBackError("Veuillez télécharger le verso de votre pièce d'identité")
        return
      }
      
      // Skip back image step for passport
      if (currentStep === 1 && idType === "passport") {
        setCurrentStep(3)
      } else {
        setCurrentStep(prev => prev + 1)
      }
    }
  }

  // Handle ID type change with reset
  const handleIdTypeChange = (type: IdType) => {
    // Reset ID number when changing ID type
    setIdNumber("");
    setIdNumberError(null);
    setIdType(type);
  }

  // If submission is complete, show success message
  if (submissionComplete) {
    return (
      <Card className="border-0 shadow-none">
        <CardHeader className="pb-2">
          <CardTitle className="text-2xl font-bold text-gray-900">Demande soumise avec succès</CardTitle>
          <CardDescription className="text-base">
            Nous avons bien reçu votre demande de vérification d&apos;identité
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-green-50 border border-green-100 rounded-lg p-6 text-center mb-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Check className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Vérification en attente</h3>
            <p className="text-gray-600 mb-4">
              Notre équipe va examiner votre demande dans les plus brefs délais. Vous recevrez une notification par email dès que votre statut de vérification sera mis à jour.
            </p>
            <div className="bg-white border border-gray-200 rounded-lg p-4 text-left">
              <h4 className="font-medium text-gray-900 mb-2">Prochaines étapes :</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start">
                  <span className="w-5 h-5 bg-orange-100 rounded-full flex items-center justify-center mr-2 mt-0.5">
                    <span className="text-orange-600 text-xs">1</span>
                  </span>
                  <span>Examen de vos documents par notre équipe (1-2 jours ouvrables)</span>
                </li>
                <li className="flex items-start">
                  <span className="w-5 h-5 bg-orange-100 rounded-full flex items-center justify-center mr-2 mt-0.5">
                    <span className="text-orange-600 text-xs">2</span>
                  </span>
                  <span>Notification par email du résultat de la vérification</span>
                </li>
                <li className="flex items-start">
                  <span className="w-5 h-5 bg-orange-100 rounded-full flex items-center justify-center mr-2 mt-0.5">
                    <span className="text-orange-600 text-xs">3</span>
                  </span>
                  <span>Accès aux fonctionnalités réservées aux utilisateurs vérifiés</span>
                </li>
              </ul>
            </div>
          </div>
          <Button
            onClick={() => router.push('/dashboard')}
            className="w-full bg-orange-500 hover:bg-orange-600 text-white"
          >
            Retour au tableau de bord
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="border-0 shadow-none">
      <CardHeader className="pb-2">
        <CardTitle className="text-2xl font-bold text-gray-900">Vérification d&apos;identité</CardTitle>
        <CardDescription className="text-base">
          Téléchargez les documents requis pour vérifier votre identité
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Progress indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Progression</span>
            <span className="text-sm font-medium text-gray-700">{currentStep + 1}/{getTotalSteps()}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div 
              className="bg-orange-500 h-2.5 rounded-full transition-all duration-300 ease-in-out" 
              style={{ width: getProgressPercentage() }}
            ></div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Step 1: ID Type */}
          {currentStep === 0 && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Type de pièce d&apos;identité</h3>
                <p className="text-gray-600">
                  Sélectionnez le type de pièce d&apos;identité que vous possédez
                </p>
              </div>

              <div className="space-y-6">
                <div className="space-y-4">
                  <Label htmlFor="id-type" className="text-base font-medium">
                    Type de document
                  </Label>
                  <div className="grid gap-4">
                    <div 
                      className={`flex items-center space-x-3 border rounded-lg p-4 cursor-pointer transition-colors ${
                        idType === "cin" ? "border-orange-500 bg-orange-50" : "border-gray-200 hover:border-orange-300"
                      }`}
                      onClick={() => handleIdTypeChange("cin")}
                    >
                      <div className={`w-5 h-5 rounded-full border flex items-center justify-center ${
                        idType === "cin" ? "border-orange-500" : "border-gray-300"
                      }`}>
                        {idType === "cin" && <div className="w-3 h-3 rounded-full bg-orange-500" />}
                      </div>
                      <div className="flex items-center">
                        <CreditCard className="w-5 h-5 text-gray-600 mr-2" />
                        <span className="font-medium">Carte d&apos;identité nationale (CIN)</span>
                      </div>
                    </div>
                    
                    <div 
                      className={`flex items-center space-x-3 border rounded-lg p-4 cursor-pointer transition-colors ${
                        idType === "passport" ? "border-orange-500 bg-orange-50" : "border-gray-200 hover:border-orange-300"
                      }`}
                      onClick={() => handleIdTypeChange("passport")}
                    >
                      <div className={`w-5 h-5 rounded-full border flex items-center justify-center ${
                        idType === "passport" ? "border-orange-500" : "border-gray-300"
                      }`}>
                        {idType === "passport" && <div className="w-3 h-3 rounded-full bg-orange-500" />}
                      </div>
                      <div className="flex items-center">
                        <FileText className="w-5 h-5 text-gray-600 mr-2" />
                        <span className="font-medium">Passeport</span>
                      </div>
                    </div>
                    
                    <div 
                      className={`flex items-center space-x-3 border rounded-lg p-4 cursor-pointer transition-colors ${
                        idType === "permit" ? "border-orange-500 bg-orange-50" : "border-gray-200 hover:border-orange-300"
                      }`}
                      onClick={() => handleIdTypeChange("permit")}
                    >
                      <div className={`w-5 h-5 rounded-full border flex items-center justify-center ${
                        idType === "permit" ? "border-orange-500" : "border-gray-300"
                      }`}>
                        {idType === "permit" && <div className="w-3 h-3 rounded-full bg-orange-500" />}
                      </div>
                      <div className="flex items-center">
                        <FileCheck className="w-5 h-5 text-gray-600 mr-2" />
                        <span className="font-medium">Permis de conduire</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="id-number" className="text-base font-medium">
                    Numéro de pièce d&apos;identité
                  </Label>
                  <Input
                    id="id-number"
                    type="text"
                    value={idNumber}
                    onChange={handleIdNumberChange}
                    placeholder={
                      idType === "cin" ? "Ex: 12345678" : 
                      idType === "passport" ? "Ex: A123456" : 
                      idType === "permit" ? "Ex: 00/123456" : 
                      "Veuillez saisir le numéro"
                    }
                    className={`w-full ${idNumberError ? 'border-red-300' : ''}`}
                  />
                  {idNumberError && (
                    <div className="flex items-center text-red-500 text-sm">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {idNumberError}
                    </div>
                  )}
                  <p className="text-sm text-gray-500">
                    {idType === "cin" && "Format: 8 chiffres exactement"}
                    {idType === "passport" && "Format: 1 lettre suivie de 6 chiffres (ex: A123456)"}
                    {idType === "permit" && "Format: 2 chiffres/6 chiffres (ex: 00/123456)"}
                    {!idType && "Sélectionnez d'abord un type de document"}
                  </p>
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  type="button"
                  onClick={goToNextStep}
                  className="bg-orange-500 hover:bg-orange-600 text-white"
                >
                  Suivant
                </Button>
              </div>
            </div>
          )}

          {/* Step 2: ID Front */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {idType === "cin" ? "Recto de la carte d'identité" : 
                   idType === "passport" ? "Page principale du passeport" : 
                   idType === "permit" ? "Recto du permis de conduire" : 
                   "Recto de la pièce d'identité"}
                </h3>
                <p className="text-gray-600">
                  Téléchargez une photo claire du {
                    idType === "passport" ? "passeport ouvert à la page principale" : "recto de votre pièce d'identité"
                  }
                </p>
              </div>

              <div className="space-y-4">
                <div 
                  className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
                    idFrontPreview ? 'border-green-300 bg-green-50' : 'border-gray-300 hover:border-orange-300 hover:bg-orange-50'
                  }`}
                  onClick={() => !idFrontPreview && idFrontRef.current?.click()}
                >
                  <input
                    type="file"
                    id="id-front"
                    ref={idFrontRef}
                    className="hidden"
                    accept="image/jpeg,image/png"
                    onChange={(e) => handleFileChange(e, setIdFrontFile, setIdFrontError)}
                  />

                  {idFrontPreview ? (
                    <div className="relative">
                      <div className="relative w-full h-48 mx-auto overflow-hidden rounded-md">
                        <Image
                          src={idFrontPreview}
                          alt="Aperçu du recto"
                          fill
                          style={{ objectFit: 'contain' }}
                        />
                      </div>
                      <div className="flex justify-center mt-4 space-x-2">
                        <Button 
                          type="button" 
                          variant="outline" 
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            idFrontRef.current?.click();
                          }}
                        >
                          <Upload className="w-4 h-4 mr-2" />
                          Changer
                        </Button>
                        <Button 
                          type="button" 
                          variant="outline" 
                          size="sm"
                          className="text-red-500 border-red-200 hover:bg-red-50 hover:text-red-600"
                          onClick={(e) => {
                            e.stopPropagation();
                            setIdFrontFile(null);
                            setIdFrontPreview(null);
                          }}
                        >
                          <X className="w-4 h-4 mr-2" />
                          Supprimer
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <div className="mx-auto flex justify-center">
                        <div className="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center">
                          <FileText className="w-6 h-6 text-orange-500" />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-700">
                          Glissez-déposez ou cliquez pour télécharger
                        </p>
                        <p className="text-xs text-gray-500">
                          JPG ou PNG, max 5MB
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {idFrontError && (
                  <div className="flex items-center text-red-500 text-sm">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    {idFrontError}
                  </div>
                )}

                <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
                  <div className="flex">
                    <Info className="w-5 h-5 text-blue-500 mr-2 flex-shrink-0" />
                    <div className="text-sm text-blue-700">
                      <p className="font-medium mb-1">Conseils pour une bonne photo :</p>
                      <ul className="list-disc list-inside space-y-1 text-xs">
                        <li>Assurez-vous que toutes les informations sont clairement visibles</li>
                        <li>Évitez les reflets et les ombres</li>
                        <li>Prenez la photo dans un endroit bien éclairé</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  type="button"
                  onClick={goToNextStep}
                  className="bg-orange-500 hover:bg-orange-600 text-white"
                >
                  Suivant
                </Button>
              </div>
            </div>
          )}

          {/* Step 3: ID Back - Only for CIN and Permit */}
          {currentStep === 2 && isBackImageRequired() && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {idType === "cin" ? "Verso de la carte d'identité" : 
                   idType === "permit" ? "Verso du permis de conduire" : 
                   "Verso de la pièce d'identité"}
                </h3>
                <p className="text-gray-600">
                  Téléchargez une photo claire du verso de votre {
                    idType === "cin" ? "carte d'identité" : 
                    idType === "permit" ? "permis de conduire" : 
                    "pièce d'identité"
                  }
                </p>
              </div>

              <div className="space-y-4">
                <div 
                  className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
                    idBackPreview ? 'border-green-300 bg-green-50' : 'border-gray-300 hover:border-orange-300 hover:bg-orange-50'
                  }`}
                  onClick={() => !idBackPreview && idBackRef.current?.click()}
                >
                  <input
                    type="file"
                    id="id-back"
                    ref={idBackRef}
                    className="hidden"
                    accept="image/jpeg,image/png"
                    onChange={(e) => handleFileChange(e, setIdBackFile, setIdBackError)}
                  />

                  {idBackPreview ? (
                    <div className="relative">
                      <div className="relative w-full h-48 mx-auto overflow-hidden rounded-md">
                        <Image
                          src={idBackPreview}
                          alt="Aperçu du verso"
                          fill
                          style={{ objectFit: 'contain' }}
                        />
                      </div>
                      <div className="flex justify-center mt-4 space-x-2">
                        <Button 
                          type="button" 
                          variant="outline" 
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            idBackRef.current?.click();
                          }}
                        >
                          <Upload className="w-4 h-4 mr-2" />
                          Changer
                        </Button>
                        <Button 
                          type="button" 
                          variant="outline" 
                          size="sm"
                          className="text-red-500 border-red-200 hover:bg-red-50 hover:text-red-600"
                          onClick={(e) => {
                            e.stopPropagation();
                            setIdBackFile(null);
                            setIdBackPreview(null);
                          }}
                        >
                          <X className="w-4 h-4 mr-2" />
                          Supprimer
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <div className="mx-auto flex justify-center">
                        <div className="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center">
                          <FileText className="w-6 h-6 text-orange-500" />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-700">
                          Glissez-déposez ou cliquez pour télécharger
                        </p>
                        <p className="text-xs text-gray-500">
                          JPG ou PNG, max 5MB
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {idBackError && (
                  <div className="flex items-center text-red-500 text-sm">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    {idBackError}
                  </div>
                )}

                <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
                  <div className="flex">
                    <Info className="w-5 h-5 text-blue-500 mr-2 flex-shrink-0" />
                    <div className="text-sm text-blue-700">
                      <p className="font-medium mb-1">Pourquoi avons-nous besoin du verso ?</p>
                      <p className="text-xs">
                        Le verso de votre pièce d&apos;identité contient des informations importantes pour la vérification, 
                        comme la date d&apos;expiration et des éléments de sécurité.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={goToPreviousStep}
                >
                  Retour
                </Button>
                <Button
                  type="button"
                  onClick={goToNextStep}
                  className="bg-orange-500 hover:bg-orange-600 text-white"
                >
                  Suivant
                </Button>
              </div>
            </div>
          )}

          {/* Step 4: Selfie with ID */}
          {currentStep === (isBackImageRequired() ? 3 : 2) && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Selfie avec votre pièce d&apos;identité</h3>
                <p className="text-gray-600">
                  Prenez une photo de vous tenant votre pièce d&apos;identité
                </p>
              </div>

              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="selfie" className="text-base font-medium">
                    Photo de vous avec votre pièce d&apos;identité
                  </Label>
                  
                  <div className="flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-6 relative">
                    {selfieFile ? (
                      <div className="relative w-full">
                        <Image
                          src={URL.createObjectURL(selfieFile)}
                          alt="Selfie preview"
                          width={300}
                          height={200}
                          className="mx-auto rounded-lg max-h-[200px] w-auto object-contain"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          className="absolute top-2 right-2 h-8 w-8 bg-white border-gray-200"
                          onClick={() => {
                            setSelfieFile(null)
                            if (selfieRef.current) {
                              selfieRef.current.value = ""
                            }
                          }}
                        >
                          <X className="h-4 w-4" />
                          <span className="sr-only">Remove</span>
                        </Button>
                      </div>
                    ) : (
                      <div className="text-center space-y-4">
                        <div className="mx-auto w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center">
                          <Camera className="h-6 w-6 text-orange-600" />
                        </div>
                        <div className="space-y-2">
                          <p className="text-sm font-medium">Téléchargez une photo de vous avec votre pièce d&apos;identité</p>
                          <p className="text-xs text-gray-500">
                            JPG ou PNG, max 5MB
                          </p>
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          className="relative"
                          onClick={() => selfieRef.current?.click()}
                        >
                          <Upload className="mr-2 h-4 w-4" />
                          Choisir un fichier
                          <input
                            ref={selfieRef}
                            type="file"
                            id="selfie"
                            className="sr-only"
                            accept="image/jpeg,image/png,image/jpg"
                            onChange={(e) => handleFileChange(e, setSelfieFile, setSelfieError)}
                          />
                        </Button>
                      </div>
                    )}
                  </div>
                  
                  {selfieError && (
                    <div className="flex items-center text-red-500 text-sm mt-1">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {selfieError}
                    </div>
                  )}
                  
                  <div className="flex items-start text-sm text-gray-500 mt-2">
                    <Info className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                    <span>
                      Assurez-vous que votre visage et les informations sur votre pièce d&apos;identité sont clairement visibles.
                    </span>
                  </div>
                </div>
              </div>

              {/* Submit button for the last step */}
              {selfieFile && (
                <Button 
                  type="submit"
                  className="w-full bg-orange-500 hover:bg-orange-600 text-white mt-6"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <span className="mr-2">Soumission en cours...</span>
                      <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    </>
                  ) : (
                    "Soumettre ma demande de vérification"
                  )}
                </Button>
              )}
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  )
}
