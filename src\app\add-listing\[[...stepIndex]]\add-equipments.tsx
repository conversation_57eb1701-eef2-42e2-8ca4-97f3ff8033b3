"use client";

import React, { FC, useEffect, useState } from "react";
import { useFormContext } from "../FormContext";
import CommonLayout from "./CommonLayout";
import Image from "next/image";
import { motion } from "framer-motion";

export interface AddEquipmentsProps { }

/**
 * Amenity interface now includes a boolean 'is_popular'.
 */
interface Amenity {
  id: string;
  name: string;
  icon: string;
  is_popular: boolean;
}

const AddEquipments: FC<AddEquipmentsProps> = () => {
  const { formData, setFormData, formErrors, setFormErrors } = useFormContext();
  const [amenities, setAmenities] = useState<Amenity[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  // Fetch amenities from your API endpoint.
  const fetchAmenities = async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/listing/amenities", { method: "GET" });
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Unknown error");
      }

      // Ensure the API returns an array of Amenity objects, each with 'is_popular'.
      setAmenities(data.amenities);
    } catch (error) {
      console.error("Error fetching amenities:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAmenities();
  }, []);

  /**
   * Toggles the selection of an amenity. The function returns another function
   * that expects a boolean to set the new checked state.
   */
  const handleCheckboxChange = (amenityId: string) => (checked: boolean) => {
    if (!amenityId) {
      console.error("Invalid amenity ID:", amenityId);
      return;
    }

    const currentSelected: string[] = formData.selectedAmenities || [];
    let newSelectedAmenities: string[];
    if (checked) {
      if (!currentSelected.includes(amenityId)) {
        newSelectedAmenities = [...currentSelected, amenityId];
      } else {
        return;
      }
    } else {
      newSelectedAmenities = currentSelected.filter((id: string) => id !== amenityId);
    }

    // Clear previous equipment errors
    setFormErrors({});
    // Update the form data with the new list
    setFormData(prev => ({
      ...prev,
      selectedAmenities: newSelectedAmenities,
    }));
  };

  // Split amenities into two groups based on 'is_popular'
  const popularAmenities = amenities.filter((amenity) => amenity.is_popular);
  const nonPopularAmenities = amenities.filter((amenity) => !amenity.is_popular);

  const pageVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.5, ease: "easeOut" } },
  };

  const gridVariants = {
    hidden: {}, // No initial animation for the grid itself, children will handle it
    visible: { transition: { staggerChildren: 0.08 } },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 60 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.08, ease: "circOut" } },
  };

  return (
    <CommonLayout params={{ stepIndex: "add-equipments" }}>
      <motion.div
        className="flex flex-col max-w-2xl mx-auto"
        variants={pageVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Header Section */}
        <div className="mb-8 md:pt-12 2xl:pt-24">
          <h2 className="font-bold text-gray-900 text-2xl md:text-3xl lg:text-4xl mb-2 md:mb-3 text-left">
            Quels équipements proposez-vous ?
          </h2>
          <p className="text-gray-400 text-base md:text-lg font-medium !mb-10 text-left">
            De nombreux clients ont cherché un hébergement par commodités.
          </p>
          <h3 className="font-bold text-lg md:text-xl text-neutral-900 !mb-2 text-left">
            Les équipements font toute la différence ! <br /> Offrez à vos voyageurs le confort qu&apos;ils attendent.
          </h3>
        </div>

        {/* Summary error for equipment selection */}
        {formErrors.selectedAmenities && (
          <div className="mb-4 flex " role="alert" aria-live="assertive">
            <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full">
              <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10" strokeWidth="2" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
              </svg>
              <span className="text-sm text-red-700 font-medium">{formErrors.selectedAmenities}</span>
            </div>
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center h-24">
            <div className="w-8 h-8 border-3 border-booking-orange border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : (
          <div className="space-y-6 md:space-y-8">
            {/* Non-popular amenities section */}
            <div className="space-y-4">
              <motion.div
                className="grid grid-cols-2 gap-x-4 gap-y-6 w-full px-4 md:grid-cols-3 md:gap-8 md:px-0 md:max-w-2xl md:mx-auto"
                variants={gridVariants}
                initial="hidden"
                animate="visible"
              >
                {nonPopularAmenities.map((amenity) => {
                  const isSelected = formData.selectedAmenities?.includes(amenity.id) || false;
                  return (
                    <motion.button
                      key={amenity.id}
                      type="button"
                      onClick={() => handleCheckboxChange(amenity.id)(!isSelected)}
                      aria-pressed={isSelected}
                      className={`border rounded-lg py-2 px-5 flex flex-col items-start justify-center transition w-full
                        ${isSelected
                          ? 'border-[#EA580F] bg-[#EA580E26]'
                          : 'border-gray-200'}
                        hover:border-orange-400 focus:border-orange-500`
                      }
                      style={isSelected ? { backgroundColor: '#EA580E26' } : undefined}
                      variants={itemVariants}
                    >
                      <Image
                        src={amenity.icon}
                        alt={amenity.name}
                        width={32}
                        height={32}
                        className="mb-2"
                        unoptimized
                      />
                      <span className="font-medium text-gray-900 text-base text-left break-words w-full block">{amenity.name}</span>
                    </motion.button>
                  );
                })}
              </motion.div>
            </div>

            {/* Popular amenities section */}
            <div className="space-y-4 pb-[4rem]">
              <h3 className="font-bold text-lg md:text-xl text-neutral-900 mb-6 text-left">
                Équipements Populaires
              </h3>
              <motion.div
                className="grid grid-cols-2 gap-x-4 gap-y-6 w-full px-4 md:grid-cols-3 md:gap-8 md:px-0 md:max-w-2xl md:mx-auto"
                variants={gridVariants} // Re-use the same grid variants
                initial="hidden"
                animate="visible"
              >
                {popularAmenities.map((amenity) => {
                  const isSelected = formData.selectedAmenities?.includes(amenity.id) || false;
                  return (
                    <motion.button
                      key={amenity.id}
                      type="button"
                      onClick={() => handleCheckboxChange(amenity.id)(!isSelected)}
                      aria-pressed={isSelected}
                      className={`border rounded-lg py-2 px-5 flex flex-col items-start justify-center transition w-full
                        ${isSelected
                          ? 'border-[#EA580F] bg-[#EA580E26]'
                          : 'border-gray-200'}
                        hover:border-orange-400 focus:border-orange-500`
                      }
                      style={isSelected ? { backgroundColor: '#EA580E26' } : undefined}
                      variants={itemVariants} // Re-use the same item variants
                    >
                      <Image
                        src={amenity.icon}
                        alt={amenity.name}
                        width={32}
                        height={32}
                        className="mb-2"
                        unoptimized
                      />
                      <span className="font-medium text-gray-900 text-base text-left break-words w-full block">{amenity.name}</span>
                    </motion.button>
                  );
                })}
              </motion.div>
            </div>
          </div>
        )}
      </motion.div>
    </CommonLayout>
  );
};

export default AddEquipments;
