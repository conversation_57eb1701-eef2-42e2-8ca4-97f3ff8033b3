"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Upload, Camera, AlertCircle, X, ArrowLeft } from "lucide-react"
import Image from "next/image"

type UploadMethod = "photo" | "webcam"

interface SelfieUploaderProps {
    uploadMethod: UploadMethod
    onComplete: (selfieFile: File) => void
    onBack: () => void
}

export function SelfieUploader({ uploadMethod, onComplete, onBack }: SelfieUploaderProps) {
    const [selfieFile, setSelfieFile] = useState<File | null>(null)
    const [selfiePreview, setSelfiePreview] = useState<string | null>(null)
    const [selfieError, setSelfieError] = useState<string | null>(null)
    const [isCapturing, setIsCapturing] = useState(false)
    const [showTips, setShowTips] = useState(true)
    const [noCamera, setNoCamera] = useState(false)
    const [showConfirmation, setShowConfirmation] = useState(false)

    const selfieInputRef = useRef<HTMLInputElement>(null)
    const videoRef = useRef<HTMLVideoElement>(null)
    const canvasRef = useRef<HTMLCanvasElement>(null)

    // Check if device has camera
    useEffect(() => {
        if (uploadMethod === "webcam") {
            navigator.mediaDevices
                .getUserMedia({ video: true })
                .then((stream) => {
                    // Camera exists, clean up the stream
                    stream.getTracks().forEach((track) => track.stop())
                    setNoCamera(false)
                })
                .catch((err) => {
                    console.error("Error checking camera:", err)
                    setNoCamera(true)
                })
        }
    }, [uploadMethod])

    // Handle file selection
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0]
        if (!file) return

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            setSelfieError("Le fichier est trop volumineux. Taille maximale: 5 Mo")
            return
        }

        // Validate file type
        if (!["image/jpeg", "image/png", "image/jpg"].includes(file.type)) {
            setSelfieError("Format de fichier non pris en charge. Utilisez JPG ou PNG")
            return
        }

        // Set file and preview
        setSelfieFile(file)
        setSelfieError(null)
        setSelfiePreview(URL.createObjectURL(file))
    }

    // Start webcam capture
    const startCapture = async () => {
        setIsCapturing(true)
        setShowTips(true)

        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: "user", // Use front camera for selfie
                    width: { ideal: 1280 },
                    height: { ideal: 720 },
                },
            })

            if (videoRef.current) {
                videoRef.current.srcObject = stream
                videoRef.current.play()
            }
        } catch (err) {
            console.error("Error accessing webcam:", err)
            setSelfieError("Impossible d'accéder à la webcam. Veuillez vérifier vos permissions.")
            setIsCapturing(false)
            setNoCamera(true)
        }
    }

    // Capture photo from webcam
    const capturePhoto = () => {
        if (!videoRef.current || !canvasRef.current) return

        const video = videoRef.current
        const canvas = canvasRef.current
        const context = canvas.getContext("2d")

        if (!context) return

        // Set canvas dimensions to match video
        canvas.width = video.videoWidth
        canvas.height = video.videoHeight

        // Draw video frame to canvas
        context.drawImage(video, 0, 0, canvas.width, canvas.height)

        // Convert canvas to blob with proper MIME type
        canvas.toBlob(
            (blob) => {
                if (!blob) return

                // Create file from blob with proper MIME type
                const timestamp = Date.now()
                const file = new File([blob], `${timestamp}-selfie.jpg`, {
                    type: "image/jpeg",
                    lastModified: timestamp,
                })

                // Verify the file has the correct type
                console.log("Captured selfie file:", file.name, file.type, file.size)

                setSelfieFile(file)
                setSelfiePreview(URL.createObjectURL(blob))
                setSelfieError(null)

                // Stop webcam
                stopCapture()
            },
            "image/jpeg",
            0.95,
        )
    }

    // Stop webcam capture
    const stopCapture = () => {
        if (videoRef.current && videoRef.current.srcObject) {
            const stream = videoRef.current.srcObject as MediaStream
            stream.getTracks().forEach((track) => track.stop())
            videoRef.current.srcObject = null
        }

        setIsCapturing(false)
        setShowTips(false)
    }

    // Handle continue button click
    const handleContinue = () => {
        // Validate required file
        if (!selfieFile) {
            setSelfieError("Veuillez télécharger une photo de vous avec votre pièce d'identité")
            return
        }

        // Show confirmation screen
        setShowConfirmation(true)
    }

    // Handle final submission
    const handleSubmit = () => {
        if (selfieFile) {
            // Ensure the file has the correct MIME type before submitting
            const timestamp = Date.now()
            const fileExtension = selfieFile.name.split(".").pop() || "jpg"

            // Create a new file with the correct MIME type
            const properFile = new File([selfieFile], `${timestamp}-selfie.${fileExtension}`, {
                type: fileExtension === "png" ? "image/png" : "image/jpeg",
                lastModified: timestamp,
            })

            console.log("Submitting selfie file:", properFile.name, properFile.type, properFile.size)
            onComplete(properFile)
        }
    }

    // Confirmation screen
    if (showConfirmation) {
        return (
            <div className="w-full max-w-lg mx-auto text-center">
                <h2 className="text-2xl font-bold mb-4">Nous vous informerons dès que vous serez vérifié</h2>

                <p className="text-gray-600 mb-8">
                    Nous vous enverrons un e-mail dans l&apos;heure pour vous informer si vous avez été vérifié avec succès ou si nous
                    avons besoin de plus d&apos;informations de votre part.
                </p>

                <button
                    onClick={handleSubmit}
                    className="px-6 py-2 rounded-md bg-black text-white font-medium hover:bg-gray-800 mx-auto"
                >
                    J&apos;ai compris
                </button>
            </div>
        )
    }

    // If no camera is detected but webcam method is selected
    if (uploadMethod === "webcam" && noCamera && !selfieFile) {
        return (
            <div className="w-full max-w-lg mx-auto">
                <h2 className="text-2xl font-bold mb-4">Ensuite, vous prendrez un selfie</h2>

                <p className="text-gray-600 mb-6">
                    Nous comparerons votre selfie avec la pièce d&apos;identité que vous avez fournie pour nous assurer qu&apos;il s&apos;agit
                    bien de vous.
                </p>

                <div className="bg-gray-50 border border-gray-200 rounded-md p-4 mb-6">
                    <p className="text-gray-700 font-medium">Mon appareil n&apos;a pas d&apos;appareil photo</p>
                </div>

                <div className="flex justify-between">
                    <button onClick={onBack} className="px-4 py-2 text-gray-600 hover:text-gray-900 font-medium">
                        Retour
                    </button>

                    <button
                        onClick={() => {
                            // Switch to photo upload method
                            selfieInputRef.current?.click()
                        }}
                        className="px-6 py-2 rounded-md bg-black text-white font-medium hover:bg-gray-800"
                    >
                        Continuer
                    </button>

                    <input
                        type="file"
                        ref={selfieInputRef}
                        className="hidden"
                        accept="image/jpeg,image/png"
                        onChange={handleFileChange}
                    />
                </div>
            </div>
        )
    }

    // Webcam capture interface
    if (isCapturing) {
        return (
            <div className="fixed inset-0 bg-black z-50 flex flex-col">
                {/* Header */}
                <div className="p-4 flex justify-between items-center">
                    <button onClick={stopCapture} className="text-white">
                        <ArrowLeft className="h-6 w-6" />
                    </button>
                    <h2 className="text-white text-lg font-medium">Prenez un selfie</h2>
                    <button onClick={() => setShowTips(!showTips)} className="text-white text-sm">
                        Conseils
                    </button>
                </div>

                {/* Tips overlay */}
                {showTips && (
                    <div className="absolute top-16 left-1/2 transform -translate-x-1/2 z-10 bg-black bg-opacity-80 rounded-lg p-4 max-w-md text-white">
                        <div className="flex items-start mb-2">
                            <X className="h-5 w-5 cursor-pointer" onClick={() => setShowTips(false)} />
                            <h3 className="text-center flex-1 font-medium">Conseils pour une photo nette</h3>
                        </div>
                        <ul className="space-y-2 text-sm">
                            <li className="flex items-start">
                                <span className="mr-2">•</span>
                                <span>Commencez par un bon éclairage. Votre visage doit être bien éclairé, sans ombre.</span>
                            </li>
                            <li className="flex items-start">
                                <span className="mr-2">•</span>
                                <span>
                  Assurez-vous que rien ne positionne sur votre visage en entier; pas de chapeau ni de lunettes de
                  soleil, s&apos;il vous plaît.
                </span>
                            </li>
                            <li className="flex items-start">
                                <span className="mr-2">•</span>
                                <span>
                  Tenez votre pièce d&apos;identité à côté de votre visage pour que les deux soient clairement visibles.
                </span>
                            </li>
                        </ul>
                        <div className="mt-4 pt-2 border-t border-gray-700 text-xs">
                            <p>Besoin d&apos;aide ? Obtenez plus de conseils en contactant-nous depuis le Centre d&apos;aide.</p>
                        </div>
                    </div>
                )}

                {/* Camera view */}
                <div className="flex-1 relative flex items-center justify-center">
                    <video ref={videoRef} className="absolute inset-0 w-full h-full object-cover" autoPlay playsInline muted />

                    {/* Face positioning guide */}
                    <div className="relative z-10">
                        <div className="w-64 h-64 rounded-full border-2 border-white"></div>
                        <p className="text-white text-center mt-4 bg-black bg-opacity-50 px-4 py-2 rounded-full">
                            Centrez votre visage dans le cercle
                        </p>
                    </div>
                </div>

                {/* Capture button */}
                <div className="p-6 flex justify-center">
                    <button
                        onClick={capturePhoto}
                        className="w-16 h-16 rounded-full bg-white flex items-center justify-center"
                        aria-label="Prendre une photo"
                    >
                        <div className="w-14 h-14 rounded-full border-2 border-gray-300"></div>
                    </button>
                </div>

                <canvas ref={canvasRef} className="hidden" />
            </div>
        )
    }

    return (
        <div className="w-full max-w-lg mx-auto">
            <h2 className="text-2xl font-bold mb-4">Ensuite, vous prendrez un selfie</h2>

            <p className="text-gray-600 mb-6">
                Nous comparerons votre selfie avec la pièce d&apos;identité que vous avez fournie pour nous assurer qu&apos;il s&apos;agit bien
                de vous. <strong>Tenez votre pièce d&apos;identité à côté de votre visage dans la photo.</strong>
            </p>

            <div className="border border-dashed rounded-md p-4 mb-8">
                {selfiePreview ? (
                    <div className="space-y-4">
                        <div className="aspect-[4/3] relative">
                            <Image
                                src={selfiePreview || "/placeholder.svg"}
                                alt="Selfie avec pièce d'identité"
                                fill
                                className="object-contain"
                            />
                        </div>

                        <div className="flex justify-center space-x-2">
                            {uploadMethod === "photo" ? (
                                <button onClick={() => selfieInputRef.current?.click()} className="text-sm text-gray-600 underline">
                                    Changer la photo
                                </button>
                            ) : (
                                <button onClick={() => startCapture()} className="text-sm text-gray-600 underline">
                                    Reprendre la photo
                                </button>
                            )}
                        </div>
                    </div>
                ) : (
                    <div
                        className="h-full flex flex-col items-center justify-center py-12 cursor-pointer"
                        onClick={() => (uploadMethod === "photo" ? selfieInputRef.current?.click() : startCapture())}
                    >
                        <div className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center mb-4">
                            {uploadMethod === "photo" ? (
                                <Upload className="w-8 h-8 text-gray-500" />
                            ) : (
                                <Camera className="w-8 h-8 text-gray-500" />
                            )}
                        </div>

                        <p className="font-medium text-center mb-1">Ajouter une photo de vous avec votre pièce d&apos;identité</p>

                        <p className="text-sm text-gray-500 text-center">
                            {uploadMethod === "photo" ? "Cliquez pour télécharger" : "Cliquez pour prendre une photo"}
                        </p>
                    </div>
                )}

                {uploadMethod === "photo" && (
                    <input
                        type="file"
                        ref={selfieInputRef}
                        className="hidden"
                        accept="image/jpeg,image/png"
                        onChange={handleFileChange}
                    />
                )}

                {selfieError && (
                    <div className="mt-2 flex items-center text-red-500 text-sm">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        <span>{selfieError}</span>
                    </div>
                )}
            </div>

            <div className="flex justify-between">
                <button onClick={onBack} className="px-4 py-2 text-gray-600 hover:text-gray-900 font-medium">
                    Retour
                </button>

                <button
                    onClick={handleContinue}
                    disabled={!selfieFile}
                    className={`px-6 py-2 rounded-md bg-black text-white font-medium ${
                        !selfieFile ? "opacity-50 cursor-not-allowed" : "hover:bg-gray-800"
                    }`}
                >
                    Continuer
                </button>
            </div>
        </div>
    )
}
