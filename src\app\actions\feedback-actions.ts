"use server"

import { createClient } from "@/utils/supabase/server"

export async function getSectionFeedbackStats(sectionId: number) {
    const supabase = await createClient()

    const { data, error } = await supabase.rpc("get_section_feedback_stats", { section_id: sectionId })

    if (error) {
        console.error("Error fetching feedback stats:", error)
        return { positive: 0, negative: 0 }
    }

    return {
        positive: data?.[0]?.positive_count || 0,
        negative: data?.[0]?.negative_count || 0,
    }
}
