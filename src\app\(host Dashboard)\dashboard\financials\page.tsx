'use client';
import React from 'react';
import FiltersBar from './components/FiltersBar/FiltersBar';
import SummaryCards from './components/SummaryCards/SummaryCards';
import RevenueChart from './components/RevenueChart/RevenueChart';
import Transactions from './components/Transactions/Transactions';
import { useState } from 'react';

const FinancialsPage = () => {
  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState('all');

  const handlePropertyChange = (property: string, checked: boolean) => {
    setSelectedProperties((prev) => {
      const next = checked
        ? [...prev, property]
        : prev.filter((p) => p !== property);
      return next;
    });
  };
  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period);
  };
  // TODO: Implement mobile-first finance dashboard UI
  return (
    <div className="p-4 pt-0">
      <h1 className="mt-[27px]  text-xl font-bold">Finances</h1>
      <div className="mt-6">
        <FiltersBar
          selectedProperties={selectedProperties}
          selectedPeriod={selectedPeriod}
          onPropertyChange={handlePropertyChange}
          onPeriodChange={handlePeriodChange}
        />
      </div>
      <div className="mt-4">
        <SummaryCards selectedProperties={selectedProperties} selectedPeriod={selectedPeriod} />
      </div>
      <div className="mt-6">
        <RevenueChart selectedProperties={selectedProperties} selectedPeriod={selectedPeriod} />
      </div>
      <div className="mt-6">
        <Transactions />
      </div>
      {/* Add main finance components here (summary cards, chart, transactions, etc.) */}
    </div>
  );
};

export default FinancialsPage; 