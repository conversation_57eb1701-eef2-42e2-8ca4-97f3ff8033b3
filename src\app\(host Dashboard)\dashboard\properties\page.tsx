"use client";

import { useState, Suspense } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import dynamic from "next/dynamic";
import { usePathname } from "next/navigation";

// Dynamic imports to prevent server/client HTML mismatch
const ListingsPage = dynamic(() => import("./listings/page"), {
  ssr: false,
  loading: () => <div className="p-6 flex items-center justify-center">Chargement des annonces...</div>
});

const CalendarPage = dynamic(() => import("./calendar/page"), {
  ssr: false,
  loading: () => <div className="p-6 flex items-center justify-center">Chargement du calendrier...</div>
});

export default function PropertiesPage() {
  const router = useRouter();
  const pathname = usePathname();
  const [activeTab, setActiveTab] = useState<string>("listings");

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  return (
    <div className="container">
      <div className="flex items-center border-b">
        <div className="flex-1 overflow-x-auto">
          <Tabs defaultValue="listings" value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="bg-transparent border-b-0 py-0">
              <TabsTrigger 
                value="listings" 
                className="data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-b-[#FF5A5F] data-[state=active]:text-[#FF5A5F] px-4 py-2 rounded-none"
              >
                Votre espace
              </TabsTrigger>
              <TabsTrigger 
                value="calendar" 
                className="data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-b-[#FF5A5F] data-[state=active]:text-[#FF5A5F] px-4 py-2 rounded-none"
              >
                Calendrier
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="listings" className="mt-0 border-0 p-0">
              <Suspense fallback={<div className="p-6 flex items-center justify-center">Chargement des annonces...</div>}>
                <ListingsPage />
              </Suspense>
            </TabsContent>
            
            <TabsContent value="calendar" className="mt-0 border-0 p-0">
              <Suspense fallback={<div className="p-6 flex items-center justify-center">Chargement du calendrier...</div>}>
                <CalendarPage />
              </Suspense>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
} 