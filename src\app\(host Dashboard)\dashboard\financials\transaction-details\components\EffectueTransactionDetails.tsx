'use client';
import React from 'react';
import PriceDetailsCard from './PriceDetailsCard';
import BankTipNotification from './BankTipNotification';
import PropertyInfoCard from './PropertyInfoCard';
import ReservationInfoCard from './ReservationInfoCard';

interface EffectueTransactionDetailsProps {
    transaction: any;
}

const EffectueTransactionDetails: React.FC<EffectueTransactionDetailsProps> = ({
    transaction
}) => {
    const { booking, listing, guest } = transaction;

    return (
        <div className="w-full max-w-md flex flex-col gap-5">
            {/* Success and Virement Text */}
            <div className="mb-2 mt-8">
                <p className="text-[18px] font-medium text-left">
                    Votre hébergement s&apos;est parfaitement déroulé. Le virement a été effectué – comptez 2 à 3 jours ouvrés pour le voir apparaître sur votre compte bancaire.
                </p>
            </div>

            {/* Bank Tip Notification */}
            <div className="mb-4">
                <BankTipNotification />
            </div>

            {/* Property Info */}
            <PropertyInfoCard
                name={listing?.title}
                location={listing?.address}
                imageSrc={listing?.featuredImageUrl}
            />

            {/* Reservation Info Card */}
            <ReservationInfoCard
                reservationNumber={booking?.reservationNumber}
                status={transaction.status}
                guestName={guest?.fullname}
                guestAvatarUrl={guest?.avatarUrl}
                startDate={booking?.startDate}
                endDate={booking?.endDate}
                listingTitle={listing?.title}
            />

            {/* Price Details */}
            <div className="mt-3">
                <PriceDetailsCard
                    priceDetails={transaction.priceDetails}
                    total={transaction.totalPrice}
                />
            </div>
        </div>
    );
};

export default EffectueTransactionDetails; 