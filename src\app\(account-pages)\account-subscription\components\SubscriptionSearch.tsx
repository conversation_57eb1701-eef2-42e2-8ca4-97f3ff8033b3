import React from 'react';
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

type SubscriptionSearchProps = {
    searchQuery: string;
    setSearchQuery: (query: string) => void;
};

const SubscriptionSearch = ({ searchQuery, setSearchQuery }: SubscriptionSearchProps) => {
    return (
        <div className="relative w-full md:w-96">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
                placeholder="Rechercher un hébergement ou un plan"
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
            />
        </div>
    );
};

export default SubscriptionSearch;