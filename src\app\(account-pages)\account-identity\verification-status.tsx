"use client"
import { <PERSON>, <PERSON><PERSON><PERSON>cle, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"

type VerificationState = "pending" | "verified" | "rejected" | "not_submitted"

interface VerificationStatusProps {
  status: VerificationState
  onResubmit?: () => void
  rejectionReason: string 
}

export function VerificationStatus({ status, onResubmit, rejectionReason }: VerificationStatusProps) {
  // Add detailed debugging
  console.log("VerificationStatus RENDER with props:", {
    status,
    rejectionReason,
    rejectionReasonType: typeof rejectionReason
  });

  console.log("Display reason to be used:", rejectionReason);

  return (
    <Card className="border-0 shadow-none">
      <CardHeader className="pb-2">
        <CardTitle className="text-2xl font-bold text-gray-900">Statut de vérification</CardTitle>
        <CardDescription className="text-base">
          Suivez l&apos;état de votre vérification d&apos;identité
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col items-center text-center p-6">
          {status === "not_submitted" && (
            <div className="max-w-md">
              <div className="w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-4">
                <ShieldCheck className="h-10 w-10 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Non soumis</h3>
              <p className="text-gray-600 mb-6">
                Vous n&apos;avez pas encore soumis vos documents d&apos;identité. La vérification est nécessaire pour accéder à toutes les fonctionnalités.
              </p>
            </div>
          )}

          {status === "pending" && (
            <div className="max-w-md">
              <div className="w-20 h-20 rounded-full bg-amber-100 flex items-center justify-center mx-auto mb-4">
                <Clock className="h-10 w-10 text-amber-500" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">En cours de vérification</h3>
              <p className="text-gray-600 mb-4">
                Nous examinons actuellement vos documents. Ce processus prend généralement entre 24 et 48 heures.
              </p>
              <div className="p-4 bg-amber-50 rounded-lg border border-amber-100">
                <p className="text-amber-700 text-sm">
                  Vous recevrez une notification par email une fois que votre vérification sera terminée.
                </p>
              </div>
            </div>
          )}

          {status === "verified" && (
            <div className="max-w-md">
              <div className="w-20 h-20 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-10 w-10 text-green-500" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Vérification réussie</h3>
              <p className="text-gray-600 mb-4">
                Votre identité a été vérifiée avec succès. Vous avez maintenant accès à toutes les fonctionnalités de la plateforme.
              </p>
              <div className="p-4 bg-green-50 rounded-lg border border-green-100">
                <p className="text-green-700 text-sm">
                  Merci d&apos;avoir complété le processus de vérification. Votre compte est maintenant entièrement vérifié.
                </p>
              </div>
            </div>
          )}

          {status === "rejected" && (
            <div className="max-w-md">
              <div className="w-20 h-20 rounded-full bg-red-100 flex items-center justify-center mx-auto mb-4">
                <AlertTriangle className="h-10 w-10 text-red-500" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Vérification échouée</h3>
              <p className="text-gray-600 mb-4">
                Malheureusement, votre vérification d&apos;identité a été rejetée. Veuillez soumettre à nouveau vos documents.
              </p>
              
              <div className="p-4 bg-red-50 rounded-lg border border-red-100 mb-6 text-left">
                <h4 className="font-medium text-red-800 mb-2">Raison du refus :</h4>
                <p className="text-red-700 text-sm">{rejectionReason}</p>
                <div className="mt-2 pt-2 border-t border-red-100">
                  <h4 className="font-medium text-red-800 mb-2">Conseils pour la nouvelle soumission :</h4>
                  <ul className="text-red-700 text-sm space-y-1 list-disc list-inside">
                    <li>Assurez-vous que tous les documents sont clairement visibles</li>
                    <li>Vérifiez que toutes les informations sont complètes</li>
                    <li>Utilisez des documents valides et non expirés</li>
                  </ul>
                </div>
              </div>
              
              <Button 
                onClick={onResubmit} 
                className="w-full bg-red-600 hover:bg-red-700 text-white"
              >
                Soumettre à nouveau
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
