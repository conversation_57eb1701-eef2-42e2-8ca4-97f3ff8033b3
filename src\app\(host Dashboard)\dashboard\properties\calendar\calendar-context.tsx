"use client"

import { createContext, useState, useEffect, type ReactNode } from "react"
import { useUser } from "@/contexts/UserContext"

// Define the structure for blocked dates
export interface BlockedDate {
  id: string
  listing_id: string
  start_date: string
  end_date: string
  listing_title?: string
  host_id?: string
}

// Define the context type
interface CalendarContextType {
  hostId: string | null
  setHostId: (id: string) => void
  blockedDates: BlockedDate[]
  isLoading: boolean
  error: string | null
  fetchBlockedDates: () => Promise<void>
}

// Create the context with default values
export const CalendarContext = createContext<CalendarContextType>({
  hostId: null,
  setHostId: () => {},
  blockedDates: [],
  isLoading: false,
  error: null,
  fetchBlockedDates: async () => {},
})

// Create the provider component
interface CalendarProviderProps {
  children: ReactNode
  initialBlockedDates?: BlockedDate[]
}

export function CalendarProvider({ children, initialBlockedDates = [] }: CalendarProviderProps) {
  // Get the host ID from the user context
  const { userProfile } = useUser()
  const [hostId, setHostId] = useState<string | null>(userProfile?.id || null)
  const [blockedDates, setBlockedDates] = useState<BlockedDate[]>(initialBlockedDates)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Update hostId when userProfile changes
  useEffect(() => {
    if (userProfile?.id) {
      setHostId(userProfile.id)
    }
  }, [userProfile])

  const fetchBlockedDates = async () => {
    if (!hostId) {
      setError("Host ID is required to fetch blocked dates")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // Change the URL to match the existing API endpoint
      const response = await fetch(`/api/listing-availability?hostId=${hostId}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch blocked dates: ${response.status}`)
      }

      const result = await response.json()

      if (result.success) {
        setBlockedDates(result.data)
      } else {
        setError(result.error || "Failed to fetch blocked dates")
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      console.error("Error fetching blocked dates:", err)
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch blocked dates when hostId changes
  useEffect(() => {
    if (hostId) {
      fetchBlockedDates()
    }
  }, [hostId])

  const value = {
    hostId,
    setHostId,
    blockedDates,
    isLoading,
    error,
    fetchBlockedDates,
  }

  return <CalendarContext.Provider value={value}>{children}</CalendarContext.Provider>
}
