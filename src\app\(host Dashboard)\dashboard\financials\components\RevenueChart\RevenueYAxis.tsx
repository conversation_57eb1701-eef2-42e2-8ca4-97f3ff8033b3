import React from 'react';
import { RevenueChartMonth } from '../../types/financeTypes';

export interface RevenueYAxisProps {
  data: RevenueChartMonth[];
  loading: boolean;
  error: string | null;
}

function getTicks(maxValue: number): number[] {
  if (maxValue <= 0) return [0, 0, 0, 0, 0];
  // Round up to nearest 1000 or 500 depending on scale
  const step = maxValue > 5000 ? 2000 : maxValue > 2000 ? 1000 : maxValue > 1000 ? 500 : 200;
  const top = Math.ceil(maxValue / step) * step;
  return [top, top * 0.75, top * 0.5, top * 0.25, 0];
}

const RevenueYAxis: React.FC<RevenueYAxisProps> = ({ data, loading, error }) => {
  let maxValue = 0;
  if (data && data.length > 0) {
    maxValue = Math.max(...data.map(d => Math.max(d.paid, d.upcoming)));
  }
  const ticks = getTicks(maxValue);

  if (loading) return <div className="flex flex-col justify-between h-[220px] min-w-[56px] relative select-none" />;
  if (error) return <div className="flex flex-col justify-between h-[220px] min-w-[56px] relative select-none" />;

  return (
    <div className="flex flex-col justify-between h-[220px] min-w-[56px] relative select-none">
      {/* Axis line flush with right edge, now extends closer to the top */}
      <div className="absolute right-0 top-0 bottom-0 w-[1px] bg-[#E8EAED] z-0" style={{ height: 'calc(100% - 8px)' }} />
      {ticks.map((tick, i) => (
        <div key={i} className="flex items-center justify-end w-full z-10" style={{ height: i === 0 ? 28 : undefined }}>
          <span className="text-xs text-[#A3A3A3] font-semibold pr-2" style={{ minWidth: 40, textAlign: 'right' }}>{tick > 0 ? tick.toLocaleString('fr-FR') : 0}</span>
        </div>
      ))}
    </div>
  );
};

export default RevenueYAxis; 