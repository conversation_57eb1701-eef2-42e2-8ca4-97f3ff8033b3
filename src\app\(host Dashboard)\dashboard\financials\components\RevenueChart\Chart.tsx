import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axi<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Toolt<PERSON>,
  ResponsiveContainer,
} from 'recharts';
import { RevenueChartMonth } from '../../types/financeTypes';

export interface ChartProps {
  data: RevenueChartMonth[];
  loading: boolean;
  error: string | null;
}

const COLORS = {
  paid: '#2563EB', // blue
  due: '#EA5911', // orange
};

const MONTHS = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];

const Chart: React.FC<ChartProps> = ({ data, loading, error }) => {
  const now = new Date();
  const currentMonth = now.getMonth() + 1; // 1-based

  // Always show all months Jan-Dec
  const chartData = Array.from({ length: 12 }, (_, i) => {
    const monthData = data.find(d => d.month === i + 1) || { month: i + 1, paid: 0, upcoming: 0 };
    return {
      ...monthData,
      month: MONTHS[i],
      // Only show upcoming for current/future months
      upcoming: i + 1 >= currentMonth ? monthData.upcoming : 0,
    };
  });

  if (loading) return <div className="h-[220px] flex items-center justify-center">Chargement...</div>;
  if (error) return <div className="h-[220px] flex items-center justify-center text-red-500">{error}</div>;

  console.log('RevenueChart chartData:', chartData);

  return (
    <ResponsiveContainer width={1200} height={220}>
      <BarChart
        width={1200}
        height={220}
        data={chartData}
        margin={{ top: 16, right: 24, left: 12, bottom: 0 }}
        barCategoryGap={16}
      >
        <XAxis
          dataKey="month"
          tick={{ fontSize: 12, fill: '#A3A3A3' }}
          axisLine={true}
          tickLine={false}
        />
        <YAxis hide={true} />
        <Tooltip
          contentStyle={{ borderRadius: 12, fontSize: 14 }}
          labelStyle={{ color: '#A3A3A3', fontWeight: 600 }}
          formatter={(value, name) => [`${value} DT`, name]}
        />
        <Bar dataKey="paid" fill={COLORS.paid} radius={[4, 4, 0, 0]} name="Montant payé" />
        <Bar dataKey="upcoming" fill={COLORS.due} radius={[4, 4, 0, 0]} name="À venir" />
      </BarChart>
    </ResponsiveContainer>
  );
};

export default Chart; 