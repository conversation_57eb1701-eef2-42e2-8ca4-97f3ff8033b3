export function NewsletterSubscription() {
  return (
    <div className="bg-[#ea580e] rounded-xl p-8 md:p-12 flex flex-col md:flex-row items-center justify-between w-full">
      <div className="text-white text-2xl md:text-3xl font-bold mb-6 md:mb-0 text-center md:text-left">
        Subscribe to our
        <br className="hidden sm:block" /> weekly newsletter
      </div>

      <div className="w-full md:w-auto">
        <div className="flex flex-col sm:flex-row gap-3">
          <input
            type="email"
            placeholder="Enter email adress"
            className="px-4 py-3 rounded-md border-0 focus:outline-none focus:ring-2 focus:ring-white/20 w-full sm:w-64"
          />
          <button className="bg-white text-[#ea580e] px-4 py-3 rounded-md font-medium hover:bg-opacity-90 transition-colors whitespace-nowrap">
            Get in Touch
          </button>
        </div>
      </div>
    </div>
  )
}

