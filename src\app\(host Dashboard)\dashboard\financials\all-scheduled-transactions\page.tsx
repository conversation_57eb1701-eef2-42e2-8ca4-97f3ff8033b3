'use client';
import React, { useState, useEffect, useRef, useCallback } from 'react';
import Header from '../components/shared/Header';
import FiltersBar from '../components/FiltersBar/FiltersBar';
import TransactionTable from '../components/shared/TransactionTable';
import { Transaction } from '../components/shared/TransactionRow';
import checkInViewIntersectionObserver from '@/utils/isInViewPortIntersectionObserver';
import TransactionFilters from '../components/shared/TransactionFilters';
import { statusOptions } from '../components/shared/filterOptions';

const PAGE_SIZE = 10;

const AllScheduledTransactionsPage = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [search, setSearch] = useState('');
  const [filters, setFilters] = useState<{ status?: string; property?: string; period?: string; amount?: string }>({});
  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState('all');
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const loaderRef = useRef<HTMLDivElement | null>(null);
  const offsetRef = useRef(0);
  const loadingMoreRef = useRef(false);

  // Use debounce for search to prevent race conditions
  const searchTimeout = useRef<NodeJS.Timeout | null>(null);
  const handleSearch = useCallback((searchTerm: string) => {
    console.log("Search term:", searchTerm); 
    // Clear the previous timeout if there is one
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }
    
    // Set a new timeout
    searchTimeout.current = setTimeout(() => {
      setSearch(searchTerm);
    }, 500); // 500ms debounce
  }, []);

  const deduplicate = (arr: Transaction[]) => {
    const seen = new Set();
    return arr.filter((tx) => {
      if (seen.has(tx.id)) return false;
      seen.add(tx.id);
      return true;
    });
  };

  const fetchTransactions = useCallback(async (reset = false) => {
    if (loadingMoreRef.current) return;
    loadingMoreRef.current = true;
    setLoading(true);
    const params = new URLSearchParams();
    const offset = reset ? 0 : offsetRef.current;
    params.set('offset', offset.toString());
    params.set('limit', PAGE_SIZE.toString());
    if (search) params.set('search', search);
    if (filters.status) params.set('status', filters.status);
    if (filters.property) params.set('property', filters.property);
    if (filters.period) params.set('period', filters.period);
    if (filters.amount) params.set('amount', filters.amount);
    const res = await fetch(`/api/financials/upcoming/all?${params.toString()}`);
    const data = await res.json();
    if (data.success) {
      const newTxs = data.transactions.map((t: any) => ({
        id: t.id,
        reservation: t.reservation_number ? t.reservation_number.toUpperCase() : '',
        status: t.status,
        amount: t.amount,
        date: t.created_at,
      }));
      if (reset) {
        setTransactions(deduplicate(newTxs));
        offsetRef.current = PAGE_SIZE;
        setHasMore(newTxs.length === PAGE_SIZE);
      } else {
        setTransactions((prev) => deduplicate([...prev, ...newTxs]));
        offsetRef.current += PAGE_SIZE;
        setHasMore(newTxs.length === PAGE_SIZE);
      }
    } else {
      if (reset) setTransactions([]);
      setHasMore(false);
    }
    setLoading(false);
    loadingMoreRef.current = false;
  }, [search, filters]);

  // Initial and filter/search effect
  useEffect(() => {
    offsetRef.current = 0;
    fetchTransactions(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search, filters]);

  // Infinite scroll
  useEffect(() => {
    if (!hasMore || loading) return;
    if (!loaderRef.current) return;
    checkInViewIntersectionObserver({
      target: loaderRef.current,
      callback: () => {
        if (!loading && hasMore) fetchTransactions();
      },
      options: { root: null, rootMargin: '0%', threshold: 0 },
      freezeOnceVisible: false,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loaderRef.current, hasMore, loading, fetchTransactions]);

  const handleFilterChange = (filterType: string, value: string) => {
    setFilters((prev) => ({ ...prev, [filterType]: value }));
  };

  // Handlers for FiltersBar
  const handlePropertyChange = (propertyId: string, checked: boolean) => {
    setSelectedProperties((prev) => {
      const next = checked
        ? [...prev, propertyId]
        : prev.filter((id) => id !== propertyId);
      // Also update filters.property for API with UUIDs
      setFilters((f) => ({ ...f, property: next.join(',') }));
      return next;
    });
  };
  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period);
    setFilters((f) => ({ ...f, period }));
  };

  // Only show these statuses for scheduled transactions (exclude paid, ready, refused)
  const scheduledStatusOptions = statusOptions.filter(opt => !['paid', 'ready', 'refused'].includes(opt.value));

  const handleStatusChange = (status: string, checked: boolean) => {
    setSelectedStatuses((prev) => {
      const next = checked
        ? [...prev, status]
        : prev.filter((s) => s !== status);
      // Also update filters.status for API (comma-separated)
      setFilters((f) => ({ ...f, status: next.join(',') }));
      return next;
    });
  };

  return (
    <div className="min-h-screen bg-white p-4">
      <Header title="Paiement prévu" />
      
      <div className="mt-4">
        <TransactionFilters
          onSearch={handleSearch}
          onFilterChange={handleFilterChange}
        />
      </div>
      <div className="mt-4">
        <FiltersBar
          selectedProperties={selectedProperties}
          selectedPeriod={selectedPeriod}
          onPropertyChange={handlePropertyChange}
          onPeriodChange={handlePeriodChange}
          statusOptions={scheduledStatusOptions}
          selectedStatuses={selectedStatuses}
          onStatusChange={handleStatusChange}
        />
      </div>
      
      <div className="mt-6">
        <TransactionTable transactions={transactions} />
        <div ref={loaderRef} style={{ height: 32 }} />
        {loading && <div className="text-center py-4 text-gray-400">Chargement...</div>}
        {!hasMore && !loading && transactions.length > 0 && (
          <div className="text-center py-4 text-gray-400">Aucune autre transaction</div>
        )}
      </div>
    </div>
  );
};

export default AllScheduledTransactionsPage; 