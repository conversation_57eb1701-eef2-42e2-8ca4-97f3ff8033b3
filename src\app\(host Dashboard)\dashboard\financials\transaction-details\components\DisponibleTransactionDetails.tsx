'use client';
import React from 'react';
import { Transaction } from '../../components/shared/TransactionRow';
import ReservationInfoCard from './ReservationInfoCard';
import PriceDetailsCard from './PriceDetailsCard';
import PaymentRequestButton from './PaymentRequestButton';
import PropertyInfoCard from './PropertyInfoCard';

interface DisponibleTransactionDetailsProps {
    transaction: any;
}

const DisponibleTransactionDetails: React.FC<DisponibleTransactionDetailsProps> = ({
    transaction
}) => {
    const { booking, listing, guest } = transaction;

    return (
        <div className="w-full max-w-md flex flex-col gap-5">
            {/* Congratulation Text */}
            <div className="mb-2 mt-8">
                <p className="text-[18px] font-medium text-left">
                    Félicitations ! Votre hébergement a été complété avec succès. Pour recevoir votre revenu
                </p>
            </div>

            {/* Payment Request Button */}
            <div className="mb-4">
                <PaymentRequestButton payoutId={transaction.id} />
            </div>

            {/* Property Info */}
            <PropertyInfoCard
                name={listing?.title}
                location={listing?.address}
                imageSrc={listing?.featuredImageUrl}
            />

            {/* Reservation Info Card */}
            <ReservationInfoCard
                reservationNumber={booking?.reservationNumber}
                status={transaction.status}
                guestName={guest?.fullname}
                guestAvatarUrl={guest?.avatarUrl}
                startDate={booking?.startDate}
                endDate={booking?.endDate}
                listingTitle={listing?.title}
            />

            {/* Price Details */}
            <div className="mt-3">
                <PriceDetailsCard
                    priceDetails={transaction.priceDetails}
                    total={transaction.totalPrice}
                />
            </div>
        </div>
    );
};

export default DisponibleTransactionDetails; 