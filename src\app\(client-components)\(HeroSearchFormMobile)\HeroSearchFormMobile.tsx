import React, { useState, useEffect } from "react";
import { MagnifyingGlassIcon } from "@heroicons/react/24/solid";
import { useRouter } from "next/navigation";
import { useSearch } from "@/app/(stay-listings)/SearchContext";
import LocationInput from "../(HeroSearchForm)/LocationInput";
import StayDatesRangeInput from "../(HeroSearchForm)/(stay-search-form)/StayDatesRangeInput";
import GuestsInput from "../(HeroSearchForm)/GuestsInput";
import type { GuestsObject } from "@/app/(client-components)/(HeroSearchFormSmall)/GuestsInput";

const HeroSearchFormMobile = () => {
  const router = useRouter();
  const { searchParams, setSearchParams } = useSearch();
  const [tempSearchParams, setTempSearchParams] = useState(searchParams);

  // Handlers for each field
  const handleLocationChange = (location: string) => {
    setTempSearchParams((prev: any) => ({ ...prev, location }));
  };

  const handleDatesChange = (startDate: Date | null, endDate: Date | null) => {
    setTempSearchParams((prev: any) => ({ ...prev, checkIn: startDate, checkOut: endDate }));
  };

  const handleGuestsChange = (guests: GuestsObject) => {
    setTempSearchParams((prev: any) => ({ ...prev, guests }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSearchParams(tempSearchParams);
    router.push("/listing-stay-map");
  };

  return (
    <form
      className="block lg:hidden w-full bg-white rounded-3xl shadow-lg p-4 space-y-2"
      onSubmit={handleSubmit}
    >
      {/* Destination */}
      <div className="border-b py-2">
        <LocationInput
          placeHolder="Destination"
          className="w-full"
          defaultValue={tempSearchParams.location}
          onChange={handleLocationChange}
        />
      </div>
      {/* Date */}
      <div className="border-b py-2">
        <StayDatesRangeInput
          className="w-full"
          onDateChange={handleDatesChange}
          startDate={tempSearchParams.checkIn}
          endDate={tempSearchParams.checkOut}
        />
      </div>
      {/* Travelers */}
      <div className="py-2">
        <GuestsInput
          className="w-full"
          onGuestsChange={handleGuestsChange}
          defaultValue={tempSearchParams.guests}
          showSubmitButton={false}
        />
      </div>
      {/* Search Button */}
      <button
        type="submit"
        className="w-full relative flex items-center justify-center bg-booking-orange text-white rounded-full py-4 text-lg font-bold mt-2"
      >
        <span className="w-full text-center">Recherche</span>
        <span className="absolute right-6 top-1/2 -translate-y-1/2">
          <MagnifyingGlassIcon className="w-7 h-7" />
        </span>
      </button>
    </form>
  );
};

export default HeroSearchFormMobile; 