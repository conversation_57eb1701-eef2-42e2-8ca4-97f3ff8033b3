import React from "react";

const BRAND_COLOR = "#EA5911";

export default function QuiSommesNous() {
    return (
        <div className="relative min-h-screen flex items-center justify-center py-10 px-2 bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
            {/* Abstract background shape */}
            <div
                aria-hidden="true"
                className="absolute -top-32 -left-32 w-[500px] h-[500px] rounded-full opacity-10 blur-2xl"
                style={{ background: `radial-gradient(circle, ${BRAND_COLOR} 0%, transparent 70%)` }}
            />
            <div className="w-full max-w-4xl bg-white rounded-3xl shadow-2xl p-10 sm:p-16 relative z-10">
                <h1 className="text-5xl font-extrabold text-center mb-8 text-gray-900 tracking-tight">
                    Qui sommes-nous&nbsp;?
                    <span
                        className="block mx-auto mt-4 rounded-full"
                        style={{ width: 80, height: 8, background: BR<PERSON>D_COLOR }}
                    ></span>
                </h1>
                <p className="mb-10 text-xl text-gray-700 text-left text-justify leading-loose">
                    <span style={{ color: BRAND_COLOR, fontWeight: 600 }}>Bienvenue sur Almindhar Booking</span>, la nouvelle extension de la plateforme immobilière tunisienne Almindhar. Nous connectons propriétaires et voyageurs pour des locations à court terme, partout en Tunisie — que ce soit pour des vacances, une mission professionnelle, un soin médical ou un événement ponctuel.
                </p>

                <div className="w-full flex justify-center mb-10">
                    <div style={{ background: BRAND_COLOR }} className="h-1 w-32 rounded-full opacity-30" />
                </div>

                <section className="mb-10">
                    <h2 className="text-2xl font-bold text-gray-800 mb-3 pl-4 border-l-8" style={{ borderColor: BRAND_COLOR, background: 'rgba(234,89,17,0.04)' }}>
                        Pourquoi Almindhar Booking&nbsp;?
                    </h2>
                    <p className="text-lg text-gray-700 leading-loose text-justify">
                        Parce que les besoins évoluent, et les logements aussi. Nous avons lancé <span style={{ color: BRAND_COLOR, fontWeight: 600 }}>Almindhar Booking</span> pour répondre à une demande croissante d&rsquo;hébergement flexible, sécurisé, et humain. Notre objectif est clair&nbsp;: <span style={{ color: BRAND_COLOR, fontWeight: 600 }}>faciliter l&rsquo;accès au logement à court terme partout en Tunisie</span>, en valorisant le patrimoine local et l&rsquo;accueil chaleureux de nos hôtes.
                    </p>
                </section>

                <div className="w-full flex justify-center mb-10">
                    <div style={{ background: BRAND_COLOR }} className="h-1 w-32 rounded-full opacity-30" />
                </div>

                <section className="mb-10">
                    <h2 className="text-2xl font-bold text-gray-800 mb-3 pl-4 border-l-8" style={{ borderColor: BRAND_COLOR, background: 'rgba(234,89,17,0.04)' }}>
                        Ce qui nous rend uniques
                    </h2>
                    <ul className="list-disc list-inside space-y-3 text-lg text-gray-700 pl-2 leading-loose">
                        <li><span style={{ color: BRAND_COLOR, fontWeight: 600 }}>Une plateforme 100% tunisienne</span>, enracinée dans le terrain.</li>
                        <li>Un modèle inspiré des meilleures pratiques internationales (Airbnb, Booking, etc.) mais adapté à nos réalités.</li>
                        <li>Une vision future&nbsp;: intégrer bientôt des activités et expériences locales (kayak, randonnée, etc.).</li>
                    </ul>
                </section>

                <div className="w-full flex justify-center mb-10">
                    <div style={{ background: BRAND_COLOR }} className="h-1 w-32 rounded-full opacity-30" />
                </div>

                <section>
                    <h2 className="text-2xl font-bold text-gray-800 mb-3 pl-4 border-l-8" style={{ borderColor: BRAND_COLOR, background: 'rgba(234,89,17,0.04)' }}>
                        Notre équipe
                    </h2>
                    <p className="text-lg text-gray-700 leading-loose text-justify">
                        Derrière <span style={{ color: BRAND_COLOR, fontWeight: 600 }}>Almindhar Booking</span>, une équipe dynamique et passionnée, issue de divers horizons&nbsp;: technologie, immobilier, tourisme, communication... Notre force, c&rsquo;est notre complémentarité.
                    </p>
                </section>
            </div>
        </div>
    );
} 