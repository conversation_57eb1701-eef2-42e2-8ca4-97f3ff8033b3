import { getDaysRemaining } from './formatters';

// Define the subscription type
export type Subscription = {
    id: string;
    listing_id: string;
    invoice_number: string;
    subscription_plan: string;
    start_date: string | null;
    end_date: string | null;
    payment_amount: number;
    is_trial: boolean;
    created_at: string;
    is_activated?: boolean;
    activation_date?: string | null;
    subscription_duration_days?: number | null;
    listings: {
        id: string;
        title: string;
        featured_image_url: string;
        status: string;
    };
};

// Define the subscription status type
export type SubscriptionStatus = {
    status: 'active' | 'trial' | 'expired' | 'pending';
    statusText: string;
    variant: 'success' | 'secondary' | 'destructive' | 'warning';
};

// Define the subscription stats type
export type SubscriptionStats = {
    active: number;
    trial: number;
    expired: number;
    pending: number;
    total: number;
};

/**
 * Calculate the status of a subscription
 */
export const getSubscriptionStatus = (subscription: Subscription): SubscriptionStatus => {
    // Check if subscription is not activated yet
    if (subscription.is_activated === false) {
        return {
            status: 'pending',
            statusText: 'En attente',
            variant: 'warning'
        };
    }
    
    // For backwards compatibility with existing subscriptions that don't have the is_activated field
    if (subscription.is_activated === undefined && subscription.listings?.status === 'pending') {
        return {
            status: 'pending',
            statusText: 'En attente',
            variant: 'warning'
        };
    }

    // Handle case when end_date is null (not activated yet but not marked as pending)
    if (!subscription.end_date) {
        return {
            status: 'pending',
            statusText: 'En attente',
            variant: 'warning'
        };
    }

    const now = new Date();
    const endDate = new Date(subscription.end_date);

    if (now > endDate) {
        return {
            status: 'expired',
            statusText: 'Expiré',
            variant: 'destructive'
        };
    }

    if (subscription.is_trial) {
        return {
            status: 'trial',
            statusText: 'Essai',
            variant: 'secondary'
        };
    }

    return {
        status: 'active',
        statusText: 'Actif',
        variant: 'success'
    };
};

/**
 * Calculate summary statistics for a list of subscriptions
 */
export const calculateSubscriptionStats = (subscriptions: Subscription[]): SubscriptionStats => {
    const stats: SubscriptionStats = {
        active: 0,
        trial: 0,
        expired: 0,
        pending: 0,
        total: subscriptions.length
    };

    subscriptions.forEach(subscription => {
        const { status } = getSubscriptionStatus(subscription);
        stats[status]++;
    });

    return stats;
};

/**
 * Check if a subscription is active or valid (includes trial)
 */
export const isSubscriptionActive = (subscription: Subscription): boolean => {
    const { status } = getSubscriptionStatus(subscription);
    return status === 'active' || status === 'trial';
};