import React, { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { X, Clock } from 'lucide-react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import {
  modalSectionVariants,
  modalElementVariants,
  gridVariants,
  itemVariants
} from '../animationVariants';

interface SubscriptionPlansModalProps {
  isOpen: boolean;
  onClose: () => void;
  isProcessing: boolean;
  handleTrialStart: () => void;
  handleSubscriptionPurchase: (plan: string, duration: number) => void;
}

const SubscriptionPlansModal: React.FC<SubscriptionPlansModalProps> = ({
  isOpen,
  onClose,
  isProcessing,
  handleTrialStart,
  handleSubscriptionPurchase
}) => {
  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded bg-white shadow-xl transition-all max-h-[90vh] overflow-y-auto">
                <motion.div 
                  className="relative p-4 sm:p-6 md:p-8"
                  variants={modalSectionVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                >
                  <div className="absolute right-4 top-4">
                    <button
                      type="button"
                      className="text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-200 p-1.5 rounded-full"
                      onClick={onClose}
                    >
                      <X className="h-5 w-5" />
                    </button>
                  </div>

                  <motion.div variants={modalElementVariants}>
                    <div className="text-center mb-4">
                      <div className="flex justify-center mb-2">
                        <div className="w-14 h-14 flex items-center justify-center">
                          <Image
                            src="https://api.almindharbooking.com/storage/v1/object/public/add-listing//add_listing_cash.svg"
                            alt="Paiements en espèces"
                            width={64}
                            height={64}
                            className="object-contain"
                          />
                        </div>
                      </div>
                      <Dialog.Title as="h2" className="text-xl font-bold text-[#1A1A1A] mb-2">
                        Réservation flexible
                      </Dialog.Title>
                      <div className="w-20 h-1 bg-gray-200 mx-auto mb-4"></div>
                    </div>
                  </motion.div>

                  <motion.div variants={modalElementVariants}>
                    <div className="space-y-4">
                      <div className="bg-[#EA580F26] rounded border border-[#EA580F] overflow-hidden shadow-sm">
                        <div className="p-4 sm:p-6">
                          <div className="flex flex-col sm:flex-row sm:items-start sm:space-x-4">
                            <div className="flex-shrink-0 flex justify-center mb-3 sm:mb-0">
                              <Clock className="h-6 w-6 text-[#EA580F]" />
                            </div>
                            <div className="flex-1 text-center sm:text-left">
                              <h3 className="text-lg font-semibold text-gray-900">Essayez Gratuitement</h3>
                              <p className="mt-1 text-sm text-gray-500">Commencez avec un essai gratuit de 60 jours pour cette annonce. Aucun paiement requis.</p>
                              <button
                                className="mt-4 w-full sm:w-auto px-4 py-2 bg-[#EA580F] text-white text-sm font-medium rounded hover:bg-[#EA580F] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#EA580F] disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                onClick={handleTrialStart}
                                disabled={isProcessing}
                              >
                                {isProcessing ? (
                                  <span className="flex items-center justify-center">
                                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Traitement...
                                  </span>
                                ) : (
                                  "Commencer l'essai gratuit"
                                )}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>

                  <motion.div variants={modalElementVariants} className="relative my-4">
                    <div className="absolute inset-0 flex items-center" aria-hidden="true">
                      <div className="w-full border-t border-gray-200"></div>
                    </div>
                    <div className="relative flex justify-center">
                      <span className="px-2 bg-white text-sm text-gray-500">Ou choisissez un plan d&apos;abonnement</span>
                    </div>
                  </motion.div>

                  <motion.div
                    className="grid grid-cols-1 gap-4 sm:grid-cols-3"
                    variants={gridVariants}
                  >
                    {/* Monthly Plan */}
                    <motion.div variants={itemVariants} className="bg-white rounded border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                      <div className="p-4 sm:p-6">
                        <h3 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-1">30 <span className="text-base sm:text-lg font-semibold">TND</span></h3>
                        <p className="text-xs sm:text-sm text-gray-500 mb-3 sm:mb-4">/ 1 Mois</p>
                        <p className="text-xs sm:text-sm text-gray-600 mb-4 sm:mb-6">
                          Pour une première approche en toute liberté
                        </p>
                        <button
                          className="w-full py-2 sm:py-2.5 px-3 sm:px-4 bg-[#EA580F26] text-[#EA580F] border border-[#EA580F] text-xs sm:text-sm font-medium rounded hover:bg-[#EA580F] hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#EA580F] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          onClick={() => handleSubscriptionPurchase('monthly', 30)}
                          disabled={isProcessing}
                        >
                          {isProcessing ? (
                            <span className="flex items-center justify-center">
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Traitement...
                            </span>
                          ) : (
                            "COMMENCER"
                          )}
                        </button>
                      </div>
                    </motion.div>

                    {/* 6-Month Plan */}
                    <motion.div variants={itemVariants} className="bg-white rounded border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                      <div className="p-4 sm:p-6">
                        <h3 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-1">169 <span className="text-base sm:text-lg font-semibold">TND</span></h3>
                        <p className="text-xs sm:text-sm text-gray-500 mb-3 sm:mb-4">/ 6 Mois</p>
                        <p className="text-xs sm:text-sm text-gray-600 mb-4 sm:mb-6">
                          Pensé pour ceux qui veulent aller plus loin.
                        </p>
                        <button
                          className="w-full py-2 sm:py-2.5 px-3 sm:px-4 bg-[#EA580F26] text-[#EA580F] border border-[#EA580F] text-xs sm:text-sm font-medium rounded hover:bg-[#EA580F] hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#EA580F] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          onClick={() => handleSubscriptionPurchase('sixMonth', 180)}
                          disabled={isProcessing}
                        >
                          {isProcessing ? (
                            <span className="flex items-center justify-center">
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Traitement...
                            </span>
                          ) : (
                            "COMMENCER"
                          )}
                        </button>
                      </div>
                    </motion.div>

                    {/* Yearly Plan */}
                    <motion.div variants={itemVariants} className="bg-white rounded border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                      <div className="p-4 sm:p-6">
                        <h3 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-1">299 <span className="text-base sm:text-lg font-semibold">TND</span></h3>
                        <p className="text-xs sm:text-sm text-gray-500 mb-3 sm:mb-4">/ 12 Mois</p>
                        <p className="text-xs sm:text-sm text-gray-600 mb-4 sm:mb-6">
                          Le choix stratégique pour une rentabilité optimale.
                        </p>
                        <button
                          className="w-full py-2 sm:py-2.5 px-3 sm:px-4 bg-[#EA580F26] text-[#EA580F] border border-[#EA580F] text-xs sm:text-sm font-medium rounded hover:bg-[#EA580F] hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#EA580F] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          onClick={() => handleSubscriptionPurchase('yearly', 365)}
                          disabled={isProcessing}
                        >
                          {isProcessing ? (
                            <span className="flex items-center justify-center">
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Traitement...
                            </span>
                          ) : (
                            "COMMENCER"
                          )}
                        </button>
                      </div>
                    </motion.div>
                  </motion.div>
                </motion.div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default SubscriptionPlansModal; 