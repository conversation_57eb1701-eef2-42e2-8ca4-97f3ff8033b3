'use client';
import React from 'react';
import BankAccountCard from './BankAccountCard';
import ReservationInfoCard from './ReservationInfoCard';
import PriceDetailsCard from './PriceDetailsCard';

interface DefaultTransactionDetailsProps {
  transaction: any;
}

const DefaultTransactionDetails: React.FC<DefaultTransactionDetailsProps> = ({ 
  transaction 
}) => {
  const { booking, listing, guest, payoutMethod } = transaction;
  return (
    <div className="w-full max-w-md flex flex-col gap-6">
      <BankAccountCard payoutMethod={payoutMethod} />
      <ReservationInfoCard
        reservationNumber={booking?.reservationNumber}
        status={transaction.status}
        guestName={guest?.fullname}
        guestAvatarUrl={guest?.avatarUrl}
        startDate={booking?.startDate}
        endDate={booking?.endDate}
        listingTitle={listing?.title}
      />
      <PriceDetailsCard
        priceDetails={transaction.priceDetails}
        total={transaction.totalPrice}
      />
    </div>
  );
};

export default DefaultTransactionDetails; 