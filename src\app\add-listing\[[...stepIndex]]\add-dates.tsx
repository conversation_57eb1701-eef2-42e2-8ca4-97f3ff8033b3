"use client"

import React, { type FC, useState, useCallback, useMemo, useEffect, useRef } from "react"
import { useFormContext } from "@/app/add-listing/FormContext"
import DatesCalendar from "@/components/DatesCalendar"
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { XMarkIcon, ClockIcon, CheckCircleIcon, ExclamationTriangleIcon, TrashIcon, ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline'
import { format, isToday, isYesterday, isTomorrow, differenceInDays } from 'date-fns'
import { fr } from 'date-fns/locale'
import toast, { Toaster } from 'react-hot-toast'
import { validateICalUrl, processICalCalendar } from "@/utils/CalendarService"
import { v4 as uuidv4 } from 'uuid'
import CommonLayout from "./CommonLayout"
import { motion, AnimatePresence } from 'framer-motion'
import { Info } from "lucide-react"

export type AddDatesProps = {}

// Moved variants outside the component
const pageVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.5, ease: "easeOut", staggerChildren: 0.12 } },
};

const sectionChildVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4, ease: "easeOut" } },
};

const listWrapperVariants = {
    hidden: { opacity: 0, height: 0 },
    visible: { opacity: 1, height: 'auto', transition: { duration: 0.3, ease: "easeOut" } },
    exit: { opacity: 0, height: 0, transition: { duration: 0.3, ease: "easeOut" } },
};

const AddDates: FC<AddDatesProps> = () => {
    const { formData, setFormData, formErrors } = useFormContext()
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [isImportModalOpen, setIsImportModalOpen] = useState(false)
    const [useOtherPlatforms, setUseOtherPlatforms] = useState(false)
    const [onlyHere, setOnlyHere] = useState(true)
    const [selectedRange, setSelectedRange] = useState<[Date | null, Date | null]>([null, null])
    const [showUnblockConfirmation, setShowUnblockConfirmation] = useState(false)
    const [calendarName, setCalendarName] = useState('')
    const [calendarUrl, setCalendarUrl] = useState('')
    const [nameError, setNameError] = useState('')
    const [urlError, setUrlError] = useState('')
    const [isFormValid, setIsFormValid] = useState(false)
    const [isProcessing, setIsProcessing] = useState(false)
    const [processingError, setProcessingError] = useState('')
    const [importedCalendars, setImportedCalendars] = useState<{ id: string; name: string; url: string }[]>([])
    const [showImportedCalendars, setShowImportedCalendars] = useState(false)

    // State for iCal help accordion
    const [activeAccordionItem, setActiveAccordionItem] = useState<string | null>(null);
    const [expandedLongText, setExpandedLongText] = useState<{ [key: string]: boolean }>({});

    // Add a new state for custom name if 'Other' is selected
    const [customCalendarName, setCustomCalendarName] = useState('');

    // Update the calendar name options
    const calendarNameOptions = [
        { value: '', label: 'Sélectionnez une plateforme' },
        { value: 'Airbnb', label: 'Airbnb' },
        { value: 'Booking', label: 'Booking' },
        { value: 'Vrbo', label: 'Vrbo' },
        { value: 'Other', label: 'Autre' },
    ];

    const toggleAccordionItem = (id: string) => {
        setActiveAccordionItem(activeAccordionItem === id ? null : id);
    };

    const toggleLongText = (id: string) => {
        setExpandedLongText(prev => ({ ...prev, [id]: !prev[id] }));
    };

    const iCalHelpContent = [
        {
            id: 'airbnb',
            title: (
                <span className="flex items-center gap-2">
                    <Info size={16} className="flex-shrink-0" color="#EA580F" />
                    Airbnb – Exporter le lien iCal
                </span>
            ),
            shortInstructions: (
                <ul className="list-disc pl-5 space-y-1 text-xs text-neutral-700">
                    <li>Accédez à <strong>Calendrier</strong> &gt; <strong>Paramètres</strong> &gt; <strong>Synchronisation des calendriers</strong> dans votre annonce.</li>
                    <li>Cliquez sur <strong>Exporter le calendrier</strong> et copiez le lien iCal.</li>
                </ul>
            ),
            longInstructions: (
                <ol className="list-decimal pl-5 space-y-2 text-xs text-neutral-700 mt-2">
                    <li>Connectez-vous à votre compte Airbnb.</li>
                    <li>Allez dans l&apos;onglet <strong>Annonces</strong>, puis sélectionnez l&apos;annonce concernée.</li>
                    <li>Cliquez sur <strong>Disponibilité</strong>.</li>
                    <li>Faites défiler jusqu&apos;à <strong>Synchroniser les calendriers</strong> et cliquez sur <strong>Exporter le calendrier</strong>.</li>
                    <li>Copiez le lien URL iCal qui s&apos;affiche.</li>
                    <li>Collez ce lien dans le champ URL de la plateforme avec laquelle vous souhaitez synchroniser.</li>
                </ol>
            )
        },
        {
            id: 'booking',
            title: (
                <span className="flex items-center gap-2">
                    <Info size={16} className="flex-shrink-0" color="#EA580F" />
                    Booking.com – Exporter le lien iCal
                </span>
            ),
            shortInstructions: (
                <ul className="list-disc pl-5 space-y-1 text-xs text-neutral-700">
                    <li>Dans l&apos;Extranet, allez dans <strong>Calendrier</strong> &gt; <strong>Synchroniser les calendriers</strong>.</li>
                    <li>Cliquez sur <strong>Exporter le calendrier</strong> et copiez le lien iCal.</li>
                </ul>
            ),
            longInstructions: (
                <ol className="list-decimal pl-5 space-y-2 text-xs text-neutral-700 mt-2">
                    <li>Connectez-vous à votre Extranet Booking.com.</li>
                    <li>Cliquez sur l&apos;onglet <strong>Calendrier et tarifs</strong> ou <strong>Tarifs et disponibilités</strong>.</li>
                    <li>Sélectionnez <strong>Synchroniser les calendriers</strong>.</li>
                    <li>Cliquez sur <strong>Ajouter une connexion au calendrier</strong>.</li>
                    <li>Ignorez l&apos;étape &quot;Connectez votre calendrier externe&quot;, puis cliquez sur <strong>Passer cette étape</strong>.</li>
                    <li>Donnez un nom à votre connexion (ex: Mon calendrier Airbnb) et cliquez sur <strong>Exporter le calendrier</strong>.</li>
                    <li>Copiez le lien iCal fourni.</li>
                    <li>Collez-le sur la plateforme avec laquelle vous souhaitez synchroniser.</li>
                    <li><em>⚠️ Remarque : Si vous ne voyez pas cette option, contactez le support Booking.com.</em></li>
                </ol>
            )
        },
        {
            id: 'vrbo',
            title: (
                <span className="flex items-center gap-2">
                    <Info size={16} className="flex-shrink-0" color="#EA580F" />
                    Vrbo – Exporter le lien iCal
                </span>
            ),
            shortInstructions: (
                <ul className="list-disc pl-5 space-y-1 text-xs text-neutral-700">
                    <li>Allez dans <strong>Calendriers</strong> &gt; <strong>Importer et exporter le calendrier</strong>.</li>
                    <li>Cliquez sur <strong>Créer / Voir le lien iCal</strong> et copiez l&apos;URL iCal.</li>
                </ul>
            ),
            longInstructions: (
                <ol className="list-decimal pl-5 space-y-2 text-xs text-neutral-700 mt-2">
                    <li>Connectez-vous à votre espace propriétaire Vrbo.</li>
                    <li>Sélectionnez l&apos;annonce concernée.</li>
                    <li>Cliquez sur <strong>Calendrier</strong> dans le menu de gauche.</li>
                    <li>Sélectionnez <strong>Importer/Exporter</strong>.</li>
                    <li>Sous la section <strong>Exporter le calendrier</strong>, copiez l&apos;URL iCal affichée.</li>
                    <li>Collez-la dans le calendrier externe où vous souhaitez synchroniser les réservations.</li>
                </ol>
            )
        },
        {
            id: 'example',
            title: (
                <span className="flex items-center gap-2">
                    <Info size={16} className="flex-shrink-0" color="#EA580F" />
                    Exemple de flux de synchronisation
                </span>
            ),
            shortInstructions: (
                <div className="text-xs text-neutral-700 space-y-2">
                    <p>Vous avez une annonce sur Airbnb et sur Almindhar Booking :</p>
                    <ul className="list-disc pl-5 space-y-1">
                        <li>Exportez le calendrier iCal d&apos;Airbnb et importez-le sur Almindhar Booking.</li>
                        <li>Exportez le calendrier iCal d&apos;Almindhar Booking et importez-le sur Airbnb.</li>
                    </ul>
                    <p>Ainsi, lorsqu&apos;un voyageur réserve sur Airbnb :</p>
                    <ul className="list-disc pl-5 space-y-1">
                        <li>Cette réservation apparaît dans le flux iCal.</li>
                        <li>Almindhar Booking identifie ces dates comme bloquées et empêche les réservations durant cette période (et vice-versa).</li>
                    </ul>
                </div>
            ),
            longInstructions: null // No long version for example
        }
    ];

    const handleUseOtherPlatforms = () => {
        setUseOtherPlatforms(true)
        setOnlyHere(false)
    }

    const handleOnlyHere = () => {
        setOnlyHere(true)
        setUseOtherPlatforms(false)
    }

    const openModal = () => {
        setIsModalOpen(true)
    }

    const closeModal = () => {
        setIsModalOpen(false)
        setSelectedRange([null, null])
        setShowUnblockConfirmation(false)
    }

    const openImportModal = () => {
        setIsImportModalOpen(true)
    }

    const closeImportModal = () => {
        setIsImportModalOpen(false)
        setCalendarName('')
        setCalendarUrl('')
        setNameError('')
        setUrlError('')
    }

    const handleUnblockAll = () => {
        setShowUnblockConfirmation(true)
    }

    const confirmUnblockAll = () => {
        setFormData((prevData) => ({ ...prevData, blockedDates: [] }))
        // Note: Removed setCalendarKey increment to preserve calendar month navigation state
        setShowUnblockConfirmation(false)
    }

    const cancelUnblockAll = () => {
        setShowUnblockConfirmation(false)
    }

    const handleDateChange = useCallback(
        (start: Date | null, end: Date | null) => {
            if (start && end) {
                // Ensure start date is always before or equal to end date
                let actualStart = start;
                let actualEnd = end;

                // Swap dates if end is before start (allows selecting in reverse)
                if (end < start) {
                    actualStart = end;
                    actualEnd = start;
                }

                setSelectedRange([actualStart, actualEnd])

                // Get current blocked dates
                let updatedDates = [...(formData.blockedDates || [])]

                // Create a map for faster lookups
                const blockedMap = new Map();
                updatedDates.forEach(timestamp => blockedMap.set(timestamp, true));

                // Process each date in the range
                const newBlockedDates: number[] = [];
                const currentDate = new Date(actualStart);

                // Collect all dates in the range
                while (currentDate <= actualEnd) {
                    newBlockedDates.push(currentDate.getTime());
                    currentDate.setDate(currentDate.getDate() + 1);
                }

                // Determine if we're blocking or unblocking based on the first date
                const firstDateTimestamp = newBlockedDates[0];
                const isFirstDateBlocked = blockedMap.has(firstDateTimestamp);

                if (isFirstDateBlocked) {
                    // UNBLOCKING: Remove all selected dates from blocked dates
                    const newBlockedSet = new Set(newBlockedDates);
                    updatedDates = updatedDates.filter(timestamp => !newBlockedSet.has(timestamp));
                } else {
                    // BLOCKING: Add all selected dates to blocked dates
                    newBlockedDates.forEach(timestamp => {
                        if (!blockedMap.has(timestamp)) {
                            updatedDates.push(timestamp);
                        }
                    });
                }

                // Update form data with new blocked dates
                setFormData((prevData) => ({ ...prevData, blockedDates: updatedDates }))
                // Note: Removed setCalendarKey increment to preserve calendar month navigation state

                // Reset selection after a short delay
                setTimeout(() => {
                    setSelectedRange([null, null])
                }, 300)
            }
        },
        [formData.blockedDates, setFormData],
    )

    const renderDayContents = useCallback(
        (day: number, date: Date) => {
            const timestamp = date.getTime()
            const isBlocked = (formData.blockedDates || []).includes(timestamp)
            return <span className={`react-datepicker__day_span ${isBlocked ? "bg-[#EA580E] text-white" : ""}`}>{day}</span>
        },
        [formData.blockedDates],
    )

    // Calculate the number of blocked dates
    const blockedDatesCount = (formData.blockedDates || []).length

    // Generate summary text for blocked dates
    const blockedDatesSummary = useMemo(() => {
        if (blockedDatesCount === 0) {
            return null;
        } else if (blockedDatesCount === 1) {
            return "1 date est bloquée actuellement."
        } else {
            return `${blockedDatesCount} dates sont bloquées actuellement.`
        }
    }, [blockedDatesCount])

    // Refactor handleCalendarNameChange to accept a string value
    const handleCalendarNameChange = (val: string) => {
        setCalendarName(val);
        setNameError('');
        if (val !== 'Other') {
            setCustomCalendarName('');
        }
    };

    // Update validation logic
    const validateName = (name: string) => {
        if (!name || name === '') {
            setNameError('Veuillez sélectionner une plateforme');
            return false;
        }
        if (name === 'Other' && !customCalendarName.trim()) {
            setNameError('Le nom personnalisé est requis');
            return false;
        }
        setNameError('');
        return true;
    };

    // Validate URL field
    const validateUrl = (url: string) => {
        if (!url.trim()) {
            setUrlError("L'URL est requise")
            return false
        }

        // Simple URL validation - should start with http:// or https://
        if (!url.match(/^(http|https):\/\/.+/i)) {
            setUrlError("L'URL doit commencer par http:// ou https://")
            return false
        }

        setUrlError('')
        return true
    }

    // Handle URL field change
    const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newUrl = e.target.value
        setCalendarUrl(newUrl)
        validateUrl(newUrl)
    }

    // Update form validity check
    useEffect(() => {
        if (
            calendarName &&
            calendarName !== '' &&
            calendarUrl.trim() &&
            !nameError &&
            !urlError &&
            (calendarName !== 'Other' || customCalendarName.trim())
        ) {
            setIsFormValid(true);
        } else {
            setIsFormValid(false);
        }
    }, [calendarName, customCalendarName, calendarUrl, nameError, urlError]);

    // Update handleImport to use the correct name
    const handleImport = async () => {
        const isNameValid = validateName(calendarName);
        const isUrlValid = validateUrl(calendarUrl);
        if (isNameValid && isUrlValid) {
            setIsProcessing(true);
            setProcessingError('');
            try {
                const validationResult = await validateICalUrl(calendarUrl);
                if (!validationResult.isValid) {
                    setUrlError(validationResult.message);
                    setIsProcessing(false);
                    return;
                }
                const result = await processICalCalendar(calendarUrl, calendarName === 'Other' ? customCalendarName : calendarName);
                if (!result.success) {
                    setProcessingError(`Une erreur est survenue lors de l'importation. Veuillez réessayer plus tard.`);
                    setIsProcessing(false);
                    return;
                }
                const newCalendar = {
                    id: result.calendarId || uuidv4(),
                    name: calendarName === 'Other' ? customCalendarName : calendarName,
                    url: calendarUrl
                };
                const updatedCalendars = [...(formData.importedCalendars || []), newCalendar];
                const currentBlockedDates = new Set(formData.blockedDates || []);
                if (result.blockedDates && result.blockedDates.length > 0) {
                    const validBlockedDates = result.blockedDates.filter(date => !isNaN(date) && typeof date === 'number');
                    validBlockedDates.forEach(date => currentBlockedDates.add(date));
                }
                const blockedDatesArray = Array.from(currentBlockedDates);
                setFormData(prevData => ({
                    ...prevData,
                    importedCalendars: updatedCalendars,
                    blockedDates: blockedDatesArray
                }));
                setImportedCalendars(updatedCalendars);
                // Note: Removed setCalendarKey increment to preserve calendar month navigation state
                toast.success("Calendrier importé avec succès");
                setCalendarName('');
                setCustomCalendarName('');
                setCalendarUrl('');
                setIsImportModalOpen(false);
                setShowImportedCalendars(true);
            } catch (error) {
                console.error('Error importing calendar:', error);
                setProcessingError("Une erreur est survenue lors de l'importation. Veuillez réessayer plus tard.");
            } finally {
                setIsProcessing(false);
            }
        }
    };

    // Load imported calendars from form data when component mounts
    useEffect(() => {
        if (formData.importedCalendars && formData.importedCalendars.length > 0) {
            setImportedCalendars(formData.importedCalendars);
            setShowImportedCalendars(true);

            // If we have imported calendars, set useOtherPlatforms to true
            if (!useOtherPlatforms) {
                setUseOtherPlatforms(true);
                setOnlyHere(false);
            }
        }
    }, [formData.importedCalendars]);

    // Function to remove a calendar
    const handleRemoveCalendar = async (calendarId: string) => {
        // Find the calendar to remove
        const calendarToRemove = importedCalendars.find(cal => cal.id === calendarId)

        if (!calendarToRemove) return

        // Filter out the calendar from the list
        const updatedCalendars = importedCalendars.filter(cal => cal.id !== calendarId)

        // Update form data - preserve blocked dates
        setFormData(prevData => ({
            ...prevData,
            importedCalendars: updatedCalendars
            // We intentionally don't clear blockedDates -
            // once dates are blocked, they stay blocked even if the calendar is removed
        }))

        // Update local state
        setImportedCalendars(updatedCalendars)

        // Show success message
        toast.success(`Calendrier "${calendarToRemove.name}" supprimé`)

        // If we've removed all calendars, hide the imported calendars panel
        if (updatedCalendars.length === 0) {
            setShowImportedCalendars(false)
        }
    }

    // Custom dropdown for calendar name selection
    function CalendarNameDropdown({ value, onChange, options, disabled }: {
        value: string;
        onChange: (val: string) => void;
        options: { value: string; label: string }[];
        disabled?: boolean;
    }) {
        const [open, setOpen] = useState(false);
        const buttonRef = useRef<HTMLButtonElement>(null);
        const listRef = useRef<HTMLUListElement>(null);
        const [highlighted, setHighlighted] = useState<number>(-1);
        const [dropdownWidth, setDropdownWidth] = useState<number | undefined>(undefined);

        useEffect(() => {
            if (open && buttonRef.current) {
                const rect = buttonRef.current.getBoundingClientRect();
                setDropdownWidth(rect.height > 0 ? rect.width : undefined);
            }
        }, [open]);

        useEffect(() => {
            if (!open) return;
            function handleClick(event: MouseEvent) {
                const target = event.target as Node;
                if (
                    buttonRef.current && !buttonRef.current.contains(target) &&
                    listRef.current && !listRef.current.contains(target)
                ) {
                    setOpen(false);
                }
            }
            document.addEventListener('mousedown', handleClick);
            return () => document.removeEventListener('mousedown', handleClick);
        }, [open]);

        const handleSelect = (val: string) => {
            if (val === '') return; // don't allow selecting placeholder
            onChange(val);
            setOpen(false);
            setHighlighted(-1);
            buttonRef.current?.focus();
        };

        const handleKeyDown = (e: React.KeyboardEvent) => {
            if (!open) {
                if (e.key === 'ArrowDown' || e.key === 'Enter' || e.key === ' ') {
                    setOpen(true);
                    setHighlighted(0);
                    e.preventDefault();
                }
                return;
            }
            if (e.key === 'ArrowDown') {
                setHighlighted(h => Math.min(h + 1, options.length - 1));
                e.preventDefault();
            } else if (e.key === 'ArrowUp') {
                setHighlighted(h => Math.max(h - 1, 0));
                e.preventDefault();
            } else if (e.key === 'Enter' || e.key === ' ') {
                if (highlighted > 0) handleSelect(options[highlighted].value); // skip placeholder
                e.preventDefault();
            } else if (e.key === 'Escape') {
                setOpen(false);
                setHighlighted(-1);
                buttonRef.current?.focus();
                e.preventDefault();
            }
        };

        const selectedLabel = options.find(opt => opt.value === value)?.label || options[0].label;

        return (
            <div className="relative" tabIndex={-1}>
                <button
                    ref={buttonRef}
                    type="button"
                    className={`w-full rounded-md border border-neutral-300  px-4 text-base text-left bg-white text-neutral-900 focus:outline-none focus:border-[#EA580F] transition flex items-center justify-between ${disabled ? 'opacity-60 cursor-not-allowed' : ''}`}
                    aria-haspopup="listbox"
                    aria-expanded={open}
                    onClick={() => !disabled && setOpen(o => !o)}
                    onKeyDown={handleKeyDown}
                    disabled={disabled}
                >
                    <span className={value === '' ? 'text-neutral-400' : ''}>{selectedLabel}</span>
                    <ChevronDownIcon className="w-5 h-5 ml-2 text-[#EA580F]" />
                </button>
                {open && (
                    <ul
                        ref={listRef}
                        tabIndex={-1}
                        role="listbox"
                        className="absolute z-50 max-h-60 overflow-auto rounded-md border border-neutral-200 bg-white shadow-lg mt-1"
                        style={{ width: dropdownWidth, left: 0, top: '100%' }}
                        onKeyDown={handleKeyDown}
                    >
                        {options.map((opt, i) => {
                            const isSelected = value === opt.value;
                            const isHighlighted = highlighted === i;
                            const isPlaceholder = opt.value === '';
                            return (
                                <li
                                    key={opt.value}
                                    role="option"
                                    aria-selected={isSelected}
                                    className={`px-4 py-2 cursor-pointer select-none transition-colors text-base ${isPlaceholder ? 'text-neutral-400 cursor-default' : isSelected ? 'bg-[#EA580F] text-white' : isHighlighted ? 'bg-[#EA580F26] text-[#EA580F]' : 'text-neutral-900'} ${isHighlighted && !isPlaceholder ? 'font-semibold' : ''}`}
                                    onMouseEnter={() => setHighlighted(i)}
                                    onMouseLeave={() => setHighlighted(-1)}
                                    onClick={() => handleSelect(opt.value)}
                                    aria-disabled={isPlaceholder}
                                >
                                    {opt.label}
                                </li>
                            );
                        })}
                    </ul>
                )}
            </div>
        );
    }

    return (
        <CommonLayout params={{ stepIndex: 'add-dates' }}>
            <motion.div
                className="max-w-3xl mx-auto px-4 py-2 sm:py-16 flex flex-col"
                variants={pageVariants}
                initial="hidden"
                animate="visible"
            >
                {/* Header Section */}
                <motion.div className="mb-8" variants={sectionChildVariants}>
                    <h2 className="text-2xl md:text-3xl font-bold text-neutral-900 mb-3 2xl:pt-20">Quand votre logement est-il disponible ?</h2>
                    <p className="text-neutral-600 text-base md:text-lg font-medium">Synchronisez votre calendrier ou bloquez des dates pour éviter les doubles réservations.</p>
                </motion.div>

                {/* Platform Sync Section */}
                <motion.div className="mb-10" variants={sectionChildVariants}>
                    <h3 className="text-lg font-medium text-neutral-900 mb-8">Utilisez-vous Airbnb, Booking.com ou un calendrier pour vos réservations ?</h3>
                    <div className="space-y-5">
                        <div className="flex items-center space-x-3">
                            <div
                                className={`w-5 h-5 flex-shrink-0 rounded-sm border ${useOtherPlatforms ? 'bg-primary border-primary' : 'border-neutral-300'} flex items-center justify-center cursor-pointer`}
                                onClick={handleUseOtherPlatforms}
                            >
                                {useOtherPlatforms && (
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                )}
                            </div>
                            <label
                                className="text-base text-neutral-700 cursor-pointer"
                                onClick={handleUseOtherPlatforms}
                            >
                                Oui, je prends aussi des réservations ailleurs.
                            </label>
                        </div>
                        <div className="flex items-center space-x-3">
                            <div
                                className={`w-5 h-5 flex-shrink-0 rounded-sm border ${onlyHere ? 'bg-primary border-primary' : 'border-neutral-300'} flex items-center justify-center cursor-pointer`}
                                onClick={handleOnlyHere}
                            >
                                {onlyHere && (
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                )}
                            </div>
                            <label
                                className="text-base text-neutral-700 cursor-pointer"
                                onClick={handleOnlyHere}
                            >
                                Non, toutes mes réservations passent par Almindhar Booking.
                            </label>
                        </div>
                    </div>
                </motion.div>

                {/* Calendar Sync/Import Section */}
                <motion.div className="mb-10" variants={sectionChildVariants}>
                    <h3 className="text-lg font-medium text-neutral-900 mb-2">Synchronisation automatique de vos calendriers.</h3>
                    <p className="text-neutral-600 text-base md:text-lg font-medium mb-5">La synchronisation évite les doubles réservations et augmente les chances.</p>
                    <AnimatePresence>
                        {showImportedCalendars && importedCalendars.length > 0 && (
                            <motion.div
                                className="mb-5"
                                variants={listWrapperVariants}
                                initial="hidden"
                                animate="visible"
                                exit="exit"
                            >
                                <h4 className="text-sm font-medium text-neutral-800 mb-2">Calendriers importés</h4>
                                <div className="space-y-3">
                                    {importedCalendars.map((calendar) => (
                                        <div
                                            key={calendar.id}
                                            className="p-3 bg-white rounded-lg border border-neutral-200 shadow-sm flex items-center justify-between"
                                        >
                                            <div className="flex items-center space-x-3">
                                                <div>
                                                    <p className="text-sm font-medium text-neutral-900">{calendar.name}</p>
                                                    <p className="text-xs text-neutral-500 truncate max-w-xs">
                                                        {calendar.url}
                                                    </p>
                                                </div>
                                            </div>
                                            <button
                                                onClick={() => handleRemoveCalendar(calendar.id)}
                                                className="text-neutral-400 hover:text-red-500 transition-colors"
                                                title="Supprimer le calendrier"
                                            >
                                                <TrashIcon className="h-5 w-5" />
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            </motion.div>
                        )}
                    </AnimatePresence>
                    <div className="mt-10">
                        {useOtherPlatforms && (
                            <button
                                className="group px-6 py-3 border border-[#EA580E] rounded-[4px] text-[#EA580E] hover:bg-[#EA580E] hover:text-white transition-colors duration-200 flex items-center font-medium"
                                onClick={openImportModal}
                            >
                                <span>Connecter mon calendrier</span>
                            </button>
                        )}
                        {onlyHere && (
                            <button
                                onClick={openModal}
                                className="group px-6 py-3 border border-[#EA580E] rounded-[4px] text-[#EA580E] hover:bg-[#EA580E] hover:text-white transition-colors duration-200 flex items-center font-medium"
                            >
                                <span>Dates bloquées</span>
                                {blockedDatesCount > 0 && (
                                    <span className="ml-2 bg-[#EA580E] text-white text-xs rounded-full px-2 py-0.5 group-hover:ring-1 group-hover:ring-white">
                                        {blockedDatesCount}
                                    </span>
                                )}
                            </button>
                        )}
                    </div>
                </motion.div>

                {/* Calendar Modal */}
                <Transition appear show={isModalOpen} as={Fragment}>
                    <Dialog as="div" className="relative z-50" onClose={closeModal}>
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0"
                            enterTo="opacity-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100"
                            leaveTo="opacity-0"
                        >
                            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
                        </Transition.Child>

                        <div className="fixed inset-0 overflow-y-auto">
                            <div className="flex min-h-full items-center justify-center p-4 text-center">
                                <Transition.Child
                                    as={Fragment}
                                    enter="ease-out duration-300"
                                    enterFrom="opacity-0 scale-95"
                                    enterTo="opacity-100 scale-100"
                                    leave="ease-in duration-200"
                                    leaveFrom="opacity-100 scale-100"
                                    leaveTo="opacity-0 scale-95"
                                >
                                    <Dialog.Panel className="w-full max-w-3xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                        <div className="flex justify-between items-center mb-5">
                                            <Dialog.Title
                                                as="h3"
                                                className="text-xl font-medium leading-6 text-neutral-900"
                                            >
                                                Gérer les dates bloquées
                                            </Dialog.Title>
                                            <button
                                                type="button"
                                                className="text-neutral-500 hover:text-neutral-700 transition-colors"
                                                onClick={closeModal}
                                            >
                                                <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                                            </button>
                                        </div>

                                        <div className="mt-2">
                                            <div className="flex items-center justify-between mb-4">
                                                <p className="text-sm text-neutral-600">
                                                    Sélectionnez une plage de dates pour la bloquer ou la débloquer. Les dates bloquées ne seront pas disponibles pour les réservations.
                                                </p>
                                            </div>

                                            <div className="mb-4 p-3 bg-neutral-50 border border-neutral-200 rounded-lg text-sm text-neutral-700">
                                                <p className="mb-1 font-medium">Comment utiliser le calendrier :</p>
                                                <ul className="list-disc pl-5 space-y-1">
                                                    <li>Sélectionnez une date de début et une date de fin pour définir une plage</li>
                                                    <li>Pour bloquer/débloquer un seul jour, cliquez deux fois sur le même jour</li>
                                                    <li>Si toutes les dates de la plage sont déjà bloquées, elles seront débloquées</li>
                                                    <li>Si certaines dates de la plage ne sont pas bloquées, toutes seront bloquées</li>
                                                </ul>
                                            </div>

                                            {showUnblockConfirmation && (
                                                <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                                                    <p className="text-sm text-yellow-800 mb-3">
                                                        Êtes-vous sûr de vouloir débloquer toutes les dates ? Cette action ne peut pas être annulée.
                                                    </p>
                                                    <div className="flex space-x-3">
                                                        <button
                                                            onClick={confirmUnblockAll}
                                                            className="px-3 py-1.5 bg-[#EA580E] text-white text-sm rounded-md hover:bg-[#D45007] transition-colors"
                                                        >
                                                            Oui, tout débloquer
                                                        </button>
                                                        <button
                                                            onClick={cancelUnblockAll}
                                                            className="px-3 py-1.5 bg-white border border-neutral-300 text-neutral-700 text-sm rounded-md hover:bg-neutral-50 transition-colors"
                                                        >
                                                            Annuler
                                                        </button>
                                                    </div>
                                                </div>
                                            )}

                                            <div className="flex flex-col mb-4">
                                                {blockedDatesCount > 0 && (
                                                    <div>
                                                        <button
                                                            onClick={handleUnblockAll}
                                                            className="px-4 py-2 border border-[#EA580E] text-[#EA580E] rounded-[4px] text-sm hover:bg-[#EA580E] hover:text-white transition-colors"
                                                        >
                                                            Tout débloquer
                                                        </button>
                                                        <div className="text-sm font-medium text-[#EA580E] mt-2">
                                                            {blockedDatesSummary}
                                                        </div>
                                                    </div>
                                                )}
                                            </div>

                                            <div className="addListingDatePickerExclude border border-neutral-200 rounded-xl p-4 shadow-sm">
                                                <DatesCalendar
                                                    className="w-full"
                                                    fieldClassName="hidden"
                                                    onDateChange={handleDateChange}
                                                    renderDayContents={renderDayContents}
                                                    blockedDates={(formData.blockedDates || []).map((timestamp) => new Date(timestamp))}
                                                    isOpenPicker={true}
                                                />
                                            </div>

                                            {formErrors.blockedDates && (
                                                <p className="text-red-500 text-sm mt-2 p-2 bg-red-50 rounded-md border border-red-100">
                                                    {formErrors.blockedDates}
                                                </p>
                                            )}
                                        </div>

                                        <div className="mt-6 flex justify-end">
                                            <button
                                                type="button"
                                                className="inline-flex justify-center rounded-[4px] border border-transparent bg-[#EA580E] px-5 py-2.5 text-sm font-medium text-white hover:bg-[#D45007] focus:outline-none focus-visible:ring-2 focus-visible:ring-[#EA580E] focus-visible:ring-offset-2 transition-colors duration-200"
                                                onClick={closeModal}
                                            >
                                                Terminé
                                            </button>
                                        </div>
                                    </Dialog.Panel>
                                </Transition.Child>
                            </div>
                        </div>
                    </Dialog>
                </Transition>

                {/* Calendar Import Modal */}
                <Transition appear show={isImportModalOpen} as={Fragment}>
                    <Dialog as="div" className="relative z-50" onClose={closeImportModal}>
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0"
                            enterTo="opacity-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100"
                            leaveTo="opacity-0"
                        >
                            <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
                        </Transition.Child>

                        <div className="fixed inset-0 overflow-y-auto">
                            <div className="flex min-h-full items-center justify-center p-4 text-center">
                                <Transition.Child
                                    as={Fragment}
                                    enter="ease-out duration-300"
                                    enterFrom="opacity-0 scale-95"
                                    enterTo="opacity-100 scale-100"
                                    leave="ease-in duration-200"
                                    leaveFrom="opacity-100 scale-100"
                                    leaveTo="opacity-0 scale-95"
                                >
                                    <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                                        <div className="relative">
                                            <button
                                                type="button"
                                                className="absolute right-4 top-4 text-gray-400 hover:text-gray-500 focus:outline-none"
                                                onClick={closeImportModal}
                                                disabled={isProcessing}
                                            >
                                                <XMarkIcon className="h-5 w-5" aria-hidden="true" />
                                            </button>

                                            <div className="p-6">
                                                <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                                                    Importer le calendrier
                                                </Dialog.Title>

                                                <div className="mt-3">
                                                    <p className="text-sm text-gray-500">
                                                        Importez les dates réservées et non disponibles depuis Booking.com, Airbnb, Google et autres calendriers que vous utilisez.
                                                    </p>
                                                </div>

                                                {processingError && (
                                                    <div className="mt-4 p-3 bg-red-50 border border-red-100 rounded-md">
                                                        <p className="text-sm text-red-600">{processingError}</p>
                                                    </div>
                                                )}

                                                <div className="mt-6 space-y-4">
                                                    <div>
                                                        <label htmlFor="calendar-name" className="block text-sm font-medium text-gray-700">
                                                            Nom <span className="text-gray-400">*</span>
                                                        </label>
                                                        <CalendarNameDropdown
                                                            value={calendarName}
                                                            onChange={handleCalendarNameChange}
                                                            options={calendarNameOptions}
                                                            disabled={isProcessing}
                                                        />
                                                        {calendarName === 'Other' && (
                                                            <input
                                                                type="text"
                                                                id="custom-calendar-name"
                                                                className={`mt-2 block w-full rounded-md shadow-sm sm:text-sm ${nameError ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-[#EA580E] focus:ring-[#EA580E]'}`}
                                                                placeholder="Nom personnalisé"
                                                                value={customCalendarName}
                                                                onChange={(e) => setCustomCalendarName(e.target.value)}
                                                                onBlur={() => validateName(calendarName)}
                                                                disabled={isProcessing}
                                                            />
                                                        )}
                                                        {nameError && (
                                                            <p className="mt-1 text-sm text-red-600">{nameError}</p>
                                                        )}
                                                    </div>

                                                    <div>
                                                        <label htmlFor="calendar-url" className="block text-sm font-medium text-gray-700">
                                                            Adresse du calendrier iCal (URL) <span className="text-gray-400">*</span>
                                                        </label>
                                                        <input
                                                            type="text"
                                                            id="calendar-url"
                                                            className={`mt-1 block w-full rounded-md shadow-sm sm:text-sm ${urlError
                                                                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                                                                : 'border-gray-300 focus:border-[#EA580E] focus:ring-[#EA580E]'
                                                                }`}
                                                            placeholder="https://..."
                                                            value={calendarUrl}
                                                            onChange={handleUrlChange}
                                                            onBlur={() => validateUrl(calendarUrl)}
                                                            disabled={isProcessing}
                                                        />
                                                        {urlError && (
                                                            <p className="mt-1 text-sm text-red-600">{urlError}</p>
                                                        )}
                                                    </div>

                                                    <div className="mt-2">
                                                        <p className="text-sm text-gray-500">
                                                            Vous ne savez pas comment obtenir l&apos;URL de votre calendrier ?
                                                            <a href="#" className="text-[#EA580E] hover:underline ml-1">
                                                                Lisez notre guide
                                                            </a>
                                                        </p>
                                                    </div>

                                                    <div style={{ backgroundColor: '#EA580F26' }} className="border border-neutral-200 rounded-md p-3">
                                                        <h4 className="text-sm font-medium text-neutral-900 mb-3">Comment trouver votre URL iCal</h4>
                                                        <div className="space-y-2">
                                                            {iCalHelpContent.map((item) => (
                                                                <div key={item.id} className="border-b border-orange-200 last:border-b-0 py-2 last:pb-0 first:pt-0">
                                                                    <button
                                                                        onClick={() => toggleAccordionItem(item.id)}
                                                                        className="flex justify-between items-center w-full text-sm font-medium text-neutral-800 hover:text-neutral-900 focus:outline-none py-1"
                                                                    >
                                                                        <span>{item.title}</span>
                                                                        {activeAccordionItem === item.id ? (
                                                                            <ChevronUpIcon className="h-4 w-4" />
                                                                        ) : (
                                                                            <ChevronDownIcon className="h-4 w-4" />
                                                                        )}
                                                                    </button>
                                                                    <AnimatePresence initial={false}>
                                                                        {activeAccordionItem === item.id && (
                                                                            <motion.div
                                                                                initial={{ height: 0, opacity: 0, marginTop: 0 }}
                                                                                animate={{ height: 'auto', opacity: 1, marginTop: '0.5rem' }}
                                                                                exit={{ height: 0, opacity: 0, marginTop: 0 }}
                                                                                transition={{ duration: 0.3, ease: "easeInOut" }}
                                                                                className="overflow-hidden"
                                                                            >
                                                                                <div className="pb-2">
                                                                                    {item.shortInstructions}
                                                                                    {item.longInstructions && (
                                                                                        <div className="mt-2">
                                                                                            <button
                                                                                                onClick={() => toggleLongText(item.id)}
                                                                                                className="text-xs text-[#EA580E] hover:underline font-semibold focus:outline-none"
                                                                                            >
                                                                                                {expandedLongText[item.id] ? "Voir moins" : "Voir plus"}
                                                                                            </button>
                                                                                            <AnimatePresence initial={false}>
                                                                                                {expandedLongText[item.id] && (
                                                                                                    <motion.div
                                                                                                        initial={{ height: 0, opacity: 0, marginTop: 0 }}
                                                                                                        animate={{ height: 'auto', opacity: 1, marginTop: '0.5rem' }}
                                                                                                        exit={{ height: 0, opacity: 0, marginTop: 0 }}
                                                                                                        transition={{ duration: 0.3, ease: "easeInOut" }}
                                                                                                        className="overflow-hidden"
                                                                                                    >
                                                                                                        <div className="mt-2">
                                                                                                            {item.longInstructions}
                                                                                                        </div>
                                                                                                    </motion.div>
                                                                                                )}
                                                                                            </AnimatePresence>
                                                                                        </div>
                                                                                    )}
                                                                                </div>
                                                                            </motion.div>
                                                                        )}
                                                                    </AnimatePresence>
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="mt-6">
                                                    <button
                                                        type="button"
                                                        className={`w-full rounded-[4px] border border-transparent px-4 py-2 text-sm font-medium text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 ${isProcessing
                                                            ? 'bg-gray-400 cursor-not-allowed'
                                                            : isFormValid
                                                                ? 'bg-[#EA580E] hover:bg-[#D45007] focus:ring-[#EA580E]'
                                                                : 'bg-[#EA580E] opacity-70 cursor-not-allowed'
                                                            }`}
                                                        onClick={isFormValid && !isProcessing ? handleImport : undefined}
                                                        disabled={!isFormValid || isProcessing}
                                                    >
                                                        {isProcessing ? (
                                                            <span className="flex items-center justify-center">
                                                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                                </svg>
                                                                Traitement en cours...
                                                            </span>
                                                        ) : (
                                                            "Importer"
                                                        )}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </Dialog.Panel>
                                </Transition.Child>
                            </div>
                        </div>
                    </Dialog>
                </Transition>
            </motion.div>
        </CommonLayout>
    );
}

export default AddDates;

