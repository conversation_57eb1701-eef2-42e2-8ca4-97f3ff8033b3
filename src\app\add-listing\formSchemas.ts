import * as Yup from 'yup';
//TODO: Add validation for each step
export const stepSchemas: { [key: string]: Yup.ObjectSchema<any> } = {
    "add-categories": Yup.object({
        categoryId: Yup.string().required("Veuillez sélectionner une catégorie."),
    }),
    "add-type": Yup.object({
        propertyTypeId: Yup.string().required("Veuillez sélectionner une catégorie de logement."),
        roomTypeId: Yup.string().required("Veuillez sélectionner un type de chambre."),
    }),
    "add-location": Yup.object({
        propertyStateId: Yup.string().required("Veuillez sélectionner une région."),
        propertyCityId: Yup.string().required("Veuillez sélectionner une ville."),
        propertyCity: Yup.string().required("Veuillez sélectionner une ville."),
        propertyAddress: Yup.string().required("Veuillez saisir une adresse détaillée."),
        latitude: Yup.number().required("Veuillez sélectionner un emplacement sur la carte."),
        longitude: Yup.number().required("Veuillez sélectionner un emplacement sur la carte."),
    }),
    // Guests and rules step validation
    "add-guests-and-rules": Yup.object({
        numGuests: Yup.number()
            .required("Veuillez indiquer le nombre d'invités.")
            .min(1, "Le nombre d'invités doit être au moins 1."),
        numBedrooms: Yup.number()
            .required("Veuillez indiquer le nombre de chambres.")
            .min(1, "Le nombre de chambres doit être au moins 1."),
        numBeds: Yup.number()
            .required("Veuillez indiquer le nombre de lits.")
            .min(1, "Le nombre de lits doit être au moins 1."),
        numBathrooms: Yup.number()
            .required("Veuillez indiquer le nombre de salles de bain.")
            .min(1, "Le nombre de salles de bain doit être au moins 1."),
        numKitchens: Yup.number()
            .required("Veuillez indiquer le nombre de cuisines.")
            .min(0, "Le nombre de cuisines doit être au moins 0."),
        eventsAllowed: Yup.boolean().nullable(),
        smokingAllowed: Yup.boolean().nullable(),
        commercialPhotographyAllowed: Yup.boolean().nullable(),
        customRules: Yup.array().of(Yup.string()),
    }),
    "add-equipments": Yup.object({
        selectedAmenities: Yup.array().of(Yup.string()).min(1, "Veuillez sélectionner au moins un équipement."),
    }),
    "add-title-and-description": Yup.object({
        listingTitle: Yup.string()
            .required("Veuillez saisir un titre pour votre annonce.")
            .min(10, "Le titre doit contenir au moins 10 caractères.")
            .max(100, "Le titre ne doit pas dépasser 100 caractères."),
        listingDescription: Yup.string()
            .required("Veuillez saisir une description pour votre annonce.")
            .min(50, "La description doit contenir au moins 50 caractères.")
            .max(1000, "La description ne doit pas dépasser 1000 caractères."),
    }),
    "add-conditions": Yup.object({
        cancellationPolicyId: Yup.string().required("Veuillez sélectionner une politique d'annulation."),
    }),
    "add-price": Yup.object({
        nightlyRate: Yup.number()
            .required("Veuillez indiquer un prix de base par nuitée.")
            .min(1, "Le prix de base doit être supérieur à 0."),
        arrivalTime: Yup.string()
            .required("Veuillez indiquer l'heure d'arrivée."),
        departureTime: Yup.string()
            .required("Veuillez indiquer l'heure de départ."),
        // Optional fields with validation
        securityDeposit: Yup.number().nullable(),
        minStay: Yup.number()
            .required("Veuillez indiquer la durée minimale du séjour.")
            .min(1, "La durée minimale du séjour doit être au moins 1 nuit."),
        maxStay: Yup.number()
            .required("Veuillez indiquer la durée maximale du séjour.")
            .min(1, "La durée maximale du séjour doit être au moins 1 nuit.")
            .test(
                'max-gte-min',
                'La durée maximale doit être supérieure ou égale à la durée minimale.',
                function (value) {
                    const { minStay } = this.parent;
                    if (value == null || minStay == null) return true;
                    return value >= minStay;
                }
            ),
        // Explicitly mark additionalFees as optional with specific structure validation
        additionalFees: Yup.array().of(
            Yup.object({
                title: Yup.string()
                    .required("Le titre du frais est obligatoire.")
                    .min(4, "Le titre du frais doit contenir au moins 4 caractères."),
                price: Yup.number().nullable(),
                feeType: Yup.string().nullable(),
            })
        ).nullable(),
    }),
    "add-discount": Yup.object({
        weeklyDiscountPercent: Yup.number()
            .nullable()
            .min(0, "La remise hebdomadaire doit être ≥ 0%")
            .max(100, "La remise hebdomadaire doit être ≤ 100%"),
        monthlyDiscountPercent: Yup.number()
            .nullable()
            .min(0, "La remise mensuelle doit être ≥ 0%")
            .max(100, "La remise mensuelle doit être ≤ 100%"),
        firstBookingDiscountPercent: Yup.number()
            .nullable()
            .min(0, "La remise première réservation doit être ≥ 0%")
            .max(100, "La remise première réservation doit être ≤ 100%"),
    }),
    "add-dates": Yup.object({
        blockedDates: Yup.array().of(Yup.number()).nullable(),
        importedCalendars: Yup.array().of(
            Yup.object({
                id: Yup.string().required(),
                name: Yup.string().required(),
                url: Yup.string().required(),
                lastSynced: Yup.date().nullable(),
                syncStatus: Yup.string().notRequired(),
                errorMessage: Yup.string().notRequired(),
            })
        ).nullable(),
    }),
    "add-payments": Yup.object({
        selectedPaymentType: Yup.string()
            .oneOf(['secure', 'cash', 'subscription', 'trial'], "Veuillez sélectionner un mode de paiement.")
            .required("Veuillez sélectionner un mode de paiement."),
        paymentMethodInfo: Yup.mixed().when('selectedPaymentType', (selectedPaymentType, schema) => {
            if (typeof selectedPaymentType === 'string' && selectedPaymentType === 'secure') {
                return Yup.object().required("Veuillez renseigner les informations de paiement en ligne.");
            }
            return schema;
        }),
    }),
    // Add more schemas for subsequent steps as needed
};