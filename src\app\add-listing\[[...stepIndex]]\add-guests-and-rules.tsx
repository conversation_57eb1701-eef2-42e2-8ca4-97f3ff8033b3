'use client';

import NcInputNumber from "@/components/NcInputNumber";
import React, { FC, useState } from "react";
import { useFormContext } from "../FormContext";
import CommonLayout from "./CommonLayout";
import { motion } from "framer-motion";

export interface AddGuestsAndRulesProps { }

const AddGuestsAndRules: FC<AddGuestsAndRulesProps> = () => {
    const { formData, setFormData, formErrors, setFormErrors, validateStep } = useFormContext();
    // Local state for new custom rule input
    const [newRule, setNewRule] = useState("");
    const [customRuleError, setCustomRuleError] = useState("");

    const handleChange = (field: keyof typeof formData, value: number | boolean) => {
        setFormData((prev) => ({ ...prev, [field]: value }));
        setFormErrors({});
    };

    // Add a custom rule to formData.customRules
    const handleAddCustomRule = () => {
        const trimmed = newRule.trim();
        if (!trimmed) {
            setCustomRuleError("La règle ne peut pas être vide.");
            return;
        }
        if (trimmed.length > 100) {
            setCustomRuleError("La règle ne doit pas dépasser 100 caractères.");
            return;
        }
        if (formData.customRules?.includes(trimmed)) {
            setCustomRuleError("Cette règle existe déjà.");
            return;
        }
        setFormErrors({});
        setFormData(prev => ({
            ...prev,
            customRules: [...(prev.customRules || []), trimmed],
        }));
        setNewRule("");
        setCustomRuleError("");
        validateStep("add-guests-and-rules");
    };

    // Remove a custom rule by index
    const handleRemoveCustomRule = (idx: number) => {
        setFormErrors({});
        setFormData(prev => ({
            ...prev,
            customRules: (prev.customRules || []).filter((_, i) => i !== idx),
        }));
    };

    const sectionVariants = {
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: { staggerChildren: 0.07 } },
    };

    const rowVariants = {
        hidden: { opacity: 0, x: -50 },
        visible: { opacity: 1, x: 0, transition: { duration: 0.4, ease: "circOut" } },
    };

    return (
        <CommonLayout params={{ stepIndex: "add-guests-and-rules" }}>
            <motion.div
                className="max-w-xl mx-auto min-h-[70vh] flex flex-col justify-center py-8 2xl:pt-24 w-full "
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, ease: "easeOut" }}
            >
                {/* Main Header */}
                <h2 className="font-bold text-gray-900 text-2xl md:text-3xl text-left mb-2 px-2 md:px-0">
                    Parlez-nous un peu de votre logement !
                </h2>
                <p className="text-gray-500 text-base md:text-lg font-medium mb-6 px-2 md:px-0">
                    Précisez en avant les details de votre logement : les invités veulent savoir la capacité, les équipement et les règles à respecter de votre logement
                </p>
                {/* Section Subheader */}
                <div className="mb-6 px-2 md:px-0">
                    <span className="font-bold text-lg md:text-xl text-neutral-900">Personnes et chambres</span>
                </div>
                {/* Summary error for capacity fields */}
                {(formErrors.numGuests || formErrors.numBedrooms || formErrors.numBeds || formErrors.numBathrooms || formErrors.numKitchens) && (
                    <div className="mt-6 flex" role="alert" aria-live="assertive">
                        <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full ">
                            <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
                            </svg>
                            <span className="text-sm text-red-700 font-medium">
                                {formErrors.numGuests || formErrors.numBedrooms || formErrors.numBeds || formErrors.numBathrooms || formErrors.numKitchens}
                            </span>
                        </div>
                    </div>
                )}
                {/* Input Rows */}
                <motion.div
                    className="divide-y divide-gray-200"
                    variants={sectionVariants}
                    initial="hidden"
                    animate="visible"
                >
                    {/* Invités */}
                    <motion.div className="flex items-center justify-between py-4 px-4" variants={rowVariants}>
                        <span className="text-base md:text-lg font-medium text-gray-900">Invités</span>
                        <NcInputNumber
                            value={formData.numGuests ?? undefined}
                            defaultValue={0}
                            placeholder={"0"}
                            onChange={(value) => {
                                setFormData(prev => ({ ...prev, numGuests: value === undefined ? undefined : value }));
                                setFormErrors({});
                            }}
                            className="ml-4"
                        />
                    </motion.div>
                    {/* Chambres */}
                    <motion.div className="flex items-center justify-between py-4 px-4" variants={rowVariants}>
                        <span className="text-base md:text-lg font-medium text-gray-900">Chambres</span>
                        <NcInputNumber
                            value={formData.numBedrooms ?? undefined}
                            defaultValue={0}
                            placeholder={"0"}
                            onChange={(value) => {
                                setFormData(prev => ({ ...prev, numBedrooms: value === undefined ? undefined : value }));
                                setFormErrors({});
                            }}
                            className="ml-4"
                        />
                    </motion.div>
                    {/* Lits */}
                    <motion.div className="flex items-center justify-between py-4 px-4" variants={rowVariants}>
                        <span className="text-base md:text-lg font-medium text-gray-900">Lits</span>
                        <NcInputNumber
                            value={formData.numBeds ?? undefined}
                            defaultValue={0}
                            placeholder={"0"}
                            onChange={(value) => {
                                setFormData(prev => ({ ...prev, numBeds: value === undefined ? undefined : value }));
                                setFormErrors({});
                            }}
                            className="ml-4"
                        />
                    </motion.div>
                    {/* Salles de bain */}
                    <motion.div className="flex items-center justify-between py-4 px-4" variants={rowVariants}>
                        <span className="text-base md:text-lg font-medium text-gray-900">Salles de bain</span>
                        <NcInputNumber
                            value={formData.numBathrooms ?? undefined}
                            defaultValue={0}
                            placeholder={"0"}
                            onChange={(value) => {
                                setFormData(prev => ({ ...prev, numBathrooms: value === undefined ? undefined : value }));
                                setFormErrors({});
                            }}
                            className="ml-4"
                        />
                    </motion.div>
                    {/* Cuisines */}
                    <motion.div className="flex items-center justify-between py-4 px-4" variants={rowVariants}>
                        <span className="text-base md:text-lg font-medium text-gray-900">Cuisines</span>
                        <NcInputNumber
                            value={formData.numKitchens ?? undefined}
                            defaultValue={0}
                            placeholder={"0"}
                            onChange={(value) => {
                                setFormData(prev => ({ ...prev, numKitchens: value === undefined ? undefined : value }));
                                setFormErrors({});
                            }}
                            className="ml-4"
                        />
                    </motion.div>
                </motion.div>

                {/* House Rules Section */}
                <div className="mt-10">
                    <h3 className="font-bold text-lg md:text-xl text-neutral-900 mb-2 px-2 md:px-0">Quelles sont les règles à respecter ?</h3>
                    <motion.div
                        className="divide-y divide-gray-200"
                        variants={sectionVariants}
                        initial="hidden"
                        animate="visible"
                    >
                        {/* Événements autorisés */}
                        <motion.div className="flex items-center justify-between py-4 px-4" variants={rowVariants}>
                            <span className="text-base md:text-lg text-gray-900">Événements</span>
                            <div className="flex items-center space-x-3">
                                <button
                                    type="button"
                                    aria-pressed={formData.eventsAllowed === false}
                                    onClick={() => handleChange('eventsAllowed', false)}
                                    className={`w-8 h-8 flex items-center justify-center rounded-full border transition focus:outline-none ${formData.eventsAllowed === false ? 'border-red-600 text-red-600' : 'border-gray-300 text-gray-400'}`}
                                >
                                    <span className="text-base font-semibold">&#10006;</span>
                                </button>
                                <button
                                    type="button"
                                    aria-pressed={formData.eventsAllowed === true}
                                    onClick={() => handleChange('eventsAllowed', true)}
                                    className={`w-8 h-8 flex items-center justify-center rounded-full border transition focus:outline-none ${formData.eventsAllowed === true ? 'border-green-600 text-green-600' : 'border-gray-300 text-gray-400'}`}
                                >
                                    <span className="text-lg font-semibold">&#10003;</span>
                                </button>
                            </div>
                            {formErrors.eventsAllowed && (
                                <div className="mt-6 flex items-center justify-center" role="alert" aria-live="assertive">
                                    <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full max-w-md">
                                        <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
                                        </svg>
                                        <span className="text-sm text-red-700 font-medium">{formErrors.eventsAllowed}</span>
                                    </div>
                                </div>
                            )}
                        </motion.div>
                        {/* Fumeur */}
                        <motion.div className="flex items-center justify-between py-4 px-4" variants={rowVariants}>
                            <span className="text-base md:text-lg text-gray-900">Fumeur</span>
                            <div className="flex items-center space-x-3">
                                <button
                                    type="button"
                                    aria-pressed={formData.smokingAllowed === false}
                                    onClick={() => handleChange('smokingAllowed', false)}
                                    className={`w-8 h-8 flex items-center justify-center rounded-full border transition focus:outline-none ${formData.smokingAllowed === false ? 'border-red-600 text-red-600' : 'border-gray-300 text-gray-400'}`}
                                >
                                    <span className="text-base font-semibold">&#10006;</span>
                                </button>
                                <button
                                    type="button"
                                    aria-pressed={formData.smokingAllowed === true}
                                    onClick={() => handleChange('smokingAllowed', true)}
                                    className={`w-8 h-8 flex items-center justify-center rounded-full border transition focus:outline-none ${formData.smokingAllowed === true ? 'border-green-600 text-green-600' : 'border-gray-300 text-gray-400'}`}
                                >
                                    <span className="text-lg font-semibold">&#10003;</span>
                                </button>
                            </div>
                            {formErrors.smokingAllowed && (
                                <div className="mt-6 flex items-center justify-center" role="alert" aria-live="assertive">
                                    <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full max-w-md">
                                        <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
                                        </svg>
                                        <span className="text-sm text-red-700 font-medium">{formErrors.smokingAllowed}</span>
                                    </div>
                                </div>
                            )}
                        </motion.div>
                        {/* Photographie et tournage commerciaux autorisés */}
                        <motion.div className="flex items-center justify-between py-4 px-4" variants={rowVariants}>
                            <span className="text-base md:text-lg text-gray-900">Photographie et tournage commerciaux</span>
                            <div className="flex items-center space-x-3">
                                <button
                                    type="button"
                                    aria-pressed={formData.commercialPhotographyAllowed === false}
                                    onClick={() => handleChange('commercialPhotographyAllowed', false)}
                                    className={`w-8 h-8 flex items-center justify-center rounded-full border transition focus:outline-none ${formData.commercialPhotographyAllowed === false ? 'border-red-600 text-red-600' : 'border-gray-300 text-gray-400'}`}
                                >
                                    <span className="text-base font-semibold">&#10006;</span>
                                </button>
                                <button
                                    type="button"
                                    aria-pressed={formData.commercialPhotographyAllowed === true}
                                    onClick={() => handleChange('commercialPhotographyAllowed', true)}
                                    className={`w-8 h-8 flex items-center justify-center rounded-full border transition focus:outline-none ${formData.commercialPhotographyAllowed === true ? 'border-green-600 text-green-600' : 'border-gray-300 text-gray-400'}`}
                                >
                                    <span className="text-lg font-semibold">&#10003;</span>
                                </button>
                            </div>
                            {formErrors.commercialPhotographyAllowed && (
                                <div className="mt-6 flex items-center justify-center" role="alert" aria-live="assertive">
                                    <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full max-w-md">
                                        <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
                                        </svg>
                                        <span className="text-sm text-red-700 font-medium">{formErrors.commercialPhotographyAllowed}</span>
                                    </div>
                                </div>
                            )}
                        </motion.div>
                    </motion.div>
                    {/* Custom Rules Section */}
                    <div className="mt-6 px-2 md:px-0">
                        <label className="block text-base font-medium text-gray-900 mb-2">Ajouter une règle personnalisée</label>
                        <div className="flex gap-2 mb-2">
                            <input
                                type="text"
                                value={newRule}
                                onChange={e => { setNewRule(e.target.value); setCustomRuleError(""); }}
                                onKeyDown={e => { if (e.key === 'Enter') { e.preventDefault(); handleAddCustomRule(); } }}
                                maxLength={100}
                                className="flex-1 border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
                                placeholder="Ex: Pas de chaussures à l'intérieur"
                                aria-label="Ajouter une règle personnalisée"
                            />
                            <button
                                type="button"
                                onClick={handleAddCustomRule}
                                className="px-4 py-2 bg-[#EA580F] text-white rounded font-medium hover:bg-orange-600 transition-colors"
                            >
                                Ajouter
                            </button>
                        </div>
                        {customRuleError && <div className="text-red-500 text-sm mb-2">{customRuleError}</div>}
                        {/* List of custom rules */}
                        <ul className="space-y-2 mt-2">
                            {(formData.customRules || []).map((rule, idx) => (
                                <motion.li
                                    key={rule}
                                    initial={{ opacity: 0, x: 20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    exit={{ opacity: 0, x: -20 }}
                                    className="flex items-center justify-between bg-gray-50 border border-gray-200 rounded px-3 py-2"
                                >
                                    <span className="text-gray-800 text-base">{rule}</span>
                                    <button
                                        type="button"
                                        onClick={() => handleRemoveCustomRule(idx)}
                                        className="ml-3 text-red-500 hover:text-red-700 text-sm font-medium"
                                        aria-label={`Supprimer la règle: ${rule}`}
                                    >
                                        Supprimer
                                    </button>
                                </motion.li>
                            ))}
                        </ul>
                    </div>
                </div>
            </motion.div>
        </CommonLayout>
    );
};

export default AddGuestsAndRules;
