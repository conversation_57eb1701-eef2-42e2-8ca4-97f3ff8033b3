// Filter options for financials filters

const currentYear = new Date().getFullYear();
const previousYear = currentYear - 1;

export const periodOptions = [
    { label: 'Toutes les dates', value: 'all' },
    { label: '30 derniers jours', value: '30' },
    { label: '60 derniers jours', value: '60' },
    { label: '90 derniers jours', value: '90' },
    { label: previousYear.toString(), value: previousYear.toString() },
];

// Status options for filters (extracted from PAYOUT_STATUS_UI_MAP)
export const statusOptions = [
    { value: 'secured', label: 'Confirmer' },
    { value: 'paid', label: 'Effectué' },
    { value: 'refused', label: 'Refusé' },
    { value: 'on_hold', label: 'En attente' },
    { value: 'on_the_way', label: 'En cours' },
    { value: 'ready', label: 'Disponible' },
    { value: 'refunded', label: 'Rembours<PERSON>' },
];

// Add other shared filter options here as needed 