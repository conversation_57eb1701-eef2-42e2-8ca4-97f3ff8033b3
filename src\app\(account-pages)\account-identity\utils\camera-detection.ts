/**
 * Checks if the device has a camera
 * @returns Promise that resolves to true if camera is available, false otherwise
 */
export async function hasCamera(): Promise<boolean> {
    try {
        // Check if mediaDevices is supported
        if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
            console.log("mediaDevices API not supported in this browser")
            return false
        }

        // Get all media devices
        const devices = await navigator.mediaDevices.enumerateDevices()

        // Filter for video input devices
        const videoDevices = devices.filter((device) => device.kind === "videoinput")

        console.log(`Found ${videoDevices.length} video input devices`)

        if (videoDevices.length === 0) {
            return false
        }

        // Try to actually access the camera to confirm permissions
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ video: true })

            // Clean up the stream
            stream.getTracks().forEach((track) => track.stop())

            return true
        } catch (err) {
            console.error("Error accessing camera:", err)
            return false
        }
    } catch (error) {
        console.error("Error checking camera availability:", error)
        return false
    }
}

/**
 * Checks if the device is mobile
 * @returns boolean indicating if the device is mobile
 */
export function isMobileDevice(): boolean {
    if (typeof window === "undefined") return false

    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

/**
 * Gets the optimal camera constraints based on the device
 * @returns MediaStreamConstraints for video
 */
export function getOptimalCameraConstraints(): MediaStreamConstraints {
    const isMobile = isMobileDevice()

    if (isMobile) {
        return {
            video: {
                facingMode: "environment", // Use back camera on mobile
                width: { ideal: 1280 },
                height: { ideal: 720 },
            },
        }
    }

    return {
        video: {
            facingMode: "user", // Use front camera on desktop
            width: { ideal: 1280 },
            height: { ideal: 720 },
        },
    }
}
