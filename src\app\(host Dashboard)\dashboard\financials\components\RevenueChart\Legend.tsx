import React, { useEffect, useState } from 'react';
import { FinanceSummary } from '../../types/financeTypes';
import { formatTND } from '../../utils/format';

const COLORS = {
  paid: '#2563EB', // blue
  due: '#EA5911', // orange
};

const Legend = () => {
  const [summary, setSummary] = useState<FinanceSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    setError(null);
    fetch('/api/financials/summary')
      .then(res => {
        if (!res.ok) throw new Error('Erreur lors du chargement des données');
        return res.json();
      })
      .then(data => setSummary(data))
      .catch(() => setError('Erreur lors du chargement des données'))
      .finally(() => setLoading(false));
  }, []);

  return (
    <div className="flex flex-col gap-1 px-4 pt-4 pb-2 w-full">
      {/* Montant payé row */}
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center gap-2">
          <span className="w-3 h-3 rounded-full" style={{ background: COLORS.paid }} />
          <span className="text-sm text-[#2563EB] font-semibold">Montant payé</span>
        </div>
        <span className="text-base font-semibold text-black">
          {loading ? '...' : error ? '--' : formatTND(summary?.totalEarnings ?? 0)}
        </span>
      </div>
      {/* Divider */}
      <div className="w-full border-t border-gray-200 my-1" />
      {/* À venir row */}
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center gap-2">
          <span className="w-3 h-3 rounded-full" style={{ background: COLORS.due }} />
          <span className="text-sm text-[#EA5911] font-semibold">À venir</span>
        </div>
        <span className="text-base font-semibold text-black">
          {loading ? '...' : error ? '--' : formatTND(summary?.nextUpcomingAmount ?? 0)}
        </span>
      </div>
    </div>
  );
};

export default Legend; 