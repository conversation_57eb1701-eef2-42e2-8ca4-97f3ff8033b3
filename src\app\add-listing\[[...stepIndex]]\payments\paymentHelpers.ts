// paymentHelpers.ts
// Pure helper functions for payment logic

export function formatRIB(value: string): string {
  // Remove all non-digit characters
  const digits = value.replace(/\D/g, '');

  // Split into groups: 2, 3, 13, 2
  const groups = [];
  let currentPos = 0;
  const groupLengths = [2, 3, 13, 2];

  for (const length of groupLengths) {
    if (currentPos < digits.length) {
      groups.push(digits.substr(currentPos, length));
      currentPos += length;
    }
  }

  // Join groups with spaces
  return groups.join(' ');
}

export function getPlanAmount(plan: string): number {
  switch (plan) {
    case 'monthly':
      return 29.99;
    case 'six_month':
      return 149.99;
    case 'yearly':
      return 279.99;
    default:
      return 29.99;
  }
}

export function getPlanDisplayName(plan: string): string {
  switch (plan) {
    case 'monthly':
      return 'Mensuel';
    case 'sixMonth':
      return 'Semestriel';
    case 'yearly':
      return 'Annuel';
    default:
      return 'Mensuel';
  }
}
// Add other helpers as needed 