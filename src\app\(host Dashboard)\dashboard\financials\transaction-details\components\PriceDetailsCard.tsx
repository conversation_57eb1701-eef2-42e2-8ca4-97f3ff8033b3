'use client';
import React from 'react';
import { formatTND } from '../../utils/format';

interface PriceDetail {
  label: string;
  amount: number;
}

interface PriceDetailsCardProps {
  priceDetails: PriceDetail[];
  total: number;
}

const PriceDetailsCard: React.FC<PriceDetailsCardProps> = ({ 
  priceDetails,
  total
}) => {
  return (
    <div className="w-full bg-white rounded-2xl border border-[#E8EAED] p-4">
      {/* Title */}
      <div className="text-[16px] font-semibold mb-4">Price details</div>
      {/* Price details list */}
      <div className="space-y-2 mb-2">
        {priceDetails.map((detail, index) => (
          <div key={index} className="flex justify-between items-center">
            <span className="text-[15px] text-gray-500">{detail.label}</span>
            <span className="text-[15px] text-black font-semibold">{formatTND(detail.amount)}</span>
          </div>
        ))}
      </div>
      {/* Total */}
      <div className="border-t border-[#E8EAED] mt-3 pt-3 flex justify-between items-center">
        <span className="font-semibold text-[16px]">Total versé</span>
        <span className="font-semibold text-[16px]">{formatTND(total)}</span>
      </div>
    </div>
  );
};

export default PriceDetailsCard;
