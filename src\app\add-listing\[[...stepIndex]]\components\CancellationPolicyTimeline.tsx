"use client"

import { useMemo, useState, useEffect } from "react"
import { format, addDays, addHours, differenceInDays, isAfter } from "date-fns"
import { fr } from "date-fns/locale"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useMediaQuery } from "@/hooks/use-media-query"

// We could import these from the utils, but for simplicity let's define them here
const formatShortDate = (date: Date): string => {
  return format(date, "d MMM", { locale: fr })
}

interface TimelinePoint {
  date: Date
  label: string
  refundStatus: string
  description: string
  isActive: boolean
  isFilled: boolean
  color: string
}

interface CancellationPolicyTimelineProps {
  policyType: string
  className?: string
}

export default function CancellationPolicyTimeline({
  policyType,
  className = "",
}: CancellationPolicyTimelineProps) {
  // Add a sample check-in date 30 days from now for the preview
  const checkInDate = useMemo(() => addDays(new Date(), 30), [])
  
  const [hoveredPoint, setHoveredPoint] = useState<number | null>(null)
  const [activePoint, setActivePoint] = useState<number | null>(null)
  const isMobile = useMediaQuery("(max-width: 768px)")

  // Calculate timeline points based on policy type
  const timelinePoints = useMemo((): TimelinePoint[] => {
    const today = new Date()
    const normalizedPolicyType = policyType
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[-\s]/g, "")

    // Calculate days until check-in
    const daysUntilCheckIn = differenceInDays(checkInDate, today)

    if (normalizedPolicyType.includes("flexible")) {
      // Flexible policy
      const fullRefundDate = addHours(checkInDate, -24)

      return [
        {
          date: today,
          label: "Aujourd'hui",
          refundStatus: "Remboursement intégral",
          description: "Remboursement intégral disponible",
          isActive: true,
          isFilled: true,
          color: "bg-green-500",
        },
        {
          date: fullRefundDate,
          label: formatShortDate(fullRefundDate),
          refundStatus: "Remboursement partiel",
          description: "Remboursement partiel (50%) disponible",
          isActive: false,
          isFilled: false,
          color: "bg-yellow-500",
        },
        {
          date: checkInDate,
          label: formatShortDate(checkInDate),
          refundStatus: "Aucun remboursement",
          description: "Aucun remboursement disponible",
          isActive: false,
          isFilled: false,
          color: "bg-red-500",
        },
      ]
    } else if (normalizedPolicyType.includes("modere") || normalizedPolicyType.includes("moderee")) {
      // Modérée policy
      const fullRefundDate = addDays(checkInDate, -5)
      const noRefundDate = addHours(checkInDate, -48)

      return [
        {
          date: today,
          label: "Aujourd'hui",
          refundStatus: "Remboursement intégral",
          description: "Remboursement intégral disponible",
          isActive: true,
          isFilled: true,
          color: "bg-green-500",
        },
        {
          date: fullRefundDate,
          label: formatShortDate(fullRefundDate),
          refundStatus: "Remboursement partiel",
          description: "Remboursement partiel (50%) disponible",
          isActive: false,
          isFilled: false,
          color: "bg-yellow-500",
        },
        {
          date: noRefundDate,
          label: formatShortDate(noRefundDate),
          refundStatus: "Aucun remboursement",
          description: "Aucun remboursement disponible",
          isActive: false,
          isFilled: false,
          color: "bg-red-500",
        },
        {
          date: checkInDate,
          label: "Arrivée",
          refundStatus: "",
          description: "Début du séjour",
          isActive: false,
          isFilled: false,
          color: "bg-gray-300",
        },
      ]
    } else if (normalizedPolicyType.includes("strict") || normalizedPolicyType.includes("stricte")) {
      // Stricte policy
      const noRefundDate = addDays(checkInDate, -7)

      return [
        {
          date: today,
          label: "Aujourd'hui",
          refundStatus: "Remboursement partiel",
          description: "Remboursement partiel (50%) disponible",
          isActive: true,
          isFilled: true,
          color: "bg-yellow-500",
        },
        {
          date: noRefundDate,
          label: formatShortDate(noRefundDate),
          refundStatus: "Aucun remboursement",
          description: "Aucun remboursement disponible",
          isActive: false,
          isFilled: false,
          color: "bg-red-500",
        },
        {
          date: checkInDate,
          label: "Arrivée",
          refundStatus: "",
          description: "Début du séjour",
          isActive: false,
          isFilled: false,
          color: "bg-gray-300",
        },
      ]
    } else if (normalizedPolicyType.includes("nonremboursable") || normalizedPolicyType.includes("non-remboursable")) {
      return [
        {
          date: today,
          label: "Aujourd'hui",
          refundStatus: "Aucun remboursement",
          description: "Aucun remboursement disponible",
          isActive: true,
          isFilled: true,
          color: "bg-red-500",
        },
        {
          date: checkInDate,
          label: "Arrivée",
          refundStatus: "",
          description: "Début du séjour",
          isActive: false,
          isFilled: false,
          color: "bg-gray-300",
        },
      ]
    } else {
      // Default to Flexible policy
      return [
        {
          date: today,
          label: "Aujourd'hui",
          refundStatus: "Remboursement intégral",
          description: "Remboursement intégral disponible",
          isActive: true,
          isFilled: true,
          color: "bg-green-500",
        },
        {
          date: addDays(checkInDate, -1),
          label: "Veille d'arrivée",
          refundStatus: "Remboursement partiel",
          description: "Remboursement partiel (50%) disponible",
          isActive: false,
          isFilled: false,
          color: "bg-yellow-500",
        },
        {
          date: checkInDate,
          label: formatShortDate(checkInDate),
          refundStatus: "Aucun remboursement",
          description: "Aucun remboursement disponible",
          isActive: false,
          isFilled: false,
          color: "bg-red-500",
        },
      ]
    }
  }, [policyType, checkInDate])

  // Calculate the segments between timeline points
  const timelineSegments = useMemo(() => {
    return timelinePoints.slice(0, -1).map((_, index) => {
      // Determine segment color based on the refund status of the starting point
      const startPoint = timelinePoints[index]

      if (startPoint.refundStatus === "Remboursement intégral") return "bg-green-500"
      if (startPoint.refundStatus === "Remboursement partiel") return "bg-yellow-500"
      return "bg-red-500"
    })
  }, [timelinePoints])

  // Handle touch interaction for mobile
  const handlePointInteraction = (index: number) => {
    if (isMobile) {
      setActivePoint(activePoint === index ? null : index)
    }
  }

  // Generate policy details for the example
  const getPolicyDetails = () => {
    const normalizedPolicyType = policyType
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[-\s]/g, "")

    if (normalizedPolicyType.includes("flexible")) {
      const fullRefundDate = addHours(checkInDate, -24)
      return [
        {
          dateLabel: `Avant le ${format(fullRefundDate, "d MMMM", { locale: fr })}`,
          title: "Remboursement intégral",
          description: `Annulez votre réservation 24 heures ou plus avant l'arrivée pour être intégralement remboursé.`,
        },
        {
          dateLabel: `Avant le ${format(checkInDate, "d MMMM", { locale: fr })}`,
          title: "Remboursement partiel",
          description: `Si vous annulez moins de 24 heures avant l'arrivée, vous serez remboursé de 50% du montant payé.`,
        }
      ]
    } else if (normalizedPolicyType.includes("modere") || normalizedPolicyType.includes("moderee")) {
      const fullRefundDate = addDays(checkInDate, -5)
      const noRefundDate = addHours(checkInDate, -48)
      return [
        {
          dateLabel: `Avant le ${format(fullRefundDate, "d MMMM", { locale: fr })}`,
          title: "Remboursement intégral",
          description: `Annulez votre réservation 5 jours ou plus avant l'arrivée pour être intégralement remboursé.`,
        },
        {
          dateLabel: `Avant le ${format(noRefundDate, "d MMMM", { locale: fr })}`,
          title: "Remboursement partiel",
          description: `Si vous annulez entre 2 et 5 jours avant l'arrivée, vous serez remboursé de 50% du montant payé.`,
        }
      ]
    } else if (normalizedPolicyType.includes("strict") || normalizedPolicyType.includes("stricte")) {
      const partialRefundDate = addDays(checkInDate, -7)
      return [
        {
          dateLabel: `Avant le ${format(partialRefundDate, "d MMMM", { locale: fr })}`,
          title: "Remboursement partiel",
          description: `Si vous annulez 7 jours ou plus avant l'arrivée, vous serez remboursé de 50% du montant payé.`,
        }
      ]
    } else if (normalizedPolicyType.includes("nonremboursable") || normalizedPolicyType.includes("non-remboursable")) {
      return [
        {
          dateLabel: "Après la réservation",
          title: "Aucun remboursement",
          description: "Cette réservation n'est pas remboursable, quel que soit le délai d'annulation.",
        }
      ]
    } else {
      // Default
      return [
        {
          dateLabel: "Politique par défaut",
          title: "Sélectionnez une politique",
          description: "Veuillez sélectionner une politique d'annulation pour voir les détails.",
        }
      ]
    }
  }

  const policyDetails = getPolicyDetails()

  useEffect(() => {
    if (activePoint === null || !isMobile) {
      return;
    }

    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      const target = event.target as HTMLElement;

      const isClickOnTrigger = target.closest('.timeline-point-trigger');
      const isClickInsideTooltipContent = target.closest('.timeline-tooltip-content');

      if (!isClickOnTrigger && !isClickInsideTooltipContent) {
        setActivePoint(null);
      }
    };

    // Add event listener on next tick
    const timerId = setTimeout(() => {
      document.addEventListener('click', handleClickOutside, true);
      document.addEventListener('touchstart', handleClickOutside, true);
    }, 0);

    return () => {
      clearTimeout(timerId);
      document.removeEventListener('click', handleClickOutside, true);
      document.removeEventListener('touchstart', handleClickOutside, true);
    };
  }, [activePoint, isMobile, setActivePoint]);

  return (
    <div className={`w-full ${className}`}>
      <div className="mb-6">
        <h3 className="text-lg sm:text-xl font-semibold text-neutral-800 mb-1">
          Aperçu de la politique (exemple)
        </h3>
        <p className="text-xs sm:text-sm text-neutral-600">
          Cet exemple montre comment cette politique fonctionnerait pour une arrivée le {format(checkInDate, "d MMMM yyyy", { locale: fr })}.
        </p>
      </div>

      {/* Interactive Timeline - Horizontal for Desktop, Vertical for Mobile */}
      <div className=" rounded-2xl p-4 md:p-6 mb-8 w-full overflow-visible">
        <div className="w-full">
          {/* Timeline visualization */}
          <div className="relative">
            {/* Desktop Timeline (Horizontal) */}
            {!isMobile && (
              <div className="relative h-24">
                {/* Horizontal lines */}
                <div className="absolute top-1/2 left-0 right-0 flex">
                  {timelineSegments.map((segmentClass, index) => (
                    <div key={index} className={`h-[3px] ${segmentClass} flex-1 transition-all duration-300`}></div>
                  ))}
                </div>

                {/* Refund status labels (positioned between points) */}
                <div className="absolute top-0 left-0 right-0 flex">
                  {timelineSegments.map((_, index) => (
                    <div key={index} className="text-center flex-1">
                      <span className="text-xs font-medium px-2">{timelinePoints[index].refundStatus}</span>
                    </div>
                  ))}
                </div>

                {/* Dots with tooltips */}
                <div className="absolute top-1/2 left-0 right-0 flex justify-between transform -translate-y-1/2">
                  <TooltipProvider>
                    {timelinePoints.map((point, index) => (
                      <Tooltip key={index} open={hoveredPoint === index}>
                        <TooltipTrigger asChild>
                          <div
                            className={`w-6 h-6 rounded-full flex items-center justify-center cursor-pointer transition-transform duration-300 timeline-point-trigger ${
                              hoveredPoint === index ? "transform scale-125" : ""
                            } ${
                              point.isFilled
                                ? point.color
                                : `border-2 border-${point.color.replace("bg-", "")} bg-white`
                            }`}
                            onMouseEnter={() => setHoveredPoint(index)}
                            onMouseLeave={() => setHoveredPoint(null)}
                          />
                        </TooltipTrigger>
                        <TooltipContent
                          side="top"
                          className="bg-white p-3 shadow-lg rounded-lg border max-w-[250px] z-50"
                          sideOffset={5}
                        >
                          <div className="text-sm">
                            <p className="font-bold mb-1">{point.label}</p>
                            <p>{point.description}</p>
                            <p className="text-xs text-gray-500 mt-1">
                              {format(point.date, "d MMMM yyyy", { locale: fr })}
                            </p>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    ))}
                  </TooltipProvider>
                </div>

                {/* Date labels (positioned under points) */}
                <div className="absolute bottom-0 left-0 right-0 flex justify-between">
                  {timelinePoints.map((point, index) => (
                    <div key={index} className="text-center" style={{ width: "auto" }}>
                      <span className="text-sm font-medium">{point.label}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Mobile Timeline (Vertical) */}
            {isMobile && (
              <div className="relative min-h-[300px] py-2">
                {/* Vertical line */}
                <div className="absolute left-1/2 top-0 bottom-0 w-0.5 bg-gray-200 -translate-x-1/2"></div>

                {/* Vertical segments */}
                {timelineSegments.map((segmentClass, index) => {
                  const topPosition = `${(index * 100) / (timelinePoints.length - 1)}%`
                  const height = `${100 / (timelinePoints.length - 1)}%`

                  return (
                    <div
                      key={index}
                      className={`absolute left-1/2 w-[3px] ${segmentClass} transition-all duration-300`}
                      style={{
                        top: topPosition,
                        height: height,
                        transform: "translateX(-50%)",
                      }}
                    ></div>
                  )
                })}

                {/* Timeline points with tooltips */}
                {timelinePoints.map((point, index) => {
                  const topPosition = `${(index * 100) / (timelinePoints.length - 1)}%`

                  return (
                    <div
                      key={index}
                      className="absolute left-1/2 transform -translate-x-1/2 flex items-center"
                      style={{ top: topPosition }}
                    >
                      {/* Refund status (left side) */}
                      <div className="absolute right-[calc(100%+12px)] text-right w-[120px]">
                        <span className="text-sm font-medium">{point.refundStatus}</span>
                      </div>

                      {/* Dot */}
                      <TooltipProvider>
                        <Tooltip open={activePoint === index}>
                          <TooltipTrigger asChild>
                            <div
                              className={`w-6 h-6 rounded-full flex items-center justify-center cursor-pointer transition-transform duration-300 timeline-point-trigger ${
                                activePoint === index ? "transform scale-125" : ""
                              } ${
                                point.isFilled
                                  ? point.color
                                  : `border-2 border-${point.color.replace("bg-", "")} bg-white`
                              }`}
                              onClick={() => handlePointInteraction(index)}
                            />
                          </TooltipTrigger>
                          <TooltipContent
                            side="bottom"
                            className="bg-white p-3 shadow-lg rounded-lg border max-w-[270px] z-50 timeline-tooltip-content"
                            sideOffset={8}
                          >
                            <div className="text-sm">
                              <p className="font-bold mb-1">{point.label}</p>
                              <p>{point.description}</p>
                              <p className="text-xs text-gray-500 mt-2">
                                {format(point.date, "d MMMM yyyy", { locale: fr })}
                              </p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      {/* Date label (right side) */}
                      <div className="absolute left-[calc(100%+12px)] w-[80px]">
                        <span className="text-sm font-medium">{point.label}</span>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile explanation text for timeline interaction */}
      {isMobile && (
        <p className="text-center text-xs font-medium text-neutral-600 pt-7">
          Touchez les points sur la chronologie pour plus d&apos;informations
        </p>
      )}

      {/* Policy details section */}
      <div className="mt-6 space-y-4">
        {policyDetails.map((detail, index) => (
          <div key={index}>
            <p className="text-sm font-medium text-neutral-700 mb-0.5">{detail.dateLabel}</p>
            <h4 className="text-lg font-semibold text-neutral-900 mb-1">{detail.title}</h4>
            <p className="text-sm text-neutral-600 leading-relaxed">{detail.description}</p>
          </div>
        ))}
      </div>
    </div>
  )
} 