import React from 'react';

interface BankAccountCardProps {
  payoutMethod?: {
    account_holder?: string;
    account_name?: string;
    account_number?: string;
    d17_phone_number?: string;
    [key: string]: any;
  };
}

function maskAccountNumber(accountNumber?: string) {
  if (!accountNumber) return '';
  // Show only last 4 digits, mask the rest
  return `**** **** **** ${accountNumber.slice(-4)}`;
}

const BankAccountCard: React.FC<BankAccountCardProps> = ({ payoutMethod }) => {
  if (!payoutMethod) return null;
  const accountHolder = payoutMethod.account_name || payoutMethod.account_holder;
  return (
    <div className="bg-white rounded-2xl border border-[#E8EAED] p-4 w-full">
      <div className="text-[18px] font-semibold text-black mb-2">Compte bancaire</div>
      {accountHolder && (
        <div className="text-[14px] font-normal text-gray-500">{accountHolder}</div>
      )}
      {payoutMethod.account_number && (
        <div className="text-[14px] font-normal text-gray-500">
          RIB : {maskAccountNumber(payoutMethod.account_number)}
        </div>
      )}
      {payoutMethod.d17_phone_number && (
        <div className="text-[14px] font-normal text-gray-500">D17 : {payoutMethod.d17_phone_number}</div>
      )}
    </div>
  );
};

export default BankAccountCard;
