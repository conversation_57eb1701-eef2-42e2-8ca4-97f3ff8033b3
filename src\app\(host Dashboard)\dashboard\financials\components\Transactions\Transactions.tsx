'use client'
import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import TransactionsTabs from './TransactionsTabs';
import TransactionsTable from './TransactionsTable';
import { PAYOUT_STATUS_UI_MAP } from '../../utils/payoutStatusMap';

const Transactions = () => {
  const [selectedTab, setSelectedTab] = useState<'paye' | 'prevu'>('paye');
  const [activeRowId, setActiveRowId] = useState<string | null>(null);
  const [transactions, setTransactions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const fetchTransactions = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const endpoint = selectedTab === 'paye' ? '/api/financials/paid' : '/api/financials/upcoming';
      const res = await fetch(endpoint);
      const data = await res.json();
      console.log("Transaction data :",data);
      if (data.success) {
        setTransactions(data.transactions || []);
      } else {
        setError(data.error || 'Erreur lors du chargement des transactions.');
        setTransactions([]);
      }
    } catch (e) {
      setError('Erreur lors du chargement des transactions.');
      setTransactions([]);
    } finally {
      setLoading(false);
    }
  }, [selectedTab]);

  useEffect(() => {
    fetchTransactions();
  }, [fetchTransactions]);

  return (
    <div className="w-full">
      <TransactionsTabs selectedTab={selectedTab} onTabChange={setSelectedTab} />
      {loading ? (
        <div className="py-10 text-center text-gray-400">Chargement...</div>
      ) : error ? (
        <div className="py-10 text-center text-red-500">{error}</div>
      ) : (
        <TransactionsTable
          transactions={transactions}
          activeRowId={activeRowId}
          setActiveRowId={setActiveRowId}
          selectedTab={selectedTab}
          onRowClick={(transaction: any) => {
            router.push(`/dashboard/financials/transaction-details?id=${transaction.id}`);
          }}
        />
      )}
    </div>
  );
};

export default Transactions; 