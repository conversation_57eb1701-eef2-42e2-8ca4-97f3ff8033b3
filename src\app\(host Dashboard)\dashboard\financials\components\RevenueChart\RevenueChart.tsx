'use client';
import React, { useEffect, useState } from 'react';
import Legend from './Legend';
import Chart from './Chart';
import { RevenueChartMonth } from '../../types/financeTypes';

interface RevenueChartProps {
  selectedProperties: string[];
  selectedPeriod: string;
}

const RevenueChart: React.FC<RevenueChartProps> = ({ selectedProperties, selectedPeriod }) => {
  const [data, setData] = useState<RevenueChartMonth[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    setError(null);
    const params = new URLSearchParams();
    if (selectedProperties.length > 0) params.set('property', selectedProperties.join(','));
    if (selectedPeriod && selectedPeriod !== 'all') params.set('period', selectedPeriod);
    const url = `/api/financials/revenue-chart${params.toString() ? `?${params.toString()}` : ''}`;
    fetch(url)
      .then(res => {
        if (!res.ok) throw new Error('Erreur lors du chargement des données');
        return res.json();
      })
      .then(data => setData(Array.isArray(data) ? data : []))
      .catch(() => setError('Erreur lors du chargement des données'))
      .finally(() => setLoading(false));
  }, [selectedProperties, selectedPeriod]);

  return (
    <div className="bg-white rounded-[8px] border" style={{ borderColor: '#E8EAED' }}>
      <div className="w-full flex flex-col gap-2">
        <Legend />
        <div className="flex flex-row items-stretch mt-6">
          {/* Only Chart, no custom Y Axis */}
          <div className="mt-4 overflow-x-auto w-full">
            <div style={{ width: 1200 }}>
              <Chart data={data} loading={loading} error={error} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RevenueChart; 