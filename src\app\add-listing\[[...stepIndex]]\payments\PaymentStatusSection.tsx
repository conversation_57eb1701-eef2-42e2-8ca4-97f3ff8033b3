// PaymentStatusSection.tsx
// Extracted PaymentStatusSection component

import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, AlertCircle } from 'lucide-react';
import { PaymentType, PaymentMethod, TrialInfo } from './paymentTypes';

const statusSectionVariants = {
  hidden: { opacity: 0, y: -10 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.4, delay: 0.1 } },
  exit: { opacity: 0, y: -10, transition: { duration: 0.3 } },
};

interface PaymentStatusSectionProps {
  paymentType: PaymentType;
  paymentMethod: PaymentMethod;
  trialInfo?: TrialInfo;
  onModify: () => void;
  animateOnMount?: boolean;
  paymentCompleted?: boolean;
  subscriptionDetails?: { plan: string; duration: number } | null;
}

const PaymentStatusSection: React.FC<PaymentStatusSectionProps> = ({
  paymentType,
  paymentMethod,
  trialInfo,
  onModify,
  animateOnMount,
  paymentCompleted,
  subscriptionDetails
}) => {
  if (!paymentType) return null;

  // Special case for completed subscription payments
  if (paymentCompleted && subscriptionDetails) {
    return (
      <motion.div 
        className="mt-8 mb-8 rounded border border-green-200 bg-green-50 p-[0.7rem] shadow-sm"
        initial={paymentType === 'trial' && animateOnMount ? 'hidden' : false}
        animate="visible"
        variants={statusSectionVariants}
      >
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <div>
              <h3 className="font-medium text-gray-900">Abonnement actif</h3>
              <p className="text-sm text-gray-600">
                Plan {subscriptionDetails.plan} - {subscriptionDetails.duration} jours
              </p>
            </div>
          </div>
          <div className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">
            Paiement Complété
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div 
      className="mt-8 mb-8 rounded border border-gray-200 bg-white p-4 shadow-sm"
      initial={paymentType === 'trial' && animateOnMount ? 'hidden' : false}
      animate="visible"
      variants={statusSectionVariants}
    >
      {paymentType === 'secure' && (
        <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-3">
          <div className="flex items-center space-x-3">
            <CheckCircle className="h-5 w-5 text-green-500" />
            <div className="flex-1 min-w-0">
              <div className="flex flex-col md:flex-row md:items-center md:space-x-2">
                <span className="inline-block self-end md:self-auto md:order-2 px-2 py-0.5 bg-orange-500 text-white text-xs font-semibold rounded-full align-super mb-1 md:mb-0 ml-0 md:ml-2" style={{ marginTop: '-2px' }}>
                  Recommandé
                </span>
                <h3 className="text-lg font-semibold text-[#1A1A1A] whitespace-normal break-words md:order-1">
                  {paymentMethod === 'bank' || paymentMethod === 'd17' ? 'Paiement en ligne' : 'Utilisez la Réservation Sécurisée'}
                </h3>
              </div>
              <p className="text-sm text-gray-500">
                Méthode de paiement: {paymentMethod === 'bank' ? 'Compte bancaire' : paymentMethod === 'd17' ? 'D17' : paymentMethod}
              </p>
            </div>
          </div>
          <button
            onClick={onModify}
            className="w-full md:w-auto mt-3 md:mt-0 text-sm font-medium border md:border-none border-[#EA580F] bg-[#EA580F26] text-[#EA580F] md:text-orange-600 md:bg-transparent md:hover:text-orange-700 rounded transition-colors duration-150 px-4 py-2"
            style={{
              background: undefined,
              color: undefined,
              border: undefined,
            }}
          >
            Modifier
          </button>
        </div>
      )}

      {paymentType === 'cash' && (
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <CheckCircle className="h-5 w-5 text-[#16214F]" />
            <div>
              <h3 className="font-medium text-gray-900">Réservation Standard activée</h3>
              <p className="text-sm text-gray-500">
                Paiement en espèces activé
              </p>
            </div>
          </div>
          <button
            onClick={onModify}
            className="text-sm font-medium text-[#16214F] hover:text-blue-800"
          >
            Modifier
          </button>
        </div>
      )}

      {paymentType === 'trial' && trialInfo && (
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <AlertCircle className="h-5 w-5 text-blue-500" />
            <div>
              <h3 className="font-medium text-gray-900">Période d&apos;essai active</h3>
              <p className="text-sm text-gray-500">
                Du {new Date(trialInfo.startDate).toLocaleDateString()} au {new Date(trialInfo.endDate).toLocaleDateString()}
              </p>
            </div>
          </div>
          <button
            onClick={onModify}
            className="text-sm font-medium text-orange-600 hover:text-orange-700"
          >
            Modifier
          </button>
        </div>
      )}
    </motion.div>
  );
};

export default PaymentStatusSection; 