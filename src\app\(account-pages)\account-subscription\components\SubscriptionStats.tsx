import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { type SubscriptionStats } from '../utils/status-helpers';

type SubscriptionStatsProps = {
    stats: SubscriptionStats;
};

const SubscriptionStats = ({ stats }: SubscriptionStatsProps) => {
    return (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
                <CardContent className="p-6">
                    <div className="flex flex-col">
                        <div className="flex items-center justify-between mb-2">
                            <p className="text-sm text-gray-500">Abonnements actifs</p>
                            <Badge variant="success">Actif</Badge>
                        </div>
                        <p className="text-2xl font-semibold">{stats.active}</p>
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardContent className="p-6">
                    <div className="flex flex-col">
                        <div className="flex items-center justify-between mb-2">
                            <p className="text-sm text-gray-500">Périodes d&apos;essai</p>
                            <Badge variant="secondary">Essai</Badge>
                        </div>
                        <p className="text-2xl font-semibold">{stats.trial}</p>
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardContent className="p-6">
                    <div className="flex flex-col">
                        <div className="flex items-center justify-between mb-2">
                            <p className="text-sm text-gray-500">En attente</p>
                            <Badge variant="warning">En attente</Badge>
                        </div>
                        <p className="text-2xl font-semibold">{stats.pending}</p>
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardContent className="p-6">
                    <div className="flex flex-col">
                        <div className="flex items-center justify-between mb-2">
                            <p className="text-sm text-gray-500">Abonnements expirés</p>
                            <Badge variant="destructive">Expiré</Badge>
                        </div>
                        <p className="text-2xl font-semibold">{stats.expired}</p>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
};

export default SubscriptionStats;