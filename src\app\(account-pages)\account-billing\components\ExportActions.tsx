"use client"
import { But<PERSON> } from "@/components/ui/button"
import { FileDown } from "lucide-react"

type ExportActionsProps = {
    onExportPDF: () => void
    onExportCSV: () => void
    isLoading: boolean
    hasInvoices: boolean
}

const ExportActions = ({ onExportPDF, onExportCSV, isLoading, hasInvoices }: ExportActionsProps) => {
    return (
        <div className="flex gap-2 justify-end">
            <Button variant="outline" size="sm" onClick={onExportPDF} disabled={isLoading || !hasInvoices}>
                <FileDown size={16} className="mr-2" />
                PDF
            </Button>
            <Button variant="outline" size="sm" onClick={onExportCSV} disabled={isLoading || !hasInvoices}>
                <FileDown size={16} className="mr-2" />
                CSV
            </Button>
        </div>
    )
}

export default ExportActions
