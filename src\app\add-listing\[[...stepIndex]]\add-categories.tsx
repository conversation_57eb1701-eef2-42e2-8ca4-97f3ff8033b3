"use client";

import React, { useEffect } from "react";
import CommonLayout from "./CommonLayout";
import Image from "next/image";
import { motion } from "framer-motion";
import { useFormContext } from "../FormContext";

const PageAddCategories = () => {
    const [categories, setCategories] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState<string | null>(null);
    const { formData, setFormData, formErrors, setFormErrors, validateStep } = useFormContext();

    useEffect(() => {
        fetch("/api/listing/categories")
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    setCategories(data.categories);
                } else {
                    setError("Failed to fetch categories");
                }
                setLoading(false);
            })
            .catch(err => {
                setError("Error fetching categories");
                setLoading(false);
            });
    }, []);

    // Validate on mount if category is already selected (e.g., from draft)
    useEffect(() => {
        if (formData.categoryId) {
            validateStep("add-categories");
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const handleSelectCategory = (catId: string) => {
        setFormData(prev => ({ ...prev, categoryId: catId }));
        setFormErrors({});
    };


    useEffect(() => {
        if (formData.categoryId) {
            validateStep("add-categories");
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [formData.categoryId]);

    return (
        <CommonLayout params={{ stepIndex: "add-categories" }}>
            <div className="flex flex-col pb-[80px] md:pb-0 hide-scrollbar items-center justify-center md:py-0">
                {loading && <p>Chargement...</p>}
                {error && <p className="text-red-500">{error}</p>}
                {!loading && !error && (
                    <>
                        <h2 className="font-bold text-gray-900 text-2xl md:text-3xl text-left mb-10 px-4">
                            Quel type de logement souhaitez-vous proposer aux voyageurs ?
                        </h2>
                        <motion.div
                            className="grid grid-cols-2 gap-x-4 gap-y-6 w-full px-4 md:grid-cols-3 md:gap-8 md:px-0 md:max-w-2xl md:mx-auto"
                            initial="hidden"
                            animate="visible"
                            variants={{
                                visible: { transition: { staggerChildren: 0.08 } },
                                hidden: {},
                            }}
                        >
                            {categories.map((cat: any) => (
                                <motion.button
                                    key={cat.id}
                                    type="button"
                                    aria-pressed={formData.categoryId === cat.id}
                                    onClick={() => handleSelectCategory(cat.id)}
                                    className={`border rounded-lg p-4 py-5 flex flex-col items-start justify-center transition w-full
                                        ${formData.categoryId === cat.id
                                            ? 'border-[#EA580F]'
                                            : 'border-gray-200'}
                                        hover:border-orange-400 focus:border-orange-500`
                                    }
                                    style={formData.categoryId === cat.id ? { backgroundColor: '#EA580E26' } : undefined}
                                    variants={{
                                        hidden: { opacity: 0, y: 60 },
                                        visible: { opacity: 1, y: 0, transition: { duration: 0.08, ease: "circOut" } },
                                    }}
                                >
                                    <Image
                                        src={cat.icons}
                                        alt={cat.name?.trim() || ""}
                                        width={32}
                                        height={32}
                                        className="mb-2"
                                        unoptimized
                                    />
                                    <span className="font-medium text-gray-900 text-base">{cat.name?.trim()}</span>
                                </motion.button>
                            ))}
                        </motion.div>
                        {formErrors.categoryId && (
                            <div
                                className="mt-6 flex items-center justify-center"
                                role="alert"
                                aria-live="assertive"
                            >
                                <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full max-w-md">
                                    <svg
                                        className="h-5 w-5 text-red-500 mr-2 flex-shrink-0"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                        aria-hidden="true"
                                    >
                                        <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth="2"
                                            d="M12 8v4m0 4h.01"
                                        />
                                    </svg>
                                    <span className="text-sm text-red-700 font-medium">{formErrors.categoryId}</span>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </div>
        </CommonLayout>
    );
};

export default PageAddCategories; 