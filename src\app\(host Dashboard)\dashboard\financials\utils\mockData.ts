import { Transaction } from '../components/shared/TransactionRow';

// Updated mock data with statuses matching the design
export const mockTransactions: Transaction[] = [
    // Paiement prévu transactions
    { id: "1", reservation: '#BK 4567255565', status: 'Confirmer', amount: 120, date: '2024-06-01' },
    { id: "2", reservation: '#BK 4567255565', status: 'En attente', amount: 120, date: '2024-07-01' },
    { id: "3", reservation: '#BK 4567255565', status: 'En cours', amount: 120, date: '2024-08-01' },
    { id: "4", reservation: '#BK 4567255565', status: 'Remboursé', amount: 120, date: '2024-09-01' },
    { id: "5", reservation: '#BK 4567255565', status: 'Confirmer', amount: 120, date: '2024-10-01' },

    // Payé transactions
    { id: "6", reservation: '#BK 4567255565', status: 'Disponible', amount: 120, date: '2024-11-01' },
    { id: "7", reservation: '#BK 4567255565', status: 'Effectué', amount: 120, date: '2024-12-01' },
    { id: "8", reservation: '#BK 4567255565', status: 'Refusé', amount: 120, date: '2023-01-01' },
    { id: "9", reservation: '#BK 4567255565', status: 'Effectué', amount: 120, date: '2023-02-01' },
    { id: "10", reservation: '#BK 4567255565', status: 'Disponible', amount: 120, date: '2023-03-01' },

    // Transactions with "Payé" status
    { id: "11", reservation: '#BK 4567255565', status: 'Payé', amount: 150, date: '2023-04-01' },
    { id: "12", reservation: '#BK 4567255565', status: 'Payé', amount: 200, date: '2023-05-01' },
    { id: "13", reservation: '#BK 4567255565', status: 'Payé', amount: 180, date: '2023-06-01' },
];

// Helper functions to filter transactions
export const getPaidTransactions = (): Transaction[] => {
    return mockTransactions.filter(t =>
        ['Disponible', 'Effectué', 'Refusé', 'Payé'].includes(t.status)
    );
};

export const getScheduledTransactions = (): Transaction[] => {
    return mockTransactions.filter(t =>
        ['Confirmer', 'En attente', 'En cours', 'Remboursé'].includes(t.status)
    );
}; 