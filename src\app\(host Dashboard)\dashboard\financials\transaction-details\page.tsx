'use client';
import React, { useEffect, useState, type FC } from 'react';
import { useSearchParams } from 'next/navigation';
import Header from './components/Header';
import PaymentCenterHeader from './components/PaymentCenterHeader';
import PDFButton from './components/PDFButton';
import DisponibleTransactionDetails from './components/DisponibleTransactionDetails';
import EffectueTransactionDetails from './components/EffectueTransactionDetails';
import DefaultTransactionDetails from './components/DefaultTransactionDetails';

interface Transaction {
  id: string;
  status: string;
  [key: string]: any;
}

const TransactionDetailsPage: FC = () => {
  const searchParams = useSearchParams();
  const id = searchParams ? searchParams.get('id') || null : null;
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) {
      setError('Aucune transaction sélectionnée.');
      setLoading(false);
      return;
    }
    setLoading(true);
    fetch(`/api/financials/transaction/${id}`)
      .then(res => {
        if (!res.ok) throw new Error('Transaction non trouvée ou accès refusé.');
        return res.json();
      })
      .then(data => {
        setTransaction(data);
        setError(null);
      })
      .catch(err => setError(err.message))
      .finally(() => setLoading(false));
  }, [id]);

  const renderTransactionDetails = () => {
    if (!transaction) return null;

    // Use priceDetails directly from the backend response
    const priceDetails = transaction.priceDetails || [];
    // Use the final payout amount for the 'Total' line in the card
    const totalPrice = transaction.amount || 0;


    const statusForSwitch = (transaction.status || '').toLowerCase();

    const detailProps = { ...transaction, priceDetails, totalPrice };
    switch (statusForSwitch) {
      case 'ready':
        return <DisponibleTransactionDetails transaction={detailProps} />;
      case 'paid':
        return <EffectueTransactionDetails transaction={detailProps} />;
      default:
        return <DefaultTransactionDetails transaction={detailProps} />;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <span>Chargement...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center text-red-600">
        <span>{error}</span>
      </div>
    );
  }

  const isPaymentCenterView = transaction && ['ready', 'paid'].includes((transaction.status || '').toLowerCase());

  return (
    <div className="min-h-screen bg-white flex flex-col items-center py-4 px-4">
      <div className="w-full max-w-md">
        {isPaymentCenterView ? (
          <PaymentCenterHeader />
        ) : (
          <Header amount={transaction?.amount} startDate={transaction?.booking?.startDate} />
        )}
      </div>
      {renderTransactionDetails()}
      {!isPaymentCenterView && (
        <div className="w-full max-w-md flex justify-end mt-6">
          <PDFButton />
        </div>
      )}
    </div>
  );
};

export default TransactionDetailsPage;
