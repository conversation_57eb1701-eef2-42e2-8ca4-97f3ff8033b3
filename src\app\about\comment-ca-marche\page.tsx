'use client'
import React, { useState } from "react";
import Image from "next/image";
import ImageLightbox from "@/components/ImageLightbox";

const BRAND_COLOR = "#EA5911";

const steps = [
  {
    title: "Ajouter une propriété",
    imageUrl: "https://api.almindharbooking.com/storage/v1/object/public/comment-ca-marche//step_1.png",
    imageAlt: "Capture d'écran étape 1",
    description: (
      <>
        <p>
          Connectez-vous à votre compte sur la plateforme Almindhar Booking.<br />
          Une fois connecté, cliquez sur le bouton « Ajouter une propriété » pour commencer à publier votre bien.
        </p>
      </>
    ),
  },
  {
    title: "Proprié<PERSON>",
    imageUrl: "https://api.almindharbooking.com/storage/v1/object/public/comment-ca-marche//step_2.png",
    imageAlt: "Capture d'écran étape 2",
    description: (
      <>
        <p>Définir les caractéristiques principales de votre bien :</p>
        <ul className="list-disc list-inside ml-4 space-y-3">
          <li>Type de propriété : choisissez parmi les options disponibles (Appartement, Villa, Chambre d&apos;hôte, Maison d&apos;hôte, Chalet).</li>
          <li>Catégorie de propriété : sélectionnez le cadre qui correspond le mieux à votre propriété (Forêt et nature, Ferme, Bord de mer, Séjours urbains, Sahara, Montagnes, ou avec piscine).</li>
          <li>Type de logement : indiquez si le bien proposé est un logement entier ou une chambre d&apos;hôte.</li>
        </ul>
      </>
    ),
  },
  {
    title: "Emplacement de votre logement",
    imageUrl: "https://api.almindharbooking.com/storage/v1/object/public/comment-ca-marche//step_3.png",
    imageAlt: "Capture d'écran étape 3",
    description: (
      <>
        <p>Renseigner avec précision l&apos;emplacement de votre bien afin d&apos;aider les voyageurs à le situer facilement :</p>
        <ul className="list-disc list-inside ml-4 space-y-3">
          <li>Sélectionnez la région dans laquelle se trouve votre propriété.</li>
          <li>Choisissez la ville correspondante.</li>
          <li>Indiquez la localisation exacte en cliquant sur le bouton « Utiliser l&apos;emplacement actuel » ou en saisissant manuellement l&apos;adresse si nécessaire.</li>
        </ul>
      </>
    ),
  },
  {
    title: "Invités",
    imageUrl: "https://api.almindharbooking.com/storage/v1/object/public/comment-ca-marche//step_4.png",
    imageAlt: "Capture d'écran étape 4",
    description: (
      <>
        <p>Précisez les détails concernant l&apos;hébergement afin d&apos;informer au mieux les futurs locataires :</p>
        <ul className="list-disc list-inside ml-4 space-y-3">
          <li>Nombre d&apos;invités : indiquez combien de personnes peuvent séjourner dans le logement.</li>
          <li>Nombre de chambres : spécifiez le nombre total de chambres disponibles.</li>
          <li>Nombre de lits : renseignez le nombre de couchages présents.</li>
          <li>Nombre de salles de bain : précisez le nombre de salles de bain à disposition.</li>
          <li>Nombre de cuisines : indiquez si une ou plusieurs cuisines sont disponibles.</li>
        </ul>
      </>
    ),
  },
  {
    title: "Photos",
    imageUrl: "https://api.almindharbooking.com/storage/v1/object/public/comment-ca-marche//step_5.png",
    imageAlt: "Capture d'écran étape 5",
    description: (
      <>
        <p>Mettez en valeur votre bien en ajoutant des photos :</p>
        <ul className="list-disc list-inside ml-4 space-y-3">
          <li>Cliquez sur « Télécharger l&apos;image » pour importer la photo de couverture, qui sera affichée en premier sur votre annonce.</li>
          <li>Cliquez ensuite sur « Ajouter une image » pour importer des photos supplémentaires présentant les différentes pièces et atouts de votre propriété.</li>
        </ul>
      </>
    ),
  },
  {
    title: "Équipements",
    imageUrl: "https://api.almindharbooking.com/storage/v1/object/public/comment-ca-marche//step_6.png",
    imageAlt: "Capture d'écran étape 6",
    description: (
      <>
        <p>Indiquez les commodités proposées dans votre logement en sélectionnant les équipements disponibles parmi les options proposées (Wi-Fi, climatisation, machine à laver, parking, etc.).<br />
        Ces informations aident les voyageurs à mieux évaluer le confort de leur hébergement.</p>
      </>
    ),
  },
  {
    title: "Règles",
    imageUrl: "https://api.almindharbooking.com/storage/v1/object/public/comment-ca-marche//step_7.png",
    imageAlt: "Capture d'écran étape 7",
    description: (
      <>
        <p>Définissez clairement les règles à respecter au sein de votre propriété.</p>
        <ul className="list-disc list-inside ml-4 space-y-3">
          <li>Indiquez si certaines actions sont autorisées ou interdites, telles que : organiser des fêtes, accueillir des animaux, etc.</li>
        </ul>
        <p>Ces règles permettent de cadrer les séjours et d&apos;assurer le respect de votre espace.</p>
      </>
    ),
  },
  {
    title: "Déscription",
    imageUrl: "https://api.almindharbooking.com/storage/v1/object/public/comment-ca-marche//step_8.png",
    imageAlt: "Capture d'écran étape 8",
    description: (
      <>
        <p>Rédigez une description claire et attrayante de votre bien afin d&apos;aider les futurs clients à mieux le découvrir :</p>
        <ul className="list-disc list-inside ml-4 space-y-3">
          <li>Titre de l&apos;annonce : choisissez un titre qui met en valeur votre logement.</li>
          <li>Description détaillée : Présentez les points forts de votre propriété (emplacement, ambiance, équipements, vue, etc.)</li>
        </ul>
      </>
    ),
  },
  {
    title: "Prix",
    imageUrl: "https://api.almindharbooking.com/storage/v1/object/public/comment-ca-marche//step_9.png",
    imageAlt: "Capture d'écran étape 9",
    description: (
      <>
        <p>Définissez les tarifs de votre propriété :</p>
        <ul className="list-disc list-inside ml-4 space-y-3">
          <li>Prix par nuitée : indiquez le tarif que vous souhaitez appliquer pour une nuit.</li>
          <li>Réductions : si vous le souhaitez, cliquez sur « Ajouter une réduction » pour proposer des offres spéciales.</li>
          <li>Frais supplémentaires : ajoutez des frais de service éventuels tels que le nettoyage, l&apos;accueil des animaux, etc.</li>
          <li>Durée de séjour et horaires : personnalisez la durée minimale ou maximale du séjour, ainsi que les horaires d&apos;arrivée et de départ en cliquant sur « Modifier ».</li>
        </ul>
      </>
    ),
  },
  {
    title: "Disponibilité",
    imageUrl: "https://api.almindharbooking.com/storage/v1/object/public/comment-ca-marche//step_10.png",
    imageAlt: "Capture d'écran étape 10",
    description: (
      <>
        <p>Gérez les dates de disponibilité de votre logement pour éviter les doubles réservations :</p>
        <ul className="list-disc list-inside ml-4 space-y-3">
              <li>Si votre bien est également listé sur d&apos;autres plateformes comme Airbnb, Booking.com, ou si vous utilisez un calendrier personnel, sélectionnez « Oui, j&apos;utilise d&apos;autres plateformes ou un calendrier personnel », puis cliquez sur « Synchroniser les calendriers » pour automatiser la mise à jour des disponibilités.</li>
          <li>Si vous gérez vos réservations exclusivement via Almindhar Booking, sélectionnez « Non, je gère toutes les réservations ici uniquement », puis utilisez l&apos;option « Dates bloquées » pour indiquer les périodes où votre logement n&apos;est pas disponible.</li>
        </ul>
      </>
    ),
  },
  {
    title: "Informations personnelles",
    imageUrl: "https://api.almindharbooking.com/storage/v1/object/public/comment-ca-marche//step_11.png",
    imageAlt: "Capture d'écran étape 11",
    description: (
      <>
        <p>Complétez vos informations personnelles pour finaliser votre annonce :</p>
        <ul className="list-disc list-inside ml-4 space-y-3">
          <li>Nom et prénom : saisissez vos informations d&apos;identité telles qu&apos;elles apparaissent sur votre profil.</li>
          <li>Numéro de téléphone : renseignez un numéro valide pour faciliter la communication avec les clients.</li>
          <li>Adresse e-mail : indiquez votre e-mail de contact principal.</li>
        </ul>
      </>
    ),
  },
  {
    title: "Annulation",
    imageUrl: "https://api.almindharbooking.com/storage/v1/object/public/comment-ca-marche//step_12.png",
    imageAlt: "Capture d'écran étape 12",
    description: (
      <>
        <p>Définissez les conditions d&apos;annulation applicables aux séjours de moins de 28 nuits :</p>
        <ul className="list-disc list-inside ml-4 space-y-3">
          <li>Choisissez une politique d&apos;annulation standard parmi les options suivantes :</li>
        </ul>
        <ul className="list-disc list-inside ml-8 space-y-3">
          <li><b>Dur</b> : Remboursement intégral si l&apos;annulation intervient au moins 30 jours avant l&apos;arrivée (sauf exceptions spécifiques).</li>
          <li><b>Flexible</b> : Remboursement intégral pour toute annulation effectuée jusqu&apos;à 2 jours avant l&apos;arrivée.</li>
          <li><b>Modérée</b> : Remboursement intégral pour toute annulation effectuée jusqu&apos;à 7 jours avant l&apos;arrivée.</li>
          <li><b>Stricte</b> : Remboursement intégral uniquement si l&apos;annulation survient dans les 48 heures suivant la réservation et au moins 14 jours avant l&apos;arrivée.</li>
        </ul>
        <p className="mt-4">
          <b>Option non remboursable</b> : Vous pouvez également proposer une alternative non remboursable, permettant aux voyageurs de bénéficier d&rsquo;une réduction de 10 % sur le tarif, tout en garantissant votre versement, quelle que soit la date d&rsquo;annulation.
        </p>
      </>
    ),
  },
  {
    title: "Plan d'abonnement",
    imageUrl: "https://api.almindharbooking.com/storage/v1/object/public/comment-ca-marche//step_13.png",
    imageAlt: "Capture d'écran étape 13",
    description: (
      <>
        <p className="font-semibold text-gray-800 mb-2">Réservation Sécurisée (Recommandée) :</p>
        <ul className="list-disc list-inside ml-4 space-y-3 mb-4">
          <li>Réservations instantanées sans délai d&apos;attente.</li>
          <li>Paiements sécurisés traités automatiquement.</li>
          <li>Commission de 5 % seulement sur chaque réservation confirmée.</li>
          <li>Aucun abonnement mensuel à payer.</li>
          <li>Support client prioritaire.</li>
        </ul>
        <p className="mb-6 italic">Fonctionnement : votre annonce est publiée immédiatement, les voyageurs peuvent réserver instantanément, et vous recevez votre paiement directement après leur séjour.</p>

        <hr className="my-6 border-gray-300" />

        <p className="font-semibold text-gray-800 mb-2">Réservation Flexible :</p>
        <ul className="list-disc list-inside ml-4 space-y-3 mb-4">
          <li>Aucun frais de commission ni frais caché.</li>
          <li>Paiement d&apos;un abonnement simple et transparent pour une gestion flexible de votre calendrier et de vos réservations.</li>
        </ul>
        <p className="font-semibold text-gray-800 mb-2">Tarification :</p>
        <ul className="list-disc list-inside ml-8 space-y-3">
          <li>30 TND pour 1 mois,</li>
          <li>169 TND pour 6 mois,</li>
          <li>299 TND pour 12 mois.</li>
        </ul>
      </>
    ),
  },
  {
    title: "Aperçu",
    imageUrl: "https://api.almindharbooking.com/storage/v1/object/public/comment-ca-marche//step_14.png",
    imageAlt: "Capture d'écran étape 14",
    description: (
      <>
        <p>Vous avez terminé la création de votre annonce !<br />
        Avant de la mettre en ligne :</p>
        <ul className="list-disc list-inside ml-4 space-y-3">
          <li>Relisez l&apos;ensemble des informations saisies pour vérifier.</li>
          <li>Une fois prêt, cliquez sur « Publier » pour soumettre votre annonce.</li>
          <li>Votre annonce passera en attente de validation par notre équipe. Une fois validée, elle sera officiellement publiée et visible par les voyageurs.</li>
        </ul>
      </>
    ),
  },
];

export default function CommentCaMarche() {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [selectedImageAlt, setSelectedImageAlt] = useState<string>("");

  const openLightbox = (imageUrl: string, altText: string) => {
    setSelectedImageUrl(imageUrl);
    setSelectedImageAlt(altText);
    setLightboxOpen(true);
  };

  const closeLightbox = () => {
    setLightboxOpen(false);
    setSelectedImageUrl(null);
    setSelectedImageAlt("");
  };

  return (
    <div className="relative min-h-screen flex items-center justify-center py-10 px-2 bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
      {/* Abstract background shape */}
      <div
        aria-hidden="true"
        className="absolute -top-32 -left-32 w-[500px] h-[500px] rounded-full opacity-10 blur-2xl"
        style={{ background: `radial-gradient(circle, ${BRAND_COLOR} 0%, transparent 70%)` }}
      />
      <div className="w-full max-w-4xl bg-white rounded-3xl shadow-2xl p-6 sm:p-12 relative z-10">
        <h1 className="text-4xl sm:text-5xl font-extrabold text-center mb-12 text-gray-900 tracking-tight">
          Guide d&apos;ajout d&apos;une propriété
          <span
            className="block mx-auto mt-4 rounded-full"
            style={{ width: 120, height: 8, background: BRAND_COLOR }}
          ></span>
        </h1>
        <ol className="space-y-12">
          {steps.map((step, idx) => (
            <li key={idx} className="bg-gray-50 rounded-xl shadow-lg p-6 sm:p-8 border-l-8" style={{ borderColor: BRAND_COLOR }}>
              <div className="flex items-center mb-6">
                <div className="text-4xl font-extrabold mr-4" style={{ color: BRAND_COLOR }}>
                  {idx + 1}
                </div>
                <h2 className="text-2xl sm:text-3xl font-bold text-gray-900" style={{ color: BRAND_COLOR }}>
                  {step.title}
                </h2>
              </div>
              <button
                type="button"
                onClick={() => openLightbox(step.imageUrl, step.imageAlt || `Capture d'écran étape ${idx + 1}`)}
                className="w-full aspect-[16/9] rounded-lg overflow-hidden border-2 border-gray-200 hover:border-[#EA5911] focus:outline-none focus:ring-2 focus:ring-[#EA5911] focus:ring-offset-2 shadow-md mb-8 bg-gray-100 flex items-center justify-center cursor-pointer transition-all duration-200 hover:shadow-xl"
                aria-label={`Voir l&rsquo;image pour ${step.title} en grand`}
              >
                <Image
                  src={step.imageUrl || "/images/step-placeholder.png"}
                  alt={step.imageAlt || `Capture d'écran étape ${idx + 1}`}
                  width={800}
                  height={450}
                  className="object-contain w-full h-full"
                  priority={idx < 3}
                />
              </button>
              <div className="text-gray-700 text-lg leading-loose text-left">
                {step.description}
              </div>
            </li>
          ))}
        </ol>
      </div>
      {lightboxOpen && selectedImageUrl && (
        <ImageLightbox
          imageUrl={selectedImageUrl}
          altText={selectedImageAlt}
          isOpen={lightboxOpen}
          onClose={closeLightbox}
        />
      )}
    </div>
  );
} 