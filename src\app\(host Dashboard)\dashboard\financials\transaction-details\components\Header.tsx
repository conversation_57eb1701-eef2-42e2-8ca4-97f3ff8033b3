'use client';
import React from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import { formatTND } from '../../utils/format';

interface HeaderProps {
  amount: number;
  startDate?: string;
}

function formatSendDate(startDate?: string) {
  if (!startDate) return '';
  const date = new Date(startDate);
  date.setDate(date.getDate() + 2); // Add 48 hours
  const options: Intl.DateTimeFormatOptions = { day: 'numeric', month: 'long' };
  return date.toLocaleDateString('fr-FR', options);
}

const Header: React.FC<HeaderProps> = ({ amount, startDate }) => {
  const router = useRouter();
  return (
    <div className="relative flex flex-col items-center pt-14 pb-2">
      {/* Close button */}
      <button
        className="absolute right-4 top-4 w-[27px] h-[27px] rounded-full border border-black flex items-center justify-center p-0 bg-white hover:bg-gray-50 active:bg-gray-200 transition"
        aria-label="Fermer"
        onClick={() => router.push('/dashboard/financials')}
      >
        <XMarkIcon className="w-4 h-4 text-black" strokeWidth={2} />
      </button>
      {/* Amount */}
      <div className="text-[24px] font-semibold mb-3">{formatTND(amount)}</div>
      {/* Subtitle */}
      <div className="flex items-center gap-1 mb-4">
        <span className="text-[14px] font-medium text-black">A envoyer en</span>
        <span className="text-[14px] font-medium text-gray-500">{formatSendDate(startDate)}</span>
      </div>
    </div>
  );
};

export default Header;
