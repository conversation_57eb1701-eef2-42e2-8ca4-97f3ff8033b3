'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { stepSchemas } from "./formSchemas";
import * as Yup from "yup";
import addListingImageStore from "./addListingImageStore";

// Minimal type for the first step (category selection)
export interface AddListingFormData {
  categoryId?: string | null;
  propertyTypeId?: string | null;
  roomTypeId?: string | null;
  // Location fields for add-location step
  propertyState?: string | null;
  propertyStateId?: string | null;
  propertyCity?: string | null;
  propertyCityId?: string | null;
  propertyAddress?: string | null;
  latitude?: number | null;
  longitude?: number | null;
  // Guests and rules step fields
  numGuests?: number | null;
  numBedrooms?: number | null;
  numBeds?: number | null;
  numBathrooms?: number | null;
  numKitchens?: number | null;
  eventsAllowed?: boolean | null;
  smokingAllowed?: boolean | null;
  commercialPhotographyAllowed?: boolean | null;
  customRules?: string[] | null;
  selectedAmenities?: string[] | null;
  // Title and description fields
  listingTitle?: string | null;
  listingDescription?: string | null;
  // Conditions fields
  cancellationPolicyId?: string | null;
  // Pricing fields
  nightlyRate?: number | null;
  securityDeposit?: number | null;
  additionalFees?: Array<{
    title?: string;
    price?: number;
    feeType?: string;
  }> | null;
  // Stay configuration
  minStay?: number | null;
  maxStay?: number | null;
  arrivalTime?: string | null;
  departureTime?: string | null;
  arrivalMode?: 'single' | 'range' | null;
  arrivalTimeStart?: string | null;
  arrivalTimeEnd?: string | null;
  // Discount fields (new robust structure)
  weeklyDiscount?: { mode: "percent" | "fixed"; value: number | null };
  monthlyDiscount?: { mode: "percent" | "fixed"; value: number | null };
  firstBookingDiscount?: { mode: "percent" | "fixed"; value: number | null };
  // Deprecated: legacy percent-only fields (for migration/backward compatibility)
  weeklyDiscountPercent?: number | null;
  monthlyDiscountPercent?: number | null;
  firstBookingDiscountPercent?: number | null;
  // Dates/calendar sync step fields
  blockedDates?: number[];
  importedCalendars?: {
    id: string;
    name: string;
    url: string;
  }[];
  // Payment step fields
  paymentSessionId?: string;
  selectedPaymentType?: string;
  paymentMethodInfo?: any;
  paymentModel?: string;
  // Add trialInfo for payment trial state
  trialInfo?: {
    startDate: string;
    endDate: string;
  };
  // Add more fields as needed for other steps
}

interface FormContextType {
  formData: AddListingFormData;
  setFormData: React.Dispatch<React.SetStateAction<AddListingFormData>>;
  formErrors: Record<string, string>;
  setFormErrors: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  currentStep: string;
  setCurrentStep: React.Dispatch<React.SetStateAction<string>>;
  validateStep: (stepKey: string) => boolean;
  validateAllSteps: () => boolean;
  coverFile: File | null;
  setCoverFile: React.Dispatch<React.SetStateAction<File | null>>;
  additionalFiles: File[];
  setAdditionalFiles: React.Dispatch<React.SetStateAction<File[]>>;
}

const FormContext = createContext<FormContextType | undefined>(undefined);

// Add this utility function at the top of the file, after imports
const isSerializable = (value: any): boolean => {
  if (value === undefined || value === null) return true;
  if (value instanceof File || value instanceof Blob) return false;
  if (typeof value === 'function') return false;
  if (typeof value === 'object') {
    if (value instanceof Date) return true;
    if (Array.isArray(value)) {
      return value.every(item => isSerializable(item));
    }
    return Object.values(value).every(item => isSerializable(item));
  }
  return true;
};


export const FormProvider = ({ children }: { children: ReactNode }) => {
  const [formData, setFormData] = useState<AddListingFormData>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("addListingFormData");
      if (saved) return JSON.parse(saved);
    }
    // Explicitly set guest-related fields to 0 on initial load
    return {
      numGuests: 0,
      numBedrooms: 0,
      numBeds: 0,
      numBathrooms: 0,
      numKitchens: 0,
    };
  });
  // Draft image buffers persisted via localForage
  const [coverFile, setCoverFile] = useState<File | null>(null);
  const [additionalFiles, setAdditionalFiles] = useState<File[]>([]);
  const [rehydrated, setRehydrated] = useState(false); // loading state
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [currentStep, setCurrentStep] = useState<string>("");

  // Rehydrate draft images from IndexedDB on mount
  useEffect(() => {
    (async () => {
      const storedCover = await addListingImageStore.getItem<any>('draft-cover');
      if (storedCover && storedCover.data) {
        const blob = new Blob([new Uint8Array(storedCover.data)], { type: storedCover.type });
        setCoverFile(new File([blob], storedCover.name, { type: storedCover.type, lastModified: storedCover.lastModified }));
      } else {
        setCoverFile(null);
      }
      // ---  deserialization for additional images ---
      const storedAdditional = await addListingImageStore.getItem<any[]>('draft-additional');
      if (storedAdditional && Array.isArray(storedAdditional)) {
        const files: File[] = await Promise.all(storedAdditional.map(async (item) => {
          if (item instanceof File) return item;
          // item: { name, type, lastModified, data (ArrayBuffer) }
          const blob = new Blob([new Uint8Array(item.data)], { type: item.type });
          return new File([blob], item.name, { type: item.type, lastModified: item.lastModified });
        }));
        setAdditionalFiles(files);
      } else {
        setAdditionalFiles([]);
      }
      setRehydrated(true);
    })();
  }, []);

  // Persist to localStorage
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("addListingFormData", JSON.stringify(formData));
    }
  }, [formData]);

  // Persist coverFile to IndexedDB
  useEffect(() => {
    if (coverFile) {
      (async () => {
        await addListingImageStore.setItem('draft-cover', {
          name: coverFile.name,
          type: coverFile.type,
          lastModified: coverFile.lastModified,
          data: await coverFile.arrayBuffer(),
        });
      })();
    }
  }, [coverFile]);
  // Persist additionalFiles to IndexedDB only after rehydration, and never remove the key automatically
  useEffect(() => {
    if (!rehydrated) return;
    (async () => {
      const serialized = await Promise.all(
        additionalFiles.map(async (file) => ({
          name: file.name,
          type: file.type,
          lastModified: file.lastModified,
          data: await file.arrayBuffer(),
        }))
      );
      await addListingImageStore.setItem('draft-additional', serialized);
    })();
  }, [additionalFiles, rehydrated]);

  // Prevent UI from rendering children until rehydration is complete
  if (!rehydrated) {
    return <div style={{padding: '2rem', textAlign: 'center'}}>Chargement des images...</div>;
  }

  const validateStep = (stepKey: string) => {
    // Custom validation for image uploads
    if (stepKey === 'add-images') {
      const newErrors: Record<string, string> = {};
      if (!coverFile) newErrors.coverFile = 'Une photo de couverture est requise.';
      if (additionalFiles.length < 5) newErrors.additionalFiles = 'Veuillez télécharger au moins 5 photos supplémentaires.';
      if (additionalFiles.length > 20) newErrors.additionalFiles = 'Vous ne pouvez pas dépasser 20 photos supplémentaires.';
      [coverFile, ...additionalFiles].forEach((f, idx) => {
        if (f) {
          if (!f.type.match(/^image\/(jpeg|png)$/)) {
            newErrors[`file${idx}`] = 'Format non supporté (jpeg/png uniquement).';
          }
          if (f.size > 5 * 1024 * 1024) {
            newErrors[`file${idx}`] = 'Chaque image doit faire \u2264 5 Mo.';
          }
        }
      });
      // Only update if errors changed
      if (JSON.stringify(formErrors) !== JSON.stringify(newErrors)) {
        setFormErrors(newErrors);
      }
      return Object.keys(newErrors).length === 0;
    }

    const schema: Yup.ObjectSchema<any> | undefined = stepSchemas[stepKey];
    if (!schema) return true;

    try {
      schema.validateSync(formData, { abortEarly: false });
      // Only update if there were previous errors
      if (Object.keys(formErrors).length > 0) {
        setFormErrors({});
      }
      return true;
    } catch (err: any) {
      if (err.inner) {
        const newErrors: Record<string, string> = {};
        err.inner.forEach((error: any) => {
          if (error.path) {
            newErrors[error.path] = error.message;
          }
        });
        // Only update if errors changed
        if (JSON.stringify(formErrors) !== JSON.stringify(newErrors)) {
          setFormErrors(newErrors);
        }
      }
      return false;
    }
  };

  const validateAllSteps = () => {
    // Validate all steps in the registry
    let allValid = true;
    for (const key in stepSchemas) {
      if (!validateStep(key)) {
        allValid = false;
      }
    }
    return allValid;
  };

  return (
    <FormContext.Provider
      value={{
        formData,
        setFormData,
        formErrors,
        setFormErrors,
        currentStep,
        setCurrentStep,
        validateStep,
        validateAllSteps,
        coverFile,
        setCoverFile,
        additionalFiles,
        setAdditionalFiles,
      }}
    >
      {children}
    </FormContext.Provider>
  );
};

export const useFormContext = () => {
  const ctx = useContext(FormContext);
  if (!ctx) throw new Error("useFormContext must be used within a FormProvider");
  return ctx;
};