'use client';

import React from 'react';
import { Button } from "@/components/ui/button";

type LoadMoreButtonProps = {
    hasMore: boolean;
    isLoading: boolean;
    onLoadMore: () => void;
};

const LoadMoreButton = ({ hasMore, isLoading, onLoadMore }: LoadMoreButtonProps) => {
    if (!hasMore) return null;

    return (
        <Button
            variant="outline"
            onClick={onLoadMore}
            disabled={isLoading}
            className="mt-4"
        >
            {isLoading ? 'Chargement...' : 'Charger plus'}
        </Button>
    );
};

export default LoadMoreButton;