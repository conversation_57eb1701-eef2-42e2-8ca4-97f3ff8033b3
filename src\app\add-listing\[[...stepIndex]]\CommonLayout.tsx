"use client"

import React, { useEffect, useState } from "react"
import type { FC } from "react"
import { MainLayout } from "../components/Layout/MainLayout"
import { useRouter } from "next/navigation"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogOverlay, DialogPortal } from "@/components/ui/dialog"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { Button } from "@/components/ui/button"
import addListingImageStore from "../addListingImageStore"

export interface CommonLayoutProps {
  children: React.ReactNode
  params: {
    stepIndex: string
  }
}

// Utility function to check if an object has any non-empty, non-null, or non-undefined values
function hasNonEmptyValues(obj: Record<string, any>): boolean {
  if (!obj || typeof obj !== 'object') return false;
  return Object.entries(obj).some(([key, value]) => {
    if (value === null || value === undefined) return false;
    if (typeof value === 'string' && value.trim() === '') return false;
    if (Array.isArray(value) && value.length === 0) return false;
    if (typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0) return false;
    // Treat 0 as empty for specific fields that are defaulted to 0
    const zeroFields = ['numGuests', 'numBedrooms', 'numBeds', 'numBathrooms', 'numKitchens'];
    if (zeroFields.includes(key) && value === 0) return false;
    return true;
  });
}

const CommonLayout: FC<CommonLayoutProps> = ({ children, params }) => {
  const router = useRouter();
  const [showExitWarning, setShowExitWarning] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(null);
  const [showRestoreDialog, setShowRestoreDialog] = useState(false);
  const [hasCheckedForDraft, setHasCheckedForDraft] = useState(false);

  // Check for draft in localStorage when component mounts
  useEffect(() => {
    if (typeof window !== 'undefined' && !hasCheckedForDraft) {
      // Check if returning from payment
      const returningFromPayment = localStorage.getItem('addListingReturningFromPayment');
      if (returningFromPayment) {
        localStorage.removeItem('addListingReturningFromPayment');
        setHasCheckedForDraft(true);
        localStorage.setItem('addListingActiveSession', 'true');
        return;
      }
      const savedFormData = localStorage.getItem('addListingFormData');
      const isActiveSession = localStorage.getItem('addListingActiveSession');
      let shouldShowRestore = false;
      if (savedFormData && !isActiveSession) {
        try {
          const parsed = JSON.parse(savedFormData);
          if (hasNonEmptyValues(parsed)) {
            shouldShowRestore = true;
          }
        } catch {
          // If parsing fails, treat as empty
        }
      }
      if (shouldShowRestore) {
        setShowRestoreDialog(true);
      }
      setHasCheckedForDraft(true);
      localStorage.setItem('addListingActiveSession', 'true');
    }
  }, [hasCheckedForDraft]);

  // Handle leaving the page with unsaved changes
  useEffect(() => {
    // Handle back/forward navigation
    const handlePopState = () => {
      const savedFormData = localStorage.getItem('addListingFormData');
      if (savedFormData) {
        setShowExitWarning(true);
      }
    };

    window.addEventListener('popstate', handlePopState);

    // Clean up event listeners
    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, []);

  // Handle confirming navigation away
  const handleConfirmNavigation = () => {
    setShowExitWarning(false);
    // Clear form data and active session before navigating
    localStorage.removeItem('addListingFormData');
    localStorage.removeItem('addListingActiveSession');
    if (pendingNavigation) {
      router.push(pendingNavigation);
      setPendingNavigation(null);
    } else {
      // If no pending navigation, allow the browser's default navigation
      window.history.back();
    }
  };

  // Handle canceling navigation
  const handleCancelNavigation = () => {
    setShowExitWarning(false);
    setPendingNavigation(null);
  };

  // Handle restoring draft
  const handleRestoreDraft = () => {
    setShowRestoreDialog(false);
    // The form will automatically load from localStorage
  };

  // Handle discarding draft
  const handleDiscardDraft = async () => {
    // Clear all localStorage keys
    const addListingKeys = [
      'addListingFormData',
      'addListingVisitedSteps',
      'addListingActiveSession',
      'paymentSessionId',
      'paymentType',
      'paymentVerified',
      'selectedPaymentType',
      'subscriptionDuration',
      'subscriptionPlan',
    ];
    addListingKeys.forEach(key => localStorage.removeItem(key));

    // Clear Images from IndexedDB
    try {
      await addListingImageStore.removeItem('draft-cover');
      await addListingImageStore.removeItem('draft-additional');
    } catch (err) {
      console.warn("Failed to clear image store : ", err);
    }


    setShowRestoreDialog(false);

    // Force a page reload to reset all React state
    window.location.reload();
  };

  // Remove session flag on unload (leaving the add-listing flow)
  useEffect(() => {
    const handleBeforeUnload = () => {
      localStorage.removeItem('addListingActiveSession');
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const isFullWidth =
    params.stepIndex === "presente-annonce" ||
    params.stepIndex === "mid-annonce" ||
    params.stepIndex === "finaliser-annonce";
  return (
    <MainLayout fullWidth={isFullWidth}>
      {children}

      {/* Exit Warning Dialog */}
      <Dialog open={showExitWarning} onOpenChange={setShowExitWarning}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Quitter sans enregistrer ?</DialogTitle>
            <DialogDescription>
              Vous avez des modifications non enregistrées. Si vous quittez maintenant, vous perdrez ces modifications.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelNavigation}>
              Annuler
            </Button>
            <Button variant="destructive" onClick={handleConfirmNavigation}>
              Quitter
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Restore Draft Dialog */}
      <Dialog open={showRestoreDialog} onOpenChange={setShowRestoreDialog}>
        <DialogPortal>
          <DialogOverlay className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
          <DialogPrimitive.Content className="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 bg-[#FCFBFF] p-6 shadow-xl duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg border-0">
            <DialogHeader>
              <DialogTitle className="text-gray-800 font-semibold">Brouillon trouvé</DialogTitle>
              <DialogDescription className="text-gray-600">
                Un brouillon non terminé a été trouvé. Souhaitez-vous le restaurer ?
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="gap-3">
              <Button
                variant="outline"
                onClick={handleDiscardDraft}
                className="bg-transparent border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400"
              >
                Supprimer
              </Button>
              <Button
                onClick={handleRestoreDraft}
                className="bg-[#EA580F26] border border-[#EA580F] text-[#EA580F] hover:bg-[#EA580F] hover:text-white transition-all duration-200 font-medium"
              >
                Restaurer
              </Button>
            </DialogFooter>
          </DialogPrimitive.Content>
        </DialogPortal>
      </Dialog>
    </MainLayout>
  );
};

export default CommonLayout;