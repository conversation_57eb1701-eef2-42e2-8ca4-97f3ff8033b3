import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Clock, FileText, Calendar, AlertCircle } from "lucide-react";
import { formatDate, formatCurrency, getDaysRemaining, formatDatePeriod } from '../utils/formatters';
import { Subscription, getSubscriptionStatus } from '../utils/status-helpers';
import Image from 'next/image';

type SubscriptionCardProps = {
    subscription: Subscription;
    onRenew?: (subscription: Subscription) => void;
};

const SubscriptionCard = ({ subscription, onRenew }: SubscriptionCardProps) => {
    const { status, statusText, variant } = getSubscriptionStatus(subscription);
    const daysRemaining = getDaysRemaining(subscription.end_date);

    // Calculate percentage of time remaining
    const calculateTimePercentage = () => {
        try {
            if (!subscription.start_date || !subscription.end_date) return 0;
            
            const startDate = new Date(subscription.start_date);
            const endDate = new Date(subscription.end_date);
            const now = new Date();
            const totalDuration = endDate.getTime() - startDate.getTime();
            const elapsed = now.getTime() - startDate.getTime();

            if (totalDuration <= 0) return 0;

            const percentage = 100 - (elapsed / totalDuration * 100);
            return Math.max(0, Math.min(100, percentage)); // Clamp between 0-100
        } catch (error) {
            return 0;
        }
    };

    const timeRemainingPercentage = status !== 'expired' && status !== 'pending' ? calculateTimePercentage() : 0;
    
    // Calculate progress bar color based on time remaining percentage
    const getProgressBarColor = (percentage: number, status: string, variant: string) => {
        // If expired or pending, use the default variant color
        if (status === 'expired' || status === 'pending') {
            return variant === 'success' ? 'bg-green-500' : 
                   variant === 'secondary' ? 'bg-blue-500' : 
                   variant === 'warning' ? 'bg-amber-500' : 'bg-red-500';
        }
        
        // For trial subscriptions, always use blue
        if (status === 'trial') {
            return 'bg-blue-500';
        }
        
        // For active subscriptions, change color based on percentage
        if (percentage <= 30) {
            return 'bg-red-500'; // Less than 30% - Red
        } else if (percentage <= 50) {
            return 'bg-yellow-500'; // Between 30% and 50% - Yellow
        } else {
            return 'bg-green-500'; // More than 50% - Green
        }
    };

    // Get the color class for the progress bar
    const progressBarColor = getProgressBarColor(timeRemainingPercentage, status, variant);
    
    const truncateTitle = (title: string, maxLength = 40) => {
        return title.length > maxLength ? `${title.substring(0, maxLength)}...` : title;
    };

    return (
        <Card key={subscription.id} className="overflow-hidden hover:shadow-md transition-shadow duration-300">
            <CardContent className="p-0">
                {/* Top section with image, title, and price */}
                <div className="p-4 sm:p-6">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                        {/* Listing image */}
                        <div className="w-full sm:w-20 h-20 relative overflow-hidden rounded-md shrink-0">
                            {subscription.listings?.featured_image_url ? (
                                <Image
                                    src={subscription.listings.featured_image_url}
                                    alt={subscription.listings.title || 'Hébergement'}
                                    fill
                                    sizes="(max-width: 640px) 100vw, 80px"
                                    className="object-cover"
                                />
                            ) : (
                                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                    <span className="text-gray-400 text-xs">Pas d&apos;image</span>
                                </div>
                            )}
                        </div>

                        {/* Main information */}
                        <div className="flex-grow min-w-0">
                            <h3 className="text-lg font-semibold mb-1 truncate" title={subscription.listings?.title || 'Hébergement'}>
                                {truncateTitle(subscription.listings?.title || 'Hébergement')}
                            </h3>
                            <p className="text-sm text-gray-500">
                                {subscription.is_trial ? 'Essai gratuit' : `Plan ${subscription.subscription_plan}`}
                            </p>
                        </div>

                        {/* Price and time information */}
                        <div className="flex flex-col items-end justify-between shrink-0">
                            {status === 'pending' ? (
                                <div className="flex items-center text-sm text-amber-500 mb-1">
                                    <AlertCircle className="h-4 w-4 mr-1" />
                                    <span>En attente d&apos;approbation</span>
                                </div>
                            ) : status !== 'expired' ? (
                                <div className="flex items-center text-sm text-gray-500 mb-1">
                                    <Clock className="h-4 w-4 mr-1" />
                                    <span>{daysRemaining} jours restants</span>
                                </div>
                            ) : null}
                            
                            {!subscription.is_trial && (
                                <div className="font-medium text-right">
                                    {formatCurrency(subscription.payment_amount)}
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Time remaining progress bar - only show for active subscriptions, not pending */}
                    {status !== 'expired' && status !== 'pending' && (
                        <div className="mt-4">
                            <div className="w-full bg-gray-100 rounded-full h-2">
                                <div
                                    className={`h-2 rounded-full ${progressBarColor}`}
                                    style={{ width: `${timeRemainingPercentage}%` }}
                                />
                            </div>
                        </div>
                    )}
                    
                    {/* Pending message - show for pending subscriptions */}
                    {status === 'pending' && (
                        <div className="mt-4 text-sm text-amber-500 border border-amber-200 bg-amber-50 rounded-md p-3">
                            <p className="flex items-center">
                                <AlertCircle className="h-4 w-4 mr-2" />
                                L&apos;abonnement débutera une fois l&apos;hébergement approuvé par l&apos;administrateur.
                            </p>
                        </div>
                    )}
                </div>

                {/* Divider */}
                <div className="border-t border-gray-100"></div>

                {/* Details grid */}
                <div className="p-4 sm:p-6 grid grid-cols-1 sm:grid-cols-3 gap-3 text-sm bg-gray-50">
                    <div className="flex items-start">
                        <FileText className="h-4 w-4 mr-2 mt-0.5 text-gray-400" />
                        <div>
                            <p className="text-gray-500 mb-1">N° Facture</p>
                            <p>{subscription.invoice_number || 'N/A'}</p>
                        </div>
                    </div>
                    <div className="flex items-start">
                        <Calendar className="h-4 w-4 mr-2 mt-0.5 text-gray-400" />
                        <div>
                            <p className="text-gray-500 mb-1">Période</p>
                            <p>{formatDatePeriod(subscription.start_date, subscription.end_date)}</p>
                        </div>
                    </div>
                    <div className="flex items-center justify-between sm:justify-start">
                        <div>
                            <p className="text-gray-500 mb-1">Status</p>
                            <Badge variant={variant}>{statusText}</Badge>
                        </div>

                        {/* Renew button for expired subscriptions - moved inside the status box */}
                        {status === 'expired' && onRenew && (
                            <Button
                                size="sm"
                                variant="outline"
                                onClick={() => onRenew(subscription)}
                                className="ml-auto sm:ml-4"
                            >
                                Renouveler
                            </Button>
                        )}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default SubscriptionCard;