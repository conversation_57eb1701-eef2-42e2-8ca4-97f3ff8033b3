.cameraContainer {
    position: relative;
    width: 100%;
    aspect-ratio: 4 / 3;
    background-color: #000;
    border-radius: 0.5rem;
    overflow: hidden;
}

.cameraHeader {
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cameraView {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cameraVideo {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cameraOverlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    background-color: rgba(0, 0, 0, 0.3);
}

.cameraGuide {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 60%;
    border: 2px dashed white;
    border-radius: 0.25rem;
    pointer-events: none;
}

.faceGuide {
    position: relative;
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.faceCircle {
    width: 16rem;
    height: 16rem;
    border-radius: 9999px;
    border: 2px solid white;
}

.faceInstructions {
    color: white;
    text-align: center;
    margin-top: 1rem;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 0.5rem 1rem;
    border-radius: 9999px;
}

.cameraControls {
    position: absolute;
    bottom: 1rem;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.captureButton {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    background-color: white;
    border: 3px solid rgba(255, 255, 255, 0.5);
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}

.captureButton::before {
    content: "";
    width: 80%;
    height: 80%;
    border-radius: 50%;
    background-color: white;
}

.captureButtonInner {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 9999px;
    border: 2px solid #d1d5db;
}

.switchButton {
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.tipsOverlay {
    position: absolute;
    top: 4rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 0.5rem;
    padding: 1rem;
    max-width: 28rem;
    color: white;
}

.tipsList {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}

.tipItem {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.tipBullet {
    margin-right: 0.5rem;
}

@media (max-width: 640px) {
    .faceCircle {
        width: 12rem;
        height: 12rem;
    }

    .tipsOverlay {
        width: 90%;
        max-width: none;
    }
}

/* Fix for iOS Safari */
@supports (-webkit-touch-callout: none) {
    .cameraVideo {
        transform: scaleX(-1); /* Mirror for front camera on iOS */
    }
}

/* For desktop browsers */
@media (min-width: 768px) {
    .videoMirror {
        transform: scaleX(-1); /* Mirror for front camera on desktop */
    }
}
