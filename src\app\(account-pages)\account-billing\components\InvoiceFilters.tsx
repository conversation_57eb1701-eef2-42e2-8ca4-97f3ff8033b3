"use client"

import type React from "react"
import { useState, useEffect, useRef } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Search } from "lucide-react"
import debounce from "lodash.debounce"

type InvoiceFiltersProps = {
    onSearch: (searchTerm: string) => void
    isLoading: boolean
}

const InvoiceFilters = ({ onSearch, isLoading }: InvoiceFiltersProps) => {
    const [searchTerm, setSearchTerm] = useState("")

    // Create the debounced search function just once with useRef
    const debouncedSearch = useRef(
        debounce((term: string) => {
            onSearch(term)
        }, 600),
    ).current

    // Cleanup the debounced function when component unmounts
    useEffect(() => {
        return () => {
            debouncedSearch.cancel()
        }
    }, [debouncedSearch])

    // Handle input changes
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value)
        debouncedSearch(e.target.value)
    }

    // Handle form submission
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        // When form is submitted directly (e.g., Enter key), cancel debounce and search immediately
        debouncedSearch.cancel()
        onSearch(searchTerm)
    }

    return (
        <form onSubmit={handleSubmit} className="flex gap-2">
            <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                    type="text"
                    placeholder="Rechercher par n° facture, hébergement ou plan..."
                    value={searchTerm}
                    onChange={handleChange}
                    className="pl-9"
                    disabled={isLoading}
                />
            </div>
            <Button type="submit" disabled={isLoading}>
                Rechercher
            </Button>
        </form>
    )
}

export default InvoiceFilters
