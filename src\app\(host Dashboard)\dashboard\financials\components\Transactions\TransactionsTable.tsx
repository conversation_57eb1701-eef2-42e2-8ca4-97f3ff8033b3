import React from 'react';
import { useRouter } from 'next/navigation';
import TransactionRow, { Transaction } from '../../components/shared/TransactionRow';

interface TransactionsTableProps {
  transactions: any[];
  onRowClick: (transaction: any) => void;
  activeRowId: string | null;
  setActiveRowId: (id: string) => void;
  selectedTab: 'paye' | 'prevu';
}

const TransactionsTable: React.FC<TransactionsTableProps> = ({ 
  transactions, 
  onRowClick, 
  activeRowId, 
  setActiveRowId,
  selectedTab
}) => {
  const router = useRouter();

  const handleViewAllClick = () => {
    if (selectedTab === 'paye') {
      router.push('/dashboard/financials/all-paid-transactions');
    } else {
      router.push('/dashboard/financials/all-scheduled-transactions');
    }
  };

  return (
    <div className="bg-[#F6FAFF] rounded-xl overflow-hidden border border-[#E8EAED]">

      {/* Table Header */}
      <div className="grid grid-cols-3 gap-0 bg-[#EAF4FF] px-6 py-3 text-[12px] font-semibold text-gray-700 text-center">
        <div className="truncate text-left">N° réservation</div>
        <div className="truncate">Montant</div>
        <div className="truncate">Statut</div>
      </div>


      {/* Table Rows */}
      <div>
        {transactions.map((transaction) => (
          <TransactionRow
            key={transaction.id}
            transaction={{
              ...transaction,
              reservation: transaction.reservation_number
            }}
            highlight={transaction.id === activeRowId}
            onClick={() => {
              setActiveRowId(transaction.id);
              onRowClick(transaction);
            }}
          />
        ))}
      </div>


      {/* Bottom Button */}
      <div className="px-4 py-5 bg-white border-t border-[#E8EAED] flex justify-center">
        <button 
          onClick={handleViewAllClick}
          className="w-full max-w-xs border border-black rounded-lg py-2 text-[14px] font-semibold text-black hover:bg-gray-50 transition"
        >
          Voir tous les payants
        </button>
      </div>
    </div>
  );
};

export default TransactionsTable; 