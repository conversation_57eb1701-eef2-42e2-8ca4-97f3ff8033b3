/**
 * Format a date string to a localized display format
 */
export const formatDate = (dateString: string | null): string => {
    if (!dateString) return 'N/A';

    try {
        const date = new Date(dateString);
        return new Intl.DateTimeFormat('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        }).format(date);
    } catch (error) {
        console.error('Error formatting date:', error);
        return 'N/A';
    }
};

/**
 * Format a date to show day with abbreviated month name and year
 */
export const formatDateWithMonth = (dateString: string | null): string => {
    if (!dateString) return 'N/A';

    try {
        const date = new Date(dateString);
        return new Intl.DateTimeFormat('fr-FR', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
        }).format(date);
    } catch (error) {
        console.error('Error formatting date with month:', error);
        return 'N/A';
    }
};

/**
 * Format a date period with an arrow between start and end dates
 */
export const formatDatePeriod = (startDate: string | null, endDate: string | null): string => {
    if (!startDate || !endDate) {
        return 'Sera définie après approbation';
    }
    
    return `${formatDateWithMonth(startDate)} → ${formatDateWithMonth(endDate)}`;
};

/**
 * Format a numeric value to a currency string
 */
export const formatCurrency = (amount: number | null): string => {
    if (amount === null || amount === undefined) return '0,00 €';

    return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'TND',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
};

/**
 * Calculate the days remaining until a given date
 */
export const getDaysRemaining = (endDateStr: string | null): number => {
    if (!endDateStr) return 0;

    try {
        const now = new Date();
        const endDate = new Date(endDateStr);

        // Set both dates to the start of the day for accurate day calculation
        now.setHours(0, 0, 0, 0);
        endDate.setHours(0, 0, 0, 0);

        const diffTime = endDate.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return diffDays > 0 ? diffDays : 0;
    } catch (error) {
        console.error('Error calculating days remaining:', error);
        return 0;
    }
};