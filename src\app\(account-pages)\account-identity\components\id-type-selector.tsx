"use client"
import { Car, CreditCard, FileText } from "lucide-react"
import Link from "next/link"

type IdType = "cin" | "passport" | "permit"

interface IdTypeSelectorProps {
    selectedType: IdType | null
    onSelectType: (type: IdType) => void
    onContinue: () => void
    onBack: () => void
}

export function IdTypeSelector({ selectedType, onSelectType, onContinue, onBack }: IdTypeSelectorProps) {
    return (
        <div className="w-full max-w-md mx-auto">
            <h2 className="text-2xl font-bold mb-6">Choisissez un type d&apos;ID à ajouter</h2>

            <div className="space-y-3 mb-6">
                <div
                    className={`flex items-center p-4 border rounded-md cursor-pointer transition-colors ${
                        selectedType === "permit" ? "border-orange-300 bg-orange-50" : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => onSelectType("permit")}
                >
                    <Car className="w-5 h-5 mr-3 text-gray-600" />
                    <span className="font-medium">Permis de conduire</span>
                </div>

                <div
                    className={`flex items-center p-4 border rounded-md cursor-pointer transition-colors ${
                        selectedType === "passport" ? "border-orange-300 bg-orange-50" : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => onSelectType("passport")}
                >
                    <FileText className="w-5 h-5 mr-3 text-gray-600" />
                    <span className="font-medium">Passeport</span>
                </div>

                <div
                    className={`flex items-center p-4 border rounded-md cursor-pointer transition-colors ${
                        selectedType === "cin" ? "border-orange-300 bg-orange-50" : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => onSelectType("cin")}
                >
                    <CreditCard className="w-5 h-5 mr-3 text-gray-600" />
                    <span className="font-medium">Carte d&apos;identité</span>
                </div>
            </div>

            <p className="text-sm text-gray-600 mb-8">
                Votre identifiant sera traité conformément à notre{" "}
                <Link href="/privacy" className="text-black font-medium">
                    politique de confidentialité
                </Link>{" "}
                et ne sera pas partagé avec votre hôte ou vos invités.
            </p>

            <div className="flex justify-between">
                <button onClick={onBack} className="px-4 py-2 text-gray-600 hover:text-gray-900 font-medium">
                    Retour
                </button>

                <button
                    onClick={onContinue}
                    disabled={!selectedType}
                    className={`px-6 py-2 rounded-md bg-black text-white font-medium ${
                        !selectedType ? "opacity-50 cursor-not-allowed" : "hover:bg-gray-800"
                    }`}
                >
                    Continuer
                </button>
            </div>
        </div>
    )
}
