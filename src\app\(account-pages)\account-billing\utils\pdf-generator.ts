import { jsPD<PERSON> } from 'jspdf';
import autoTable from 'jspdf-autotable';
import { formatDate, formatCurrency } from './formatters';

// Add TypeScript declaration for the lastAutoTable property
declare module 'jspdf' {
  interface jsPDF {
    lastAutoTable?: {
      finalY: number;
    };
  }
}

// Company information - replace with your actual information
const COMPANY = {
    LOGO: 'https://api.almindharbooking.com/storage/v1/object/public/facturation//AlmindharBooking_Logo.svg',
    NAME: 'Almindhar Booking',
    ADDRESS: 'Rue 14 Janvier Immo Hadj Kacem 4ème étage',
    CITY: 'Hammam Sousse',
    COUNTRY: 'Tunisie',
    PHONE: '+216 12 345 678',
    EMAIL: '<EMAIL>',
    TAX_ID: '*********',
    SUPPORT_EMAIL: '<EMAIL>',
    WEBSITE: 'https://www.almindharbooking.com/'
};

// Invoice type to match the data we receive
type Invoice = {
    id: string;
    invoice_number: string;
    start_date: string;
    end_date: string;
    subscription_plan: string;
    payment_amount: number;
    listings: {
        id: string;
        title: string;
    };
    profile?: {
        fullname: string;
        phone_number: string;
        email: string;
    };
};

/**
 * Adds company logo to the PDF
 * @param doc jsPDF document
 * @param x X position
 * @param y Y position
 * @param width Width of the logo
 * @param height Height of the logo
 * @returns Promise that resolves when the logo is loaded and added
 */
async function addCompanyLogo(doc: jsPDF, x: number, y: number, width: number, height: number): Promise<void> {
    // Alternative approach using a hard-coded data URL for the orange Almindhar logo
    // This ensures the logo is always available without relying on external fetches
    try {
        // Better orange logo with text, resembling the screenshot
        const orangeLogoDataUrl = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzMDAgODAiPjxzdHlsZT4uc3Qwe2ZpbGw6I2U4NWQwNDt9IC5zdDF7Zm9udC1mYW1pbHk6QXJpYWwsIHNhbnMtc2VyaWY7IGZvbnQtd2VpZ2h0OmJvbGQ7fTwvc3R5bGU+PGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMCwwKSI+PGNpcmNsZSBjbGFzcz0ic3QwIiBjeD0iMzAiIGN5PSI0MCIgcj0iMjUiLz48Y2lyY2xlIGNsYXNzPSJzdDAiIGN4PSI2NSIgY3k9IjQwIiByPSIyNSIvPjxyZWN0IGNsYXNzPSJzdDAiIHg9IjMwIiB5PSIxNSIgd2lkdGg9IjM1IiBoZWlnaHQ9IjUwIi8+PHRleHQgeD0iMTA1IiB5PSIzNSIgZm9udC1zaXplPSIyNCIgY2xhc3M9InN0MCBzdDEiPmFsbWluZGhhcjwvdGV4dD48dGV4dCB4PSIxMzAiIHk9IjYwIiBmb250LXNpemU9IjI0IiBjbGFzcz0ic3QwIHN0MSI+Ym9va2luZzwvdGV4dD48L2c+PC9zdmc+';
        
        // Add the logo from the data URL
        doc.addImage(orangeLogoDataUrl, 'SVG', x, y, width, height);
        return Promise.resolve();
    } catch (error) {
        console.error('Error using hardcoded logo:', error);
        
        // Try the original approach as fallback
        try {
            const logoUrl = 'https://api.almindharbooking.com/storage/v1/object/public/facturation//AlmindharBooking_Logo.svg';
            
            // Create a temporary image to load the SVG
            const img = new Image();
            img.crossOrigin = 'Anonymous';  // Enable cross-origin loading
            
            return new Promise((resolve) => {
                img.onload = () => {
                    // Create a canvas to draw the image
                    const canvas = document.createElement('canvas');
                    canvas.width = img.width || 300;
                    canvas.height = img.height || 80;
                    const ctx = canvas.getContext('2d');
                    if (ctx) {
                        ctx.drawImage(img, 0, 0);
                        try {
                            // Get data URL from canvas
                            const dataUrl = canvas.toDataURL('image/png');
                            doc.addImage(dataUrl, 'PNG', x, y, width, height);
                        } catch (e) {
                            console.error('Failed to add image to PDF:', e);
                        }
                    }
                    resolve();
                };
                
                img.onerror = () => {
                    console.error('Failed to load image from URL:', logoUrl);
                    resolve(); // Resolve without adding image
                };
                
                img.src = logoUrl;
            });
        } catch (error) {
            console.error('All logo loading attempts failed:', error);
            return Promise.resolve(); // Continue without logo
        }
    }
}

/**
 * Generates a PDF for a single invoice
 * @param invoice The invoice data to generate a PDF for
 * @returns Blob of the generated PDF
 */
export async function generateInvoicePDF(invoice: Invoice): Promise<Blob> {
    // Create a new PDF document
    const doc = new jsPDF();

    // Set default font
    doc.setFont('helvetica');

    // Add current date
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();

    // Set up document metadata
    doc.setProperties({
        title: `Facture ${invoice.invoice_number}`,
        subject: 'Invoice',
        author: COMPANY.NAME,
        keywords: 'invoice, facture, booking',
        creator: COMPANY.NAME
    });

    // Page margins and dimensions
    const margin = 20;
    const pageWidth = doc.internal.pageSize.width;
    let y = margin;

    // Add company logo - centered at the top of the page with increased width
    const logoWidth = 110;
    const logoHeight = 30;
    const logoX = (pageWidth - logoWidth) / 2; // Center the logo horizontally
    await addCompanyLogo(doc, logoX, y, logoWidth, logoHeight);

    // Add invoice title - centered below the logo
    y += logoHeight + 15; // Provide enough space below the logo
    doc.setFontSize(20); // Reduced from 24 to 20
    doc.setFont('helvetica', 'bold');
    doc.text('FACTURE / INVOICE', pageWidth / 2, y, { align: 'center' });

    // Add a horizontal line below the title
    y += 5;
    doc.setDrawColor(220, 220, 220); // Light gray
    doc.setLineWidth(0.5);
    doc.line(margin, y, pageWidth - margin, y);
    
    // Add company information in a balanced layout
    y += 12; // Increased spacing after the line
    doc.setFontSize(11); // Slightly smaller font
    doc.setFont('helvetica', 'normal');
    
    // Left side - Company info
    const companyInfoLines = [
        COMPANY.NAME,
        COMPANY.ADDRESS,
        `${COMPANY.CITY}, ${COMPANY.COUNTRY}`,
        `Tel: ${COMPANY.PHONE}`,
        `Email: ${COMPANY.EMAIL}`,
        `TVA/Tax ID: ${COMPANY.TAX_ID}`
    ];
    
    // Add each line with spacing
    let lineY = y;
    for (const line of companyInfoLines) {
        doc.text(line, margin, lineY);
        lineY += 5.5; // Increased spacing between lines
    }

    // Right side - Invoice details
    const invoiceDetailLines = [
        `N° Facture: ${invoice.invoice_number}`,
        `Date d'émission: ${formatDate(invoice.start_date)}`,
        `Date d'échéance: Payé`
    ];
    
    // Add each line with spacing
    lineY = y;
    for (const line of invoiceDetailLines) {
        doc.text(line, pageWidth - margin, lineY, { align: 'right' });
        lineY += 5.5; // Increased spacing between lines
    }

    // Calculate the new y position based on the client info height
    // Use the highest y-coordinate from either company info or invoice details
    y = Math.max(y + companyInfoLines.length * 5.5, lineY) + 5;

    // Add client information with improved spacing
    y += 20; // Increased from 40 to provide proper separation
    doc.setFont('helvetica', 'bold');
    doc.text('FACTURÉ À / BILLED TO:', margin, y);
    y += 8;
    doc.setFont('helvetica', 'normal');
    
    const clientInfoLines = [
        invoice.profile?.fullname || 'Client',
        `Tel: ${invoice.profile?.phone_number || 'N/A'}`,
        `Email: ${invoice.profile?.email || 'N/A'}`
    ];
    
    // Add each line with spacing
    lineY = y;
    for (const line of clientInfoLines) {
        doc.text(line, margin, lineY);
        lineY += 5.5; // Increased spacing between lines
    }

    // Add invoice table - adjusted spacing
    y = lineY + 15; // Increased spacing after client info

    // Use the imported autoTable function directly
    autoTable(doc, {
        startY: y,
        head: [['DESCRIPTION', 'DATES', 'PLAN', 'MONTANT']],
        body: [[
            `Hébergement: ${invoice.listings?.title || 'N/A'}`,
            `Du ${formatDate(invoice.start_date)}\nAu ${formatDate(invoice.end_date)}`,
            invoice.subscription_plan,
            formatCurrency(invoice.payment_amount)
        ]],
        theme: 'grid',
        headStyles: {
            fillColor: [41, 128, 185],
            textColor: 255,
            fontStyle: 'bold'
        },
        styles: {
            overflow: 'linebreak',
            cellPadding: 5
        },
        columnStyles: {
            0: { cellWidth: 80 },
            1: { cellWidth: 50 },
            2: { cellWidth: 25 },
            3: { cellWidth: 35, halign: 'right' }
        }
    });

    // Get the y position after the table
    const tableEndY = doc.lastAutoTable?.finalY || y;
    y = tableEndY + 10;

    // Add total - improved formatting and alignment
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('TOTAL:', 100, y);
    doc.setFontSize(14);  // Larger font for the total amount
    doc.text(formatCurrency(invoice.payment_amount), 190, y, { align: 'right' });
    doc.setFontSize(12);

    // Add payment terms
    y += 20;
    doc.setFont('helvetica', 'bold');
    doc.text('MODALITÉS DE PAIEMENT / PAYMENT TERMS:', margin, y);
    y += 8;
    doc.setFont('helvetica', 'normal');
    doc.text('Cette facture est marquée comme payée. Aucun paiement supplémentaire n\'est requis.', margin, y);
    y += 6.5; // Increased from 6 to 6.5
    doc.text('This invoice is marked as paid. No additional payment is required.', margin, y);

    // Add payment method
    y += 15;
    doc.setFont('helvetica', 'bold');
    doc.text('MODE DE PAIEMENT / PAYMENT METHOD:', margin, y);
    y += 8;
    doc.setFont('helvetica', 'normal');
    doc.text('Paiement en ligne / Online payment', margin, y);


    // Add footer
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(10);
    doc.text(`${COMPANY.NAME} © ${currentYear}`, doc.internal.pageSize.width / 2, doc.internal.pageSize.height - 10, { align: 'center' });

    // Return the PDF as a blob
    return doc.output('blob');
}

/**
 * Generates a PDF for multiple invoices
 * @param invoices Array of invoices to include in the PDF
 * @returns Blob of the generated PDF
 */
export async function generateBulkInvoicePDF(invoices: Invoice[]): Promise<Blob> {
    if (invoices.length === 0) {
        throw new Error('No invoices to generate PDF');
    }

    if (invoices.length === 1) {
        return generateInvoicePDF(invoices[0]);
    }

    // Create a new PDF document
    const doc = new jsPDF();

    // Set default font
    doc.setFont('helvetica');

    // Set up document metadata
    doc.setProperties({
        title: `Factures ${new Date().toISOString().split('T')[0]}`,
        subject: 'Invoices',
        author: COMPANY.NAME,
        keywords: 'invoice, facture, booking, bulk',
        creator: COMPANY.NAME
    });

    // For each invoice, add a new page (except the first one)
    for (let index = 0; index < invoices.length; index++) {
        const invoice = invoices[index];
        if (index > 0) {
            doc.addPage();
        }

        // Generate single invoice on this page
        // Page margins and dimensions
        const margin = 20;
        const pageWidth = doc.internal.pageSize.width;
        let y = margin;

        // Add company logo - centered at the top of the page with increased width
        const logoWidth = 90;
        const logoHeight = 30;
        const logoX = (pageWidth - logoWidth) / 2; // Center the logo horizontally
        await addCompanyLogo(doc, logoX, y, logoWidth, logoHeight);

        // Add invoice title - centered below the logo
        y += logoHeight + 15; // Provide enough space below the logo
        doc.setFontSize(20); // Reduced from 24 to 20
        doc.setFont('helvetica', 'bold');
        doc.text('FACTURE / INVOICE', pageWidth / 2, y, { align: 'center' });

        // Add a horizontal line below the title
        y += 5;
        doc.setDrawColor(220, 220, 220); // Light gray
        doc.setLineWidth(0.5);
        doc.line(margin, y, pageWidth - margin, y);
        
        // Add company information in a balanced layout
        y += 12; // Increased spacing after the line
        doc.setFontSize(11); // Slightly smaller font
        doc.setFont('helvetica', 'normal');
        
        // Left side - Company info
        const companyInfoLines = [
            COMPANY.NAME,
            COMPANY.ADDRESS,
            `${COMPANY.CITY}, ${COMPANY.COUNTRY}`,
            `Tel: ${COMPANY.PHONE}`,
            `Email: ${COMPANY.EMAIL}`,
            `TVA/Tax ID: ${COMPANY.TAX_ID}`
        ];
        
        // Add each line with spacing
        let lineY = y;
        for (const line of companyInfoLines) {
            doc.text(line, margin, lineY);
            lineY += 5.5; // Increased spacing between lines
        }

        // Right side - Invoice details
        const invoiceDetailLines = [
            `N° Facture: ${invoice.invoice_number}`,
            `Date d'émission: ${formatDate(invoice.start_date)}`,
            `Date d'échéance: Payé`
        ];
        
        // Add each line with spacing
        lineY = y;
        for (const line of invoiceDetailLines) {
            doc.text(line, pageWidth - margin, lineY, { align: 'right' });
            lineY += 5.5; // Increased spacing between lines
        }

        // Calculate the new y position based on the client info height
        // Use the highest y-coordinate from either company info or invoice details
        y = Math.max(y + companyInfoLines.length * 5.5, lineY) + 5;

        // Add client information with improved spacing
        y += 20; // Increased from 40 to provide proper separation
        doc.setFont('helvetica', 'bold');
        doc.text('FACTURÉ À / BILLED TO:', margin, y);
        y += 8;
        doc.setFont('helvetica', 'normal');
        
        const clientInfoLines = [
            invoice.profile?.fullname || 'Client',
            `Tel: ${invoice.profile?.phone_number || 'N/A'}`,
            `Email: ${invoice.profile?.email || 'N/A'}`
        ];
        
        // Add each line with spacing
        lineY = y;
        for (const line of clientInfoLines) {
            doc.text(line, margin, lineY);
            lineY += 5.5; // Increased spacing between lines
        }

        // Add invoice table - adjusted spacing
        y = lineY + 15; // Increased spacing after client info

        // Use the imported autoTable function directly
        autoTable(doc, {
            startY: y,
            head: [['DESCRIPTION', 'DATES', 'PLAN', 'MONTANT']],
            body: [[
                `Hébergement: ${invoice.listings?.title || 'N/A'}`,
                `Du ${formatDate(invoice.start_date)}\nAu ${formatDate(invoice.end_date)}`,
                invoice.subscription_plan,
                formatCurrency(invoice.payment_amount)
            ]],
            theme: 'grid',
            headStyles: {
                fillColor: [41, 128, 185],
                textColor: 255,
                fontStyle: 'bold'
            },
            styles: {
                overflow: 'linebreak',
                cellPadding: 5
            },
            columnStyles: {
                0: { cellWidth: 80 },
                1: { cellWidth: 50 },
                2: { cellWidth: 25 },
                3: { cellWidth: 35, halign: 'right' }
            }
        });

        // Get the y position after the table
        const tableEndY = doc.lastAutoTable?.finalY || y;
        y = tableEndY + 10;

        // Add total - improved formatting and alignment
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text('TOTAL:', 100, y);
        doc.setFontSize(14);  // Larger font for the total amount
        doc.text(formatCurrency(invoice.payment_amount), 190, y, { align: 'right' });
        doc.setFontSize(12);

        // Add payment terms
        y += 20;
        doc.setFont('helvetica', 'bold');
        doc.text('MODALITÉS DE PAIEMENT / PAYMENT TERMS:', margin, y);
        y += 8;
        doc.setFont('helvetica', 'normal');
        doc.text('Cette facture est marquée comme payée. Aucun paiement supplémentaire n\'est requis.', margin, y);
        y += 6.5; // Increased from 6 to 6.5
        doc.text('This invoice is marked as paid. No additional payment is required.', margin, y);

        // Add payment method
        y += 15;
        doc.setFont('helvetica', 'bold');
        doc.text('MODE DE PAIEMENT / PAYMENT METHOD:', margin, y);
        y += 8;
        doc.setFont('helvetica', 'normal');
        doc.text('Paiement en ligne / Online payment', margin, y);

        // Add footer
        const currentYear = new Date().getFullYear();
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(10);
        doc.text(`${COMPANY.NAME} © ${currentYear} | Page ${index + 1} of ${invoices.length}`, doc.internal.pageSize.width / 2, doc.internal.pageSize.height - 10, { align: 'center' });
    }

    // Return the PDF as a blob
    return doc.output('blob');
}