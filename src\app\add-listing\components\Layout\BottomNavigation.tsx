"use client";

import React from "react";
import { useParams, useRouter } from "next/navigation";
import { ADD_LISTING_STEPS } from "../../[[...stepIndex]]/steps";
import { useFormContext } from "../../FormContext";
import addListingImageStore from "../../addListingImageStore";

// Extend window type for debugging
declare global {
    interface Window {
        __FORM_ERRORS__?: any;
    }
}

export const BottomNavigation: React.FC = () => {
    const params = useParams();
    const router = useRouter();
    const { validateStep, formData } = useFormContext();
    const stepIndexValue = Array.isArray(params.stepIndex) ? params.stepIndex[0] : params.stepIndex;
    const currentStepIdx = ADD_LISTING_STEPS.findIndex(s => s.key === stepIndexValue);
    const isIntro = stepIndexValue === "intro";
    const isFirstStep = currentStepIdx === 0;
    const isLastStep = currentStepIdx === ADD_LISTING_STEPS.length - 1;
    const nextHref = !isLastStep ? `/add-listing/${ADD_LISTING_STEPS[currentStepIdx + 1].key}` : undefined;
    const backHref = !isFirstStep ? `/add-listing/${ADD_LISTING_STEPS[currentStepIdx - 1].key}` : undefined;
    const [loading, setLoading] = React.useState(false);

    // Defensive: If step is not found, render nothing
    if (currentStepIdx === -1) return null;

    const currentStepKey = ADD_LISTING_STEPS[currentStepIdx].key;

    const handleBackClick = () => {
        if (!isFirstStep && backHref) {
            router.replace(backHref);
        }
    };

    const handleNextClick = () => {
        // Validate current step before navigating
        if (!isPublishStep && nextHref) {
            const isValid = validateStep(currentStepKey);
            if (isValid) {
                router.replace(nextHref);
            }
        }
    };

    // Utility: check if current step is the publish step (add-payments)
    const PUBLISH_STEP_KEY = "add-payments";
    const isPublishStep = currentStepKey === PUBLISH_STEP_KEY;

    const handlePublish = async () => {
        const isValid = validateStep(currentStepKey);
        if (!isValid) return;
        setLoading(true);
        try {
            // Gather images from the correct store
            const storedCover = await addListingImageStore.getItem<any>("draft-cover");
            const coverFile = storedCover
                ? new File([new Uint8Array(storedCover.data)], storedCover.name, {
                    type: storedCover.type,
                    lastModified: storedCover.lastModified,
                })
                : null;
            const additionalFiles = (await addListingImageStore.getItem<any[]>("draft-additional")) || [];
            // Reconstruct File objects for additional images
            const additionalFileObjs: File[] = await Promise.all(
                additionalFiles.map(async (item) => {
                    if (item instanceof File) return item;
                    const blob = new Blob([new Uint8Array(item.data)], { type: item.type });
                    return new File([blob], item.name, { type: item.type, lastModified: item.lastModified });
                })
            );
            // --- DISCOUNT MAPPING LOGIC START ---
            // Map discount keys to a single discounts object
            const discounts: Record<string, { mode: "percent" | "fixed"; value: number | null }> = {};
            if (formData.weeklyDiscount) discounts.weekly = formData.weeklyDiscount;
            if (formData.monthlyDiscount) discounts.monthly = formData.monthlyDiscount;
            if (formData.firstBookingDiscount) discounts.firstBooking = formData.firstBookingDiscount;
            // Build the payload, omitting the individual discount keys
            const listingDataToSend = {
                ...formData,
                discounts,
            };
            delete listingDataToSend.weeklyDiscount;
            delete listingDataToSend.monthlyDiscount;
            delete listingDataToSend.firstBookingDiscount;
            // --- DISCOUNT MAPPING LOGIC END ---
            // Build FormData
            const fd = new FormData();
            fd.append("listingData", JSON.stringify(listingDataToSend));
            if (coverFile) fd.append("coverImage", coverFile);
            additionalFileObjs.forEach((file) => fd.append("placeImages", file));
            // Call API
            const res = await fetch("/api/listing", {
                method: "POST",
                body: fd,
            });
            if (res.ok) {
                // Clear all add-listing and payment-related keys from localStorage
                const addListingKeys = [
                    'addListingFormData',
                    'addListingActiveSession',
                    'addListingVisitedSteps',
                    'paymentSessionId',
                    'paymentType',
                    'paymentVerified',
                    'selectedPaymentType',
                    'subscriptionDuration',
                    'subscriptionPlan',
                ];
                addListingKeys.forEach(key => localStorage.removeItem(key));
                // Clear images from IndexedDB
                try {
                    await addListingImageStore.removeItem('draft-cover');
                    await addListingImageStore.removeItem('draft-additional');
                } catch (cleanupErr) {
                    // Log but do not block redirect
                    console.warn("Failed to clear add listing image store:", cleanupErr);
                }
                router.replace("/add-listing/congratulations");
            } else {
                const data = await res.json();
                alert(data.error || "Erreur lors de la publication de l'annonce.");
            }
        } catch (err) {
            alert("Erreur inattendue lors de la publication. " + (err));
            console.error("Publish error:", err);
        } finally {
            setLoading(false);
        }
    };

    if (isIntro) {
        return (
            <div className="w-full p-4 flex md:justify-end">
                <button
                    onClick={() => { if (nextHref) router.replace(nextHref); }}
                    className="block w-full md:w-auto md:inline-block bg-booking-orange text-white font-bold text-[16px] rounded-[4px] min-h-[44px] px-6 md:px-8"
                >
                    Commencer
                </button>
            </div>
        );
    }

    // Special case: Congratulations step
    if (currentStepKey === "congratulations") {
        return (
            <div className="max-w-7xl mx-auto px-4 py-3 flex justify-end">
                <button
                    onClick={() => router.replace("/dashboard/properties")}
                    className="px-5 sm:px-4 md:px-8 py-3 rounded-[4px] bg-booking-orange text-white font-medium text-base min-h-[44px] min-w-[120px] hover:bg-orange-600 flex items-center justify-center gap-2"
                >
                    Continue
                </button>
            </div>
        );
    }

    return (
        <div className="2xl:max-w-[113rem] md:max-w-[80rem] mx-auto  py-3 flex justify-between items-center">
            {/* Left: Retour button */}
            <div>
                {!isFirstStep && (
                    <button
                        onClick={handleBackClick}
                        className={`
                            px-4 py-3 text-black font-medium text-base bg-transparent shadow-none
                            min-h-[44px] min-w-[80px]
                        `}
                    >
                        Retour
                    </button>
                )}
            </div>
            {/* Right: Suivant/Publier button */}
            <div className="px-5">
                <button
                    onClick={isPublishStep ? handlePublish : handleNextClick}
                    disabled={isPublishStep ? loading : false}
                    className={`
                        !px-5 !sm:px-4 !md:px-8 py-3 rounded-[4px] bg-booking-orange text-white font-medium text-base
                        min-h-[44px] min-w-[100px]
                        hover:bg-orange-600
                        flex items-center justify-center gap-2
                        ${isPublishStep ? '' : ''}
                        ${loading ? 'opacity-60 cursor-not-allowed' : ''}
                    `}
                >
                    {isPublishStep && loading ? (
                        <span>Publication...</span>
                    ) : isPublishStep ? "Publier" : "Suivant"}
                </button>
            </div>
        </div>
    );
};

export default BottomNavigation;