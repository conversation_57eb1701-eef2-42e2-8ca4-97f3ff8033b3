"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { VerificationStatus } from "./verification-status"
import { Button } from "@/components/ui/button"
import { Shield, Info, Lock } from "lucide-react"

// Import the VerificationState type from verification-status
type VerificationState = "pending" | "verified" | "rejected" | "not_submitted"

export default function IdentityPage() {
  const router = useRouter()
  const [showForm, setShowForm] = useState(false)
  const [showIntro, setShowIntro] = useState(true)
  const [verificationStatus, setVerificationStatus] = useState<VerificationState>("not_submitted")
  const [loading, setLoading] = useState(true)
  const [dbStatus, setDbStatus] = useState<string | null>(null)
  const [rejectionReason, setRejectionReason] = useState<string | null>(null)

  useEffect(() => {
    fetchVerificationStatus()
  }, [])

  // Fetch verification status from API
  const fetchVerificationStatus = async () => {
    try {
      const response = await fetch("/api/identity-verification/status")

      if (!response.ok) {
        const errorData = await response.json()
        console.error("API error:", errorData)

        // If 401 unauthorized, just set not_submitted
        if (response.status === 401) {
          setVerificationStatus("not_submitted")
          setShowForm(false)
          setShowIntro(true)
        } else {
          // For any other error, also set a default state to avoid infinite loading
          setVerificationStatus("not_submitted")
          setShowForm(false)
          setShowIntro(true)
        }

        setLoading(false)
        return
      }

      const data = await response.json()

      // Store the raw database status
      setDbStatus(data.dbStatus)
      console.log("Raw DB status:", data.dbStatus)
      console.log("UI status:", data.status)
      console.log("Rejection reason:", data.rejection_reason)

      // Set rejection reason if available
      setRejectionReason(data.rejection_reason)

      // Set UI verification status
      setVerificationStatus(data.status as VerificationState)

      // Show appropriate view based on verification state
      if (data.status === "not_submitted") {
        setShowIntro(true)
        setShowForm(false)
      } else {
        setShowIntro(false)
        setShowForm(false)
      }

      setLoading(false)
    } catch (error) {
      console.error("Error fetching verification status:", error)
      setVerificationStatus("not_submitted")
      setShowIntro(true)
      setShowForm(false)
      setLoading(false)
    }
  }

  // Handle successful form submission
  const handleFormSubmitted = (status: VerificationState) => {
    setVerificationStatus(status)
    setShowForm(false)
    setShowIntro(false)
  }

  // Handle resubmission when verification is rejected
  const handleResubmit = () => {
    setShowForm(true)
    setShowIntro(false)
  }

  // Handle click on "Se faire vérifier" button in intro screen
  const handleStartVerification = () => {
    setShowIntro(false)
    setShowForm(true)
  }

  // Handle click on "Se faire vérifier" button in policy screen
  const handleProceedToVerification = () => {
    router.push("/account-identity/verification-form")
  }

  if (loading) {
    return (
        <div className="p-8">
          <div className="flex items-center justify-center py-10">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-4"></div>
              <p className="text-gray-500">Chargement...</p>
            </div>
          </div>
        </div>
    )
  }

  // If the status is exactly "approuved", only show the verification status component
  if (dbStatus === "approuved") {
    return (
        <div className="p-8">
          <h2 className="text-2xl font-medium text-black mb-8">Vérification d&apos;identité</h2>

          <div className="bg-white rounded-lg border border-gray-200 p-6 max-w-2xl">
            <div className="text-center">
              <div className="flex flex-col items-center text-center p-4">
                <div className="w-20 h-20 rounded-full bg-green-100 flex items-center justify-center mb-4">
                  <svg
                      className="w-10 h-10 text-green-500"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                        d="M5 13L9 17L19 7"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                    />
                  </svg>
                </div>
                <h3 className="text-xl font-medium text-gray-900 mb-2">Vérifié</h3>
                <p className="text-gray-500 mb-4">
                  Votre identité a été vérifiée avec succès. Vous avez maintenant accès à toutes les fonctionnalités.
                </p>

                <div className="p-4 bg-green-50 rounded-lg border border-green-100 max-w-md">
                  <p className="text-green-800 text-sm">
                    <span className="font-medium">Compte vérifié</span> - Votre badge de vérification est maintenant
                    visible sur votre profil.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
    )
  }

  // Initial screen - "Your identity isn't verified"
  if (showIntro) {
    return (
        <div className="p-8">
          <h2 className="text-2xl font-medium text-black mb-8">Vérification d&apos;identité</h2>

          <div className="bg-white rounded-lg border border-gray-200 p-6 max-w-2xl">
            <div className="p-4">
              <h3 className="text-xl font-bold text-black mb-3">Votre identité n&apos;est pas vérifiée</h3>
              <p className="text-gray-600 mb-6">
                Vous devrez vérifier votre identité pour réserver ou héberger sur Almindhar booking. Faites-le dès
                maintenant pour prendre une longueur d&apos;avance.
              </p>

              <Button onClick={handleStartVerification} className="bg-black hover:bg-gray-800 text-white">
                Se faire vérifier
              </Button>
            </div>
          </div>
        </div>
    )
  }

  // Second screen - "Ajoutons votre pièce d'identité gouvernementale"
  if (showForm) {
    return (
        <div className="p-8">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <h2 className="text-2xl font-bold mb-6">Ajoutons votre pièce d&apos;identité gouvernementale</h2>

                <div className="mb-6">
                  <p className="text-gray-600 mb-4">
                    Nous vous demanderons d&apos;ajouter une pièce d&apos;identité officielle. Cette étape permet de vérifier votre
                    identité.
                  </p>

                  <p className="text-gray-600 mb-6">
                    Selon votre pays d&apos;origine, vous pouvez ajouter un permis de conduire, un passeport ou une carte
                    nationale d&apos;identité.
                  </p>
                </div>

                <div className="flex justify-center mb-8">
                  <div className="relative w-full max-w-md h-64 lg:h-80">
                    <Image
                        src="/img.png"
                        alt="Vérification d'identité"
                        fill
                        style={{ objectFit: "contain" }}
                        priority
                    />
                  </div>
                </div>

                <div className="flex justify-center mt-6">
                  <Button onClick={handleProceedToVerification} className="bg-black hover:bg-gray-800 text-white">
                    Se faire vérifier
                  </Button>
                </div>
              </div>

              <div className="lg:col-span-1">
                <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
                  <h3 className="text-lg font-bold mb-4">La confidentialité de vos données</h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Nous nous efforçons de préserver la confidentialité et la sécurité des données que vous partagez au
                    cours de ce processus. Pour en savoir plus, consultez notre
                  </p>
                  <a href="/privacy" className="text-black font-medium text-sm inline-flex items-center">
                    politique de confidentialité
                  </a>
                </div>

                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-bold mb-4">Comment fonctionne la vérification d&apos;identité</h3>
                  <ul className="space-y-4">
                    <li className="flex items-start">
                      <div className="bg-gray-100 rounded-full p-1 mr-3 mt-0.5">
                        <Shield className="h-4 w-4 text-gray-500" />
                      </div>
                      <div className="text-sm text-gray-600">
                        Nous utilisons une technologie de pointe pour vérifier votre identité
                      </div>
                    </li>
                    <li className="flex items-start">
                      <div className="bg-gray-100 rounded-full p-1 mr-3 mt-0.5">
                        <Lock className="h-4 w-4 text-gray-500" />
                      </div>
                      <div className="text-sm text-gray-600">
                        Vos données sont cryptées et stockées de manière sécurisée
                      </div>
                    </li>
                    <li className="flex items-start">
                      <div className="bg-gray-100 rounded-full p-1 mr-3 mt-0.5">
                        <Info className="h-4 w-4 text-gray-500" />
                      </div>
                      <div className="text-sm text-gray-600">La vérification prend généralement 24 à 48 heures</div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
    )
  }

  // For all other statuses, show the verification status component
  return (
      <div className="p-8">
        <h2 className="text-2xl font-medium text-black mb-8">Vérification d&apos;identité</h2>

        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <VerificationStatus
              status={verificationStatus}
              onResubmit={handleResubmit}
              rejectionReason={rejectionReason || ""}
          />
        </div>
      </div>
  )
}
