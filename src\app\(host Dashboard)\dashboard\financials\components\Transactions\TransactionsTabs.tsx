import React from 'react';

interface TransactionsTabsProps {
  selectedTab: 'paye' | 'prevu';
  onTabChange: (tab: 'paye' | 'prevu') => void;
}

const TransactionsTabs: React.FC<TransactionsTabsProps> = ({ selectedTab, onTabChange }) => {
  return (
    <div className="w-full">
      <div className="text-lg font-bold mb-2">Liste des transactions</div>
      <div className="flex gap-6 items-end border-b border-[#E8EAED] mb-2">
        <button
          className={`pb-2 text-[14px] font-semibold transition-colors px-4 ${selectedTab === 'paye' ? 'text-orange-500 bg-[#FFFAF5] rounded-[4px] border-b-[3px] border-orange-500 rounded-b-[4px]' : 'text-gray-400'}`}
          onClick={() => onTabChange('paye')}
        >
          Payé
        </button>
        <button
          className={`pb-2 text-[14px] font-semibold transition-colors px-4 ${selectedTab === 'prevu' ? 'text-orange-500 bg-[#FFFAF5] rounded-[4px] border-b-[3px] border-orange-500 rounded-b-[4px]' : 'text-gray-400'}`}
          onClick={() => onTabChange('prevu')}
        >
          Paiement prévu
        </button>
      </div>
    </div>
  );
};

export default TransactionsTabs; 