import PageAddListingIntro from "./intro";
import PageAddListingPresenteAnnonce from "./presente-annonce";
import PageAddCategories from "./add-categories";
import PageAddType from "./add-type";
import AddLocation from "./add-location";
import AddGuestsAndRules from "./add-guests-and-rules";
import PageAddListingMidAnnonce from "./mid-annonce";
import AddEquipments from "./add-equipments";
import AddImages from "./add-images";
import AddTitleAndDescription from "./add-title-and-description";
import AddConditions from "./add-conditions";
import PageAddListingFinaliserAnnonce from "./finaliser-annonce";
import AddPrice from "./add-price";
import AddDiscount from "./add-discount";
import AddDates from "./add-dates";
import AddPayments from "./add-payments";
import Congratulations from "./Congratulations";

export const ADD_LISTING_STEPS = [
    { key: "intro", component: PageAddListingIntro },
    { key: "presente-annonce", component: PageAddListingPresenteAnnonce },
    { key: "add-categories", component: PageAddCategories },
    { key: "add-type", component: PageAddType },
    { key: "add-location", component: AddLocation },
    { key: "add-guests-and-rules", component: AddGuestsAndRules },
    { key: "mid-annonce", component: PageAddListingMidAnnonce },
    { key: "add-equipments", component: AddEquipments },
    { key: "add-images", component: AddImages },
    { key: "add-title-and-description", component: AddTitleAndDescription },
    { key: "add-conditions", component: AddConditions },
    { key: "finaliser-annonce", component: PageAddListingFinaliserAnnonce },
    { key: "add-price", component: AddPrice },
    { key: "add-discount", component: AddDiscount },
    { key: "add-dates", component: AddDates },
    { key: "add-payments", component: AddPayments },
    { key: "congratulations", component: Congratulations },
]; 