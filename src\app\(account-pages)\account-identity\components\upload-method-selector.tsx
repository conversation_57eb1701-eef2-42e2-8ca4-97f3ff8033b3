"use client"

import { Upload, Camera } from "lucide-react"

type UploadMethod = "photo" | "webcam"

interface UploadMethodSelectorProps {
    selectedMethod: UploadMethod | null
    onSelectMethod: (method: UploadMethod) => void
    onContinue: () => void
    onBack: () => void
}

export function UploadMethodSelector({
                                         selectedMethod,
                                         onSelectMethod,
                                         onContinue,
                                         onBack,
                                     }: UploadMethodSelectorProps) {
    return (
        <div className="w-full max-w-md mx-auto">
            <h2 className="text-2xl font-bold mb-6">Comment souhaitez-vous ajouter votre pièce d&apos;identité officielle ?</h2>

            <div className="space-y-3 mb-8">
                <div
                    className={`flex items-center p-4 border rounded-md cursor-pointer transition-colors ${
                        selectedMethod === "photo" ? "border-orange-300 bg-orange-50" : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => onSelectMethod("photo")}
                >
                    <div className="flex-1">
                        <div className="font-medium">Télécharger une photo existante</div>
                        <div className="text-sm text-gray-500">Recommandée</div>
                    </div>
                    <Upload className="w-5 h-5 text-gray-400" />
                </div>

                <div
                    className={`flex items-center p-4 border rounded-md cursor-pointer transition-colors ${
                        selectedMethod === "webcam" ? "border-orange-300 bg-orange-50" : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => onSelectMethod("webcam")}
                >
                    <div className="flex-1">
                        <div className="font-medium">Prendre une photo avec une webcam</div>
                    </div>
                    <Camera className="w-5 h-5 text-gray-400" />
                </div>
            </div>

            <div className="flex justify-between">
                <button onClick={onBack} className="px-4 py-2 text-gray-600 hover:text-gray-900 font-medium">
                    Retour
                </button>

                <button
                    onClick={onContinue}
                    disabled={!selectedMethod}
                    className={`px-6 py-2 rounded-md bg-black text-white font-medium ${
                        !selectedMethod ? "opacity-50 cursor-not-allowed" : "hover:bg-gray-800"
                    }`}
                >
                    Continuer
                </button>
            </div>
        </div>
    )
}
