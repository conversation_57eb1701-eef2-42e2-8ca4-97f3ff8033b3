"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { createClient } from "@/utils/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { IdTypeSelector } from "../components/id-type-selector"
import { UploadMethodSelector } from "../components/upload-method-selector"
import { IdPhotoUploader } from "../components/id-photo-uploader"
import { SelfieUploader } from "../components/selfie-uploader"
import { VerificationConfirmation } from "../components/verification-confirmation"
import { ArrowLeft } from "lucide-react"

type VerificationState = "pending" | "verified" | "rejected" | "not_submitted"
type IdType = "cin" | "passport" | "permit"
type UploadMethod = "photo" | "webcam"
type Step = "id-type" | "upload-method" | "id-photos" | "selfie" | "confirmation" | "complete"

export default function VerificationFormPage() {
    const router = useRouter()
    const { toast } = useToast()
    const supabase = createClient()

    // State
    const [loading, setLoading] = useState(true)
    const [submitting, setSubmitting] = useState(false)
    const [currentStep, setCurrentStep] = useState<Step>("id-type")
    const [idType, setIdType] = useState<IdType | null>(null)
    const [uploadMethod, setUploadMethod] = useState<UploadMethod | null>(null)
    const [frontFile, setFrontFile] = useState<File | null>(null)
    const [backFile, setBackFile] = useState<File | null>(null)
    const [selfieFile, setSelfieFile] = useState<File | null>(null)
    const [idNumber, setIdNumber] = useState("")
    const [userId, setUserId] = useState<string | null>(null)

    useEffect(() => {
        // Check if user is already verified and get user ID
        const checkVerificationStatus = async () => {
            try {
                // Get current user
                const {
                    data: { user },
                    error: userError,
                } = await supabase.auth.getUser()

                if (userError || !user) {
                    toast({
                        title: "Erreur d'authentification",
                        description: "Vous devez être connecté pour accéder à cette page",
                        variant: "destructive",
                    })
                    router.push("/login")
                    return
                }

                // Store user ID for later use
                setUserId(user.id)

                const response = await fetch("/api/identity-verification/status")

                if (!response.ok) {
                    setLoading(false)
                    return
                }

                const data = await response.json()

                // If user is already verified or has a pending verification, redirect back
                if (data.status === "verified" || data.status === "pending") {
                    router.push("/account-identity")
                }

                setLoading(false)
            } catch (error) {
                console.error("Error checking verification status:", error)
                setLoading(false)
            }
        }

        checkVerificationStatus()
    }, [router, toast, supabase.auth])

    // Handle ID type selection
    const handleSelectIdType = (type: IdType) => {
        setIdType(type)
    }

    // Handle upload method selection
    const handleSelectUploadMethod = (method: UploadMethod) => {
        setUploadMethod(method)
    }

    // Handle ID photos upload completion
    const handleIdPhotosComplete = (frontFile: File, backFile: File | null, idNumberValue: string) => {
        setFrontFile(frontFile)
        setBackFile(backFile)
        setIdNumber(idNumberValue)
        setCurrentStep("selfie")
    }

    // Handle selfie upload completion
    const handleSelfieComplete = (file: File) => {
        setSelfieFile(file)
        setCurrentStep("confirmation")
    }

    // Upload ID photos using the identity-verification endpoint
    const uploadIdPhotos = async () => {
        if (!userId || !frontFile || !idType) {
            throw new Error("Missing required fields for ID upload")
        }

        // For passport, we don't need a back image, but the API expects one
        // So we'll use the front image as the back image in this case
        const backImageToUpload = backFile || frontFile

        // Create form data for the upload
        const formData = new FormData()
        formData.append("idType", idType)
        formData.append("idNumber", idNumber || "N/A") // Ensure idNumber is never empty
        formData.append("userId", userId)
        formData.append("frontImage", frontFile)
        formData.append("backImage", backImageToUpload)

        try {
            console.log("Uploading ID photos with data:", {
                idType,
                idNumber: idNumber || "N/A",
                userId,
                frontImage: frontFile.name,
                backImage: backImageToUpload.name,
            })

            // Upload the images
            const response = await fetch("/api/identity-verification", {
                method: "POST",
                body: formData,
            })

            const responseData = await response.json()

            if (!response.ok) {
                console.error("ID upload error response:", responseData)
                throw new Error(responseData.error || "Error uploading ID photos")
            }

            return responseData.verificationId
        } catch (error) {
            console.error("Error in uploadIdPhotos:", error)
            throw error
        }
    }

    // Upload selfie and update verification record
    const uploadSelfieAndUpdateVerification = async (verificationId: string) => {
        if (!userId || !selfieFile) {
            throw new Error("Missing required fields for selfie upload")
        }

        try {
            // First, upload the selfie file to Supabase storage
            const timestamp = Date.now()
            const fileExtension = selfieFile.name.split(".").pop() || "jpg"
            const selfieFileName = `${timestamp}-selfie.${fileExtension}`
            const selfiePath = `${userId}/identity_verification/${selfieFileName}`

            console.log("Uploading selfie:", {
                fileName: selfieFileName,
                fileType: selfieFile.type,
                fileSize: selfieFile.size,
                path: selfiePath,
            })

            // Convert selfie file to array buffer for upload
            const selfieArrayBuffer = await selfieFile.arrayBuffer()

            // Upload selfie using Supabase client with explicit content type
            const { data, error: selfieUploadError } = await supabase.storage
                .from("identity-documents")
                .upload(selfiePath, selfieFile, {
                    contentType: selfieFile.type,
                    upsert: true,
                })

            if (selfieUploadError) {
                console.error("Selfie upload error:", selfieUploadError)
                throw new Error("Error uploading selfie: " + selfieUploadError.message)
            }

            console.log("Selfie uploaded successfully:", data)

            // Update the verification record with the selfie path
            const response = await fetch("/api/identity-verification/submit", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    isUpdate: true,
                    userId,
                    verificationData: {
                        additional_image_path: selfiePath,
                    },
                }),
            })

            const responseData = await response.json()

            if (!response.ok) {
                console.error("Verification update error:", responseData)
                throw new Error(responseData.error || "Error updating verification with selfie")
            }

            return responseData
        } catch (error) {
            console.error("Error in uploadSelfieAndUpdateVerification:", error)
            throw error
        }
    }

    // Handle form submission
    const handleSubmitVerification = async () => {
        if (!idType || !frontFile || !selfieFile || !userId) {
            toast({
                title: "Erreur",
                description: "Veuillez compléter toutes les étapes de vérification",
                variant: "destructive",
            })
            return
        }

        // For CIN and Permit, back image is required
        if ((idType === "cin" || idType === "permit") && !backFile) {
            toast({
                title: "Erreur",
                description: "Veuillez télécharger le verso de votre pièce d'identité",
                variant: "destructive",
            })
            return
        }

        try {
            setSubmitting(true)

            // First, upload the ID photos
            const verificationId = await uploadIdPhotos()

            if (!verificationId) {
                throw new Error("Failed to create verification record")
            }

            // Then, upload the selfie and update the verification record
            await uploadSelfieAndUpdateVerification(verificationId)

            // Show success message
            toast({
                title: "Demande soumise",
                description: "Votre demande de vérification a été soumise avec succès",
            })

            // Redirect to identity page
            router.push("/account-identity")
        } catch (error: any) {
            console.error("Error submitting verification:", error)
            toast({
                title: "Erreur",
                description: error.message || "Une erreur s'est produite lors de la soumission de votre demande",
                variant: "destructive",
            })
        } finally {
            setSubmitting(false)
        }
    }

    // Handle back button click
    const handleBack = () => {
        switch (currentStep) {
            case "upload-method":
                setCurrentStep("id-type")
                break
            case "id-photos":
                setCurrentStep("upload-method")
                break
            case "selfie":
                setCurrentStep("id-photos")
                break
            case "confirmation":
                setCurrentStep("selfie")
                break
            default:
                router.push("/account-identity")
        }
    }

    if (loading || submitting) {
        return (
            <div className="p-8">
                <div className="flex items-center justify-center py-10">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-4"></div>
                        <p className="text-gray-500">{submitting ? "Soumission de votre demande..." : "Chargement..."}</p>
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className="p-8">
            <div className="max-w-3xl mx-auto">
                <button
                    className="mb-6 flex items-center text-gray-600 hover:text-black"
                    onClick={() => router.push("/account-identity")}
                >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Retour à la page principale
                </button>

                {/* Step indicator - only show for steps before confirmation */}
                {currentStep !== "confirmation" && (
                    <div className="mb-8">
                        <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">
                Étape{" "}
                  {currentStep === "id-type"
                      ? "1"
                      : currentStep === "upload-method"
                          ? "2"
                          : currentStep === "id-photos"
                              ? "3"
                              : currentStep === "selfie"
                                  ? "4"
                                  : "5"}{" "}
                  sur 4
              </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                                className="bg-black h-2 rounded-full transition-all duration-300 ease-in-out"
                                style={{
                                    width:
                                        currentStep === "id-type"
                                            ? "25%"
                                            : currentStep === "upload-method"
                                                ? "50%"
                                                : currentStep === "id-photos"
                                                    ? "75%"
                                                    : "100%",
                                }}
                            ></div>
                        </div>
                    </div>
                )}

                {/* Current step content */}
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                    {currentStep === "id-type" && (
                        <IdTypeSelector
                            selectedType={idType}
                            onSelectType={handleSelectIdType}
                            onContinue={() => setCurrentStep("upload-method")}
                            onBack={() => router.push("/account-identity")}
                        />
                    )}

                    {currentStep === "upload-method" && (
                        <UploadMethodSelector
                            selectedMethod={uploadMethod}
                            onSelectMethod={handleSelectUploadMethod}
                            onContinue={() => setCurrentStep("id-photos")}
                            onBack={handleBack}
                        />
                    )}

                    {currentStep === "id-photos" && idType && uploadMethod && (
                        <IdPhotoUploader
                            idType={idType}
                            uploadMethod={uploadMethod}
                            onComplete={handleIdPhotosComplete}
                            onBack={handleBack}
                            onIdNumberChange={setIdNumber}
                            idNumber={idNumber}
                        />
                    )}

                    {currentStep === "selfie" && uploadMethod && (
                        <SelfieUploader uploadMethod={uploadMethod} onComplete={handleSelfieComplete} onBack={handleBack} />
                    )}

                    {currentStep === "confirmation" && <VerificationConfirmation onComplete={handleSubmitVerification} />}
                </div>
            </div>
        </div>
    )
}
