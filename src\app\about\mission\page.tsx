import React from "react";

const BRAND_COLOR = "#EA5911";

export default function Mission() {
  return (
    <div className="relative min-h-screen flex items-center justify-center py-10 px-2 bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
      {/* Abstract background shape */}
      <div
        aria-hidden="true"
        className="absolute -top-32 -left-32 w-[500px] h-[500px] rounded-full opacity-10 blur-2xl"
        style={{ background: `radial-gradient(circle, ${BRAND_COLOR} 0%, transparent 70%)` }}
      />
      <div className="w-full max-w-4xl bg-white rounded-3xl shadow-2xl p-10 sm:p-16 relative z-10">
        <h1 className="text-5xl font-extrabold text-center mb-8 text-gray-900 tracking-tight">
          Mission, Vision &amp; Valeurs
          <span
            className="block mx-auto mt-4 rounded-full"
            style={{ width: 120, height: 8, background: BRAND_COLOR }}
          ></span>
        </h1>

        {/* Mission Section */}
        <section className="mb-10">
          <h2 className="text-2xl font-bold text-gray-800 mb-3 pl-4 border-l-8" style={{ borderColor: BRAND_COLOR, background: 'rgba(234,89,17,0.04)' }}>
            Notre mission
          </h2>
          <p className="text-lg text-gray-700 leading-loose text-justify">
            Faciliter la mise en relation entre hôtes et voyageurs en Tunisie à travers une plateforme simple, sécurisée et humaine, qui valorise les logements authentiques et l&rsquo;hospitalité locale.<br />
            Chez Almindhar Booking, nous ne vendons pas que des nuits — nous offrons des expériences de séjour sereines et mémorables.
          </p>
        </section>

        <div className="w-full flex justify-center mb-10">
          <div style={{ background: BRAND_COLOR }} className="h-1 w-32 rounded-full opacity-30" />
        </div>

        {/* Vision Section */}
        <section className="mb-10">
          <h2 className="text-2xl font-bold text-gray-800 mb-3 pl-4 border-l-8" style={{ borderColor: BRAND_COLOR, background: 'rgba(234,89,17,0.04)' }}>
            Notre vision
          </h2>
          <p className="text-lg text-gray-700 leading-loose text-justify mb-4">
            Devenir la référence tunisienne de la location à court terme en combinant technologie, confiance et proximité, et dans un futur proche, élargir notre offre pour inclure des expériences à vivre dans chaque région du pays.
          </p>
          <ul className="list-disc list-inside space-y-3 text-lg text-gray-700 pl-2 leading-loose">
            <li>Valoriser les régions tunisiennes sous-représentées.</li>
            <li>Créer une communauté de confiance entre hôtes et voyageurs.</li>
            <li>Soutenir les micro-entrepreneurs locaux via la location ou l&rsquo;offre d&rsquo;expériences.</li>
          </ul>
        </section>

        <div className="w-full flex justify-center mb-10">
          <div style={{ background: BRAND_COLOR }} className="h-1 w-32 rounded-full opacity-30" />
        </div>

        {/* Valeurs Section */}
        <section>
          <h2 className="text-2xl font-bold text-gray-800 mb-3 pl-4 border-l-8" style={{ borderColor: BRAND_COLOR, background: 'rgba(234,89,17,0.04)' }}>
            Nos valeurs
          </h2>
          <ul className="list-disc list-inside space-y-3 text-lg text-gray-700 pl-2 leading-loose">
            <li><span style={{ color: BRAND_COLOR, fontWeight: 700 }}>Fiabilité</span> : Chaque réservation compte. Nous garantissons la transparence et la sécurité pour tous.</li>
            <li><span style={{ color: BRAND_COLOR, fontWeight: 700 }}>Proximité</span> : Une plateforme locale, pensée par des Tunisiens, pour la Tunisie.</li>
            <li><span style={{ color: BRAND_COLOR, fontWeight: 700 }}>Simplicité</span> : Une interface intuitive pour tous les profils.</li>
            <li><span style={{ color: BRAND_COLOR, fontWeight: 700 }}>Authenticité</span> : Nous privilégions les logements et expériences qui reflètent la richesse culturelle et humaine de nos régions.</li>
            <li><span style={{ color: BRAND_COLOR, fontWeight: 700 }}>Croissance partagée</span> : Quand un hôte réussit, un voyageur est satisfait. Et c&rsquo;est toute la plateforme qui évolue.</li>
          </ul>
        </section>
      </div>
    </div>
  );
} 