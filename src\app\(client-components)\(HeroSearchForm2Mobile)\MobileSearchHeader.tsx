"use client"

import type React from "react"

import { Fragment, useState, useEffect, useRef } from "react"
import {
    <PERSON>alog,
    DialogPanel,
    Tab,
    TabGroup,
    TabList,
    TabPanel,
    TabPanels,
    Transition,
    TransitionChild,
} from "@headlessui/react"
import { ChevronLeftIcon } from "@heroicons/react/24/outline"
import { XMarkIcon } from "@heroicons/react/24/solid"
import ButtonSubmit from "./ButtonSubmit"
import { useTimeoutFn } from "react-use"
import StaySearchForm from "./(stay-search-form)/StaySearchForm"
import { type SearchParams, useSearch } from "@/app/(stay-listings)/SearchContext"

// Helper function to format a date using French locale.
const formatDate = (date: Date) => {
    date = new Date(date)
    return date.toLocaleDateString("fr-FR", { day: "numeric", month: "short" })
}

interface MobileSearchHeaderProps {
    isScrollingDown?: boolean
    headerTransformed?: boolean
    setHeaderTransformed?: (value: boolean) => void
}

const MobileSearchHeader = ({
                                isScrollingDown = false,
                                headerTransformed = false,
                                setHeaderTransformed = () => {},
                            }: MobileSearchHeaderProps) => {
    // Tracks whether we've transformed to the "screenshot" design yet
    const [isTransformed, setIsTransformed] = useState(false)
    
    // Scroll detection 
    const isScrollingRef = useRef(false)
    const scrollTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null)
    // Track if a scroll handler is active
    const scrollHandlerActiveRef = useRef(false)
    // Track the last scroll position to detect real scroll movement
    const lastScrollPositionRef = useRef(0)
    // Track if the transform is recent to prevent immediate scroll detection
    const recentTransformRef = useRef(false)

    // Existing modal logic
    const [showModal, setShowModal] = useState(false)
    const { searchParams, setSearchParams, setMobileTabFiltersVisible } = useSearch()
    const [tempSearchParams, setTempSearchParams] = useState(searchParams)
    const [showDialog, setShowDialog] = useState(false)
    const [, , resetIsShowingDialog] = useTimeoutFn(() => setShowDialog(true), 1)

    // Update parent component's state when local state changes
    useEffect(() => {
        if (setHeaderTransformed) {
            setHeaderTransformed(isTransformed)
        }
        
        if (isTransformed) {
            // Set a flag to indicate a recent transform
            recentTransformRef.current = true;
            // Store the current scroll position
            lastScrollPositionRef.current = window.scrollY;
            
            // Clear the flag after a short delay to allow the component to stabilize
            setTimeout(() => {
                recentTransformRef.current = false;
            }, 300);
        }
        
        // Attach/detach scroll handlers based on transformed state
        const handleScrollEvent = () => {
            // Ignore scroll events immediately after transformation
            if (recentTransformRef.current) {
                return;
            }
            
            // Get current scroll position
            const currentScrollY = window.scrollY;
            // Calculate scroll difference
            const scrollDiff = Math.abs(currentScrollY - lastScrollPositionRef.current);
            
            // Only consider it a scroll if moved more than 5px
            if (scrollDiff > 5) {
                
                // If the user is scrolling, revert to initial state
                setIsTransformed(false);
                
                // Mark as scrolling
                isScrollingRef.current = true;
                
                // Update last scroll position
                lastScrollPositionRef.current = currentScrollY;
                
                // Reset the scrolling flag after a short delay
                if (scrollTimeoutRef.current) {
                    clearTimeout(scrollTimeoutRef.current);
                }
                
                scrollTimeoutRef.current = setTimeout(() => {
                    isScrollingRef.current = false;
                }, 150); // Short timeout to detect when scrolling stops
            } else {
                // Update position even for small movements
                lastScrollPositionRef.current = currentScrollY;
            }
        };
        
        // Only add scroll listener when isTransformed is true and on client side
        if (typeof window !== "undefined" && isTransformed && !scrollHandlerActiveRef.current) {
            window.addEventListener("scroll", handleScrollEvent, { passive: true });
            scrollHandlerActiveRef.current = true;
        } 
        // Remove listener when isTransformed is false
        else if (typeof window !== "undefined" && !isTransformed && scrollHandlerActiveRef.current) {
            window.removeEventListener("scroll", handleScrollEvent);
            scrollHandlerActiveRef.current = false;
        }
        
        // Cleanup function to remove event listener
        return () => {
            if (typeof window !== "undefined" && scrollHandlerActiveRef.current) {
                window.removeEventListener("scroll", handleScrollEvent);
                scrollHandlerActiveRef.current = false;
            }
        }
    }, [isTransformed, setHeaderTransformed])

    // Clean up timeout on unmount
    useEffect(() => {
        return () => {
            if (scrollTimeoutRef.current) {
                clearTimeout(scrollTimeoutRef.current)
            }
            
            // Additional cleanup to ensure no lingering event listeners
            if (typeof window !== "undefined" && scrollHandlerActiveRef.current) {
                // We need to re-define the handler here since the original might be stale
                const cleanupHandler = () => {
                    setIsTransformed(false)
                }
                window.removeEventListener("scroll", cleanupHandler)
                scrollHandlerActiveRef.current = false
            }
        }
    }, [])

    // Close dialog + reset transformation
    function closeModal() {
        setShowModal(false)
    }

    // Open the existing dialog
    function openModal() {
        setTempSearchParams(searchParams)
        setShowModal(true)
    }

    // Clear search params
    const handleClearAll = () => {
        const clearedParams: SearchParams = {
            location: "",
            checkIn: null,
            checkOut: null,
            guests: { guestAdults: 0, guestChildren: 0, guestInfants: 0 },
            propertyTypes: [],
            propertyCategories: [],
            amenities: [],
            priceRange: [0, 1000],
            beds: 0,
            bedrooms: 0,
            bathrooms: 0,
            kitchens: 0,
            mapBounds: null,
            paymentType: null,
            roomTypes: [],
            minRating: 0
        }
        setSearchParams(clearedParams)
        setTempSearchParams(clearedParams)
        setShowDialog(false)
        resetIsShowingDialog()
    }

    // Handle updated search params
    const handleSearchChange = (newParams: any) => {
        setTempSearchParams(newParams)
    }

    // Handle button click: always transform regardless of scroll position
    const handleButtonClick = (e: React.MouseEvent) => {
        if (!isTransformed) {
            // First click => transform the design
            e.stopPropagation()
            setIsTransformed(true)
            setMobileTabFiltersVisible(true)
        } else {
            // Second click => open the modal
            openModal()
        }
    }

    // Compute dynamic fields
    const dateRange =
        searchParams.checkIn && searchParams.checkOut
            ? `${formatDate(searchParams.checkIn)} – ${formatDate(searchParams.checkOut)}`
            : "Arrivé"

    const totalGuests =
        (searchParams.guests.guestAdults || 0) +
        (searchParams.guests.guestChildren || 0) +
        (searchParams.guests.guestInfants || 0)

    const guestsText = totalGuests > 0 ? `${totalGuests} ${totalGuests === 1 ? "invité" : "invités"}` : "Invités"

    // 1) The original design (as you have it now)
    const renderInitialDesign = () => {
        return (
            <div className="relative flex items-center space-x-4 rounded-lg border border-neutral-200 px-4 py-2 shadow-lg dark:border-neutral-6000 max-w-lg mx-4 sm:mx-auto transition-all duration-300 ease-in-out">
                    <div className="w-2/5 flex justify-between items-center">
                        {/* Left arrow icon with click redirect */}
                        <ChevronLeftIcon
                            className="h-4 w-4 cursor-pointer"
                            onClick={(e) => {
                                e.stopPropagation() // Prevents triggering openModal
                                window.location.href = "/" // Redirects to the home page
                            }}
                        />

                        {/* Location (value from searchParams if exists, else placeholder) */}
                        <span className="text-sm font-medium line-clamp-1">{searchParams.location || "Localisation"}</span>
                    </div>

                    <div className="w-2/5 flex justify-center border-x-2 border-neutral-200">
                        {/* Date range (formatted if both dates exist, else placeholder) */}
                        <span className="text-sm font-medium line-clamp-1">{dateRange}</span>
                    </div>

                    <div className="w-1/5 flex justify-center">
                        {/* Travelers (computed total or placeholder) */}
                        <span className="text-sm font-medium line-clamp-1">{guestsText}</span>
                    </div>
                </div>
        )
    }

    // 2) The screenshot design updated to display values from search context (or placeholders if missing)
    const renderSecondDesign = () => {
        return (
            <div className="z-50 bg-white relative flex flex-col items-center justify-between rounded-lg border border-neutral-200 shadow-lg dark:border-neutral-6000 font-medium mx-4 transition-all duration-300 ease-in-out">
                {/* Top section: location */}
                <div className="flex-1 flex flex-col items-start p-4 w-full border-b">
          <span className="text-sm flex items-center space-x-2">
            {/* Location icon */}
              <svg width="13" height="16" viewBox="0 0 13 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                  d="M8.625 6.4375C8.625 7.61111 7.67361 8.5625 6.5 8.5625C5.32639 8.5625 4.375 7.61111 4.375 6.4375C4.375 5.26389 5.32639 4.3125 6.5 4.3125C7.67361 4.3125 8.625 5.26389 8.625 6.4375Z"
                  stroke="#6C757E"
                  strokeWidth="1.3"
                  strokeLinecap="round"
                  strokeLinejoin="round"
              />
              <path
                  d="M11.8125 6.4375C11.8125 11.4965 6.5 14.4062 6.5 14.4062C6.5 14.4062 1.1875 11.4965 1.1875 6.4375C1.1875 3.50349 3.56599 1.125 6.5 1.125C9.43401 1.125 11.8125 3.50349 11.8125 6.4375Z"
                  stroke="#6C757E"
                  strokeWidth="1.3"
                  strokeLinecap="round"
                  strokeLinejoin="round"
              />
            </svg>
              {/* Display location value or placeholder */}
              <span>{searchParams.location || "Localisation"}</span>
          </span>
                </div>

                {/* Middle and bottom section: date range and travelers */}
                <div className="flex w-full">
                    {/* Middle section: date range */}
                    <div className="flex p-4 items-center w-1/2 border-r">
            <span className="text-sm flex items-center space-x-2">
              {/* Calendar icon */}
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M4.0625 1.25V2.9375M11.9375 1.25V2.9375M1.25 13.0625V4.625C1.25 3.69302 2.00552 2.9375 2.9375 2.9375H13.0625C13.9945 2.9375 14.75 3.69302 14.75 4.625V13.0625M1.25 13.0625C1.25 13.9945 2.00552 14.75 2.9375 14.75H13.0625C13.9945 14.75 14.75 13.9945 14.75 13.0625M1.25 13.0625V7.4375C1.25 6.50552 2.00552 5.75 2.9375 5.75H13.0625C13.9945 5.75 14.75 6.50552 14.75 7.4375V13.0625M8 8.5625H8.00563V8.56813H8V8.5625ZM8 10.25H8.00563V10.2556H8V10.25ZM8 11.9375H8.00563V11.9431H8V11.9375ZM6.3125 10.25H6.31813V10.2556H6.3125V10.25ZM6.3125 11.9375H6.31813V11.9431H6.3125V11.9375ZM4.625 10.25H4.63063V10.2556H4.625V10.25ZM4.625 11.9375H4.63063V11.9431H4.625V11.9375ZM9.6875 8.5625H9.69313V8.56813H9.6875V8.5625ZM9.6875 10.25H9.69313V10.2556H9.6875V10.25ZM9.6875 11.9375H9.69313V11.9431H9.6875V11.9375ZM11.375 8.5625H11.3806V8.56813H11.375V8.5625ZM11.375 10.25H11.3806V10.2556H11.375V10.25Z"
                    stroke="#6C757E"
                    strokeWidth="1.3"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
              </svg>
                {/* Display date range or placeholder */}
                <span>{dateRange}</span>
            </span>
                    </div>

                    {/* Right section: travelers */}
                    <div className="flex p-4 items-end w-1/2 justify-start">
            <span className="text-sm flex items-center space-x-2">
              {/* Travelers icon */}
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M10.25 13.3457C10.8747 13.5275 11.5353 13.625 12.2187 13.625C13.3278 13.625 14.3769 13.3682 15.3098 12.9109C15.3116 12.8679 15.3125 12.8247 15.3125 12.7812C15.3125 11.0726 13.9273 9.6875 12.2187 9.6875C11.1552 9.6875 10.217 10.2241 9.66018 11.0414M10.25 13.3457V13.3438C10.25 12.5091 10.0361 11.7244 9.66018 11.0414M10.25 13.3457C10.25 13.3724 10.2498 13.3991 10.2493 13.4257C8.85302 14.2664 7.21735 14.75 5.46875 14.75C3.72014 14.75 2.08448 14.2664 0.68819 13.4257C0.687732 13.3985 0.6875 13.3711 0.6875 13.3438C0.6875 10.7031 2.82814 8.5625 5.46875 8.5625C7.27469 8.5625 8.84677 9.56374 9.66018 11.0414M8 3.78125C8 5.17922 6.86672 6.3125 5.46875 6.3125C4.07078 6.3125 2.9375 5.17922 2.9375 3.78125C2.9375 2.38328 4.07078 1.25 5.46875 1.25C6.86672 1.25 8 2.38328 8 3.78125ZM14.1875 5.46875C14.1875 6.55606 13.3061 7.4375 12.2187 7.4375C11.1314 7.4375 10.25 6.55606 10.25 5.46875C10.25 4.38144 11.1314 3.5 12.2187 3.5C13.3061 3.5 14.1875 4.38144 14.1875 5.46875Z"
                    stroke="#6C757E"
                    strokeWidth="1.3"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                />
              </svg>
                {/* Display travelers count or placeholder */}
                <span>{guestsText}</span>
            </span>
                    </div>
                </div>
            </div>
        )
    }

    // Conditionally render the correct layout in one button
    const renderMainButton = () => {
        return (
            <button onClick={handleButtonClick} className={`w-full ${isTransformed ? 'pt-2 pb-4' : 'my-4'}`}>
                {isTransformed ? renderSecondDesign() : renderInitialDesign()}
            </button>
        )
    }

    
    return (
        <div className={`HeroSearchForm2Mobile md:hidden sticky top-0 bg-white z-50`}>
            {/* Main button: first click transforms, second click opens modal */}
            {renderMainButton()}

            {/* The existing dialog transitions remain unchanged */}
            <Transition appear show={showModal} as={Fragment}>
                <Dialog as="div" className="HeroSearchFormMobile__Dialog z-max relative" onClose={closeModal}>
                    <div className="fixed inset-0 bg-neutral-100 dark:bg-neutral-900">
                        <div className="flex h-full">
                            <TransitionChild
                                as={Fragment}
                                enter="ease-out transition-transform"
                                enterFrom="opacity-0 translate-y-52"
                                enterTo="opacity-100 translate-y-0"
                                leave="ease-in transition-transform"
                                leaveFrom="opacity-100 translate-y-0"
                                leaveTo="opacity-0 translate-y-52"
                            >
                                <DialogPanel className="w-full">
                                    {showDialog && (
                                        <TabGroup manual className="relative flex h-full flex-1 flex-col justify-between overflow-hidden">
                                            <div className="absolute left-4 top-4">
                                                <button onClick={closeModal}>
                                                    <XMarkIcon className="h-5 w-5 text-black dark:text-white" />
                                                </button>
                                            </div>
                                            <TabList className="flex w-full justify-center space-x-6 pt-12 text-sm font-semibold text-neutral-500 dark:text-neutral-400 sm:space-x-8 sm:text-base">
                                                {["Séjour"].map((item, index) => (
                                                    <Tab key={index} as={Fragment}>
                                                        {({ selected }) => (
                                                            <div className="relative select-none outline-none focus:outline-none focus-visible:ring-0">
                                                                <div className={`${selected ? "text-black dark:text-white" : ""}`}>{item}</div>
                                                                {selected && (
                                                                    <span className="absolute inset-x-0 top-full border-b-2 border-black dark:border-white"></span>
                                                                )}
                                                            </div>
                                                        )}
                                                    </Tab>
                                                ))}
                                            </TabList>
                                            <div className="flex flex-1 overflow-hidden px-1.5 pt-3 sm:px-4">
                                                <TabPanels className="hiddenScrollbar flex-1 overflow-y-auto py-4">
                                                    <TabPanel>
                                                        <div className="animate-[myblur_0.4s_ease-in-out] transition-opacity">
                                                            <StaySearchForm onSearchChange={handleSearchChange} />
                                                        </div>
                                                    </TabPanel>
                                                </TabPanels>
                                            </div>
                                            <div className="flex justify-between border-t border-neutral-200 bg-white px-4 py-3 dark:border-neutral-700 dark:bg-neutral-900">
                                                <button
                                                    type="button"
                                                    className="flex-shrink-0 font-semibold underline"
                                                    onClick={handleClearAll}
                                                >
                                                    Effacer tout
                                                </button>
                                                <ButtonSubmit
                                                    className="bg-booking-orange"
                                                    onSubmit={() => {
                                                        closeModal()
                                                    }}
                                                    searchFilters={tempSearchParams}
                                                />
                                            </div>
                                        </TabGroup>
                                    )}
                                </DialogPanel>
                            </TransitionChild>
                        </div>
                    </div>
                </Dialog>
            </Transition>
        </div>
    )
}

export default MobileSearchHeader
