"use client"

import { MapPinIcon } from "@heroicons/react/24/solid"
import React, { useEffect, useState, useCallback, useRef } from "react"
import { Map, AdvancedMarker, useMap } from "@vis.gl/react-google-maps"
import { useFormContext } from "@/app/add-listing/FormContext"
import CustomSelect from "@/components/CustomSelect";
import { stepSchemas } from "../formSchemas"
import CommonLayout from "./CommonLayout";
import { motion, AnimatePresence } from 'framer-motion';

export interface State {
	id: string
	name: string
}

export interface City {
	id: string
	name: string
	state_id: string
}

const debounce = (func: any, wait: number) => {
	let timeoutId: NodeJS.Timeout | null
	return (...args: any[]) => {
		if (timeoutId) {
			clearTimeout(timeoutId)
		}
		timeoutId = setTimeout(() => {
			func.apply(null, args)
		}, wait)
	}
}

// Helper type guard for Google Maps LatLng vs LatLngLiteral
function isLatLngObject(obj: any): obj is { lat: () => number; lng: () => number } {
	return (
		obj &&
		typeof obj.lat === 'function' &&
		typeof obj.lng === 'function'
	);
}

// Helper: extract region and city from address_components
function extractRegionAndCity(address_components: any[]): { region: string; city: string } {
	let region = '';
	let city = '';
	for (const comp of address_components) {
		if (comp.types.includes('administrative_area_level_1')) {
			region = comp.long_name;
		}
		if (comp.types.includes('locality')) {
			city = comp.long_name;
		}
		// Fallbacks
		if (!city && comp.types.includes('administrative_area_level_2')) {
			city = comp.long_name;
		}
		if (!city && comp.types.includes('sublocality')) {
			city = comp.long_name;
		}
		if (!city && comp.types.includes('administrative_area_level_3')) {
			city = comp.long_name;
		}
	}
	return { region, city };
}

// Helper: reverse geocode lat/lng to address, region, city
function reverseGeocodeLatLng(lat: number, lng: number, callback: (result: { address: string; region: string; city: string }) => void) {
	if (window.google && window.google.maps) {
		const geocoder = new window.google.maps.Geocoder();
		geocoder.geocode({ location: { lat, lng } }, (results, status) => {
			if (status === 'OK' && results && results[0]) {
				const address = results[0].formatted_address;
				const { region, city } = extractRegionAndCity(results[0].address_components);
				callback({ address, region, city });
			} else {
				callback({ address: '', region: '', city: '' });
			}
		});
	}
}

// Helper: clean Plus Code from address
function cleanPlusCode(address: string): string {
	// Robust Plus Code pattern: 4-7 alphanum, '+', 2-3 alphanum, optional space/comma
	return address.replace(/^[A-Z0-9]{4,7}\+[A-Z0-9]{2,3}[ ,]*/i, '');
}

// --- Helper: robust normalization for fuzzy matching ---
function normalizeString(str: string): string {
	return str
		.toLowerCase()
		.replace(/governorate|wilaya|province/g, '')
		.replace(/[''`]/g, '')
		.normalize('NFD').replace(/[\u0300-\u036f]/g, '')
		.replace(/\s+/g, '')
		.trim();
}

// --- Helper: best match from options ---
function findBestMatch(name: string, options: { id: string; name: string }[]): { id: string; name: string } | undefined {
	const normName = normalizeString(name);
	// First try exact normalized match
	let match = options.find(opt => normalizeString(opt.name) === normName);
	if (match) return match;
	// Then try includes logic
	match = options.find(opt => normalizeString(opt.name).includes(normName) || normName.includes(normalizeString(opt.name)));
	return match;
}

const AddLocation = () => {
	const [center, setCenter] = useState<{ lat: number; lng: number }>({
		lat: 36.80075374757571,
		lng: 10.187096463461918,
	})
	const [markerPosition, setMarkerPosition] = useState<{ lat: number; lng: number }>(center)
	const [states, setStates] = useState<State[]>([])
	const [cities, setCities] = useState<City[]>([])
	const { formData, setFormData, formErrors, setFormErrors, validateStep } = useFormContext()
	const [showSuggestions, setShowSuggestions] = useState(false)
	const [suggestions, setSuggestions] = useState<google.maps.places.AutocompletePrediction[]>([])
	const [suppressSuggestions, setSuppressSuggestions] = useState(false)
	const [showAddressInputs, setShowAddressInputs] = useState(false)

	// Ref to prevent map onClick after marker drag
	const isDragRef = useRef(false);
	// Access the Google Maps instance for smooth panning
	const map = useMap();

	// Mark this step as visited when the component mounts and validate the form
	useEffect(() => {
		validateStep("add-location");
	}, []);

	// Add effect to validate when component becomes visible
	useEffect(() => {
		// Re-validate this step whenever formData changes
		validateStep("add-location");
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [formData]);

	const debouncedFetchSuggestions = useCallback(
		debounce((input: string) => {
			if (input && window.google && window.google.maps) {
				const service = new window.google.maps.places.AutocompleteService()
				const sessionToken = new window.google.maps.places.AutocompleteSessionToken()
				service.getPlacePredictions(
					{
						input,
						sessionToken,
						componentRestrictions: { country: "tn" },
						types: ["geocode"],
					},
					(predictions, status) => {
						if (status === window.google.maps.places.PlacesServiceStatus.OK && predictions) {
							setSuggestions(predictions)
							setShowSuggestions(true)
						} else {
							setSuggestions([])
							setShowSuggestions(false)
						}
					},
				)
			} else {
				setSuggestions([])
				setShowSuggestions(false)
			}
		}, 300),
		[]
	)

	const fetchStates = async () => {
		try {
			const response = await fetch("/api/listing/states", { method: "GET" });
			const data = await response.json(); // Parse the response JSON

			if (!response.ok) {
				return { error: data.error || "Unknown error" };
			}

			const propStates = data.states;

			setStates(propStates)

		} catch (err) {
			return { error: "Network error" };
		}
	};

	useEffect(() => {
		fetchStates()
	}, [])

	useEffect(() => {
		const fetchCities = async (statesId: string) => {
			const response = await fetch(`/api/listing/states/${statesId}/cities`, {
				method: "GET",
			});
			const data = await response.json();
			setCities(data.cities)
		}
		if (formData.propertyStateId) {
			fetchCities(formData.propertyStateId)
			setFormData((prev) => ({ ...prev, propertyCity: "" })) // Reset city selection
		}
	}, [formData.propertyState, setFormData])

	// Handle form field changes
	const handleChange = (field: keyof typeof formData, value: string | number) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
		// Validation will be handled by the useEffect below
	};

	useEffect(() => {
		if (!suppressSuggestions) {
			debouncedFetchSuggestions(formData.propertyAddress)
		} else {
			setShowSuggestions(false);
			setSuppressSuggestions(false);
		}
	}, [formData.propertyAddress, debouncedFetchSuggestions])

	// --- Fetch all states on mount ---
	useEffect(() => {
		fetch('/api/listing/states')
			.then(res => res.json())
			.then(data => {
				setStates(data.states || []);
			});
	}, []);

	// --- Fetch cities when a state is selected ---
	useEffect(() => {
		if (formData.propertyStateId) {
			fetch(`/api/listing/states/${formData.propertyStateId}/cities`)
				.then(res => res.json())
				.then(data => {
					setCities(data.cities || []);
				});
		} else {
			setCities([]);
		}
	}, [formData.propertyStateId]);


	// --- Update geocode handler to match and set dropdown values ---
	const handleSelect = (description: string) => {
		setSuppressSuggestions(true);
		setShowSuggestions(false);
		setSuggestions([]);
		if (window.google && window.google.maps) {
			const geocoder = new window.google.maps.Geocoder();
			geocoder.geocode({ address: description }, (results, status) => {
				if (status === 'OK' && results && results[0]) {
					const { lat, lng } = results[0].geometry.location;
					const newLat = typeof lat === 'function' ? lat() : lat;
					const newLng = typeof lng === 'function' ? lng() : lng;
					const { region, city } = extractRegionAndCity(results[0].address_components);
					applyGeocodedLocation({ address: description, region, city, lat: newLat, lng: newLng });
				} else {
					setFormData((prev) => ({ ...prev, propertyAddress: cleanPlusCode(description) }));
				}
			});
		} else {
			setFormData((prev) => ({ ...prev, propertyAddress: cleanPlusCode(description) }));
		}
	};

	// --- Dropdown change handlers ---
	const handleStateChange = (option: { id: string; name: string } | undefined) => {
		if (option) {
			setFormData(prev => ({
				...prev,
				propertyState: option.name,
				propertyStateId: option.id,
				propertyCity: '',
				propertyCityId: '',
			}));
		} else {
			setFormData(prev => ({
				...prev,
				propertyState: '',
				propertyStateId: '',
				propertyCity: '',
				propertyCityId: '',
			}));
		}
	};
	const handleCityChange = (option: { id: string; name: string } | undefined) => {
		if (option) {
			setFormData(prev => ({
				...prev,
				propertyCity: option.name,
				propertyCityId: option.id,
			}));
		} else {
			setFormData(prev => ({
				...prev,
				propertyCity: '',
				propertyCityId: '',
			}));
		}
	};

	// --- Shared function to apply geocoded location with robust matching ---
	function applyGeocodedLocation({ address, region, city, lat, lng }: { address: string; region: string; city: string; lat: number; lng: number }) {
		const cleaned = cleanPlusCode(address);
		const matchedState = findBestMatch(region, states);
		if (matchedState) {
			fetch(`/api/listing/states/${matchedState.id}/cities`)
				.then(res => res.json())
				.then(data => {
					setCities(data.cities || []);
					const matchedCity = findBestMatch(city, data.cities || []);
					setFormData((prev) => {
						const newData = {
							...prev,
							propertyAddress: cleaned,
							latitude: lat,
							longitude: lng,
							propertyState: matchedState ? matchedState.name : '',
							propertyStateId: matchedState ? matchedState.id : '',
							propertyCity: matchedCity ? matchedCity.name : '',
							propertyCityId: matchedCity ? matchedCity.id : '',
						}
						return newData;
					});
				});
		} else {
			setFormData((prev) => {
				const newData = {
					...prev,
					propertyAddress: cleaned,
					latitude: lat,
					longitude: lng,
					propertyState: '',
					propertyStateId: '',
					propertyCity: '',
					propertyCityId: '',
				};
				return newData;
			});
		}
		// Update marker and pan map without resetting center state
		setMarkerPosition({ lat, lng });
		map?.panTo({ lat, lng });
		if (cleaned || region || city) setShowAddressInputs(true);
	}

	// Add useEffect to validate when any relevant field changes
	useEffect(() => {
		validateStep("add-location");
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [formData.propertyStateId, formData.propertyCityId, formData.propertyAddress, formData.latitude, formData.longitude]);

	return (
		<CommonLayout params={{ stepIndex: "add-location" }}>
			<div className="flex flex-col items-center justify-center min-h-[70vh] py-8">
				{/* Header and Subheader with unified animation */}
				<AnimatePresence mode="wait">
					{!showAddressInputs ? (
						<motion.div
							key="header-initial"
							initial={{ opacity: 0, y: -16 }}
							animate={{ opacity: 1, y: 0 }}
							exit={{ opacity: 0, y: -16 }}
							transition={{ duration: 0.4, ease: 'easeInOut' }}
						>
							<h2 className="text-2xl md:text-3xl font-bold text-center mb-2">Où est situé votre logement ?</h2>
							<p className="text-center text-gray-500 mb-6">Indiquez l&apos;emplacement précis pour aider les voyageurs à trouver votre logement facilement.</p>
						</motion.div>
					) : (
						<motion.div
							key="header-after"
							initial={{ opacity: 0, y: -16 }}
							animate={{ opacity: 1, y: 0 }}
							exit={{ opacity: 0, y: -16 }}
							transition={{ duration: 0.4, ease: 'easeInOut' }}
						>
							<h2 className="text-[1.4rem] md:text-3xl 2xl:pt-24 font-bold text-center mb-2">Le repère est-il au bon endroit?</h2>
							<p className="text-center md:text-left text-gray-500 mb-6 font-medium">Votre adresse est uniquement communiquée aux voyageurs une fois leur réservation effectuée.</p>
						</motion.div>
					)}
				</AnimatePresence>
				{/* Region, City, and Address block with unified animation */}
				<AnimatePresence>
					{showAddressInputs && (
						<motion.div
							key="inputs"
							initial={{ opacity: 0, y: -16 }}
							animate={{ opacity: 1, y: 0 }}
							exit={{ opacity: 0, y: -16 }}
							transition={{ duration: 0.4, ease: 'easeInOut' }}
							className="w-full max-w-xl mx-auto mb-6"
						>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-4">
								<div>
									<label htmlFor="region" className="block font-bold !mb-3">Région</label>
									<CustomSelect
										options={states}
										value={formData.propertyState || ''}
										onChange={handleStateChange}
										placeholder="Choisissez une région"
									/>
									{formErrors.propertyStateId && (
										<div className="mt-2 flex items-center bg-red-50 border border-red-200 rounded px-3 py-2">
											<svg className="h-4 w-4 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" strokeWidth="2" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" /></svg>
											<span className="text-xs text-red-700 font-medium">{formErrors.propertyStateId}</span>
										</div>
									)}
								</div>
								<div>
									<label htmlFor="city" className="block font-bold !mb-3">Ville</label>
									<CustomSelect
										options={cities}
										value={formData.propertyCity || ''}
										onChange={handleCityChange}
										placeholder="Choisissez une ville"
									/>
									{(formErrors.propertyCity || formErrors.propertyCityId) && (
										<div className="mt-2 flex items-center bg-red-50 border border-red-200 rounded px-3 py-2">
											<svg className="h-4 w-4 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" strokeWidth="2" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" /></svg>
											<span className="text-xs text-red-700 font-medium">{formErrors.propertyCity || formErrors.propertyCityId}</span>
										</div>
									)}
								</div>
								<div>
									<label htmlFor="address" className="block font-bold !mb-3">Adresse détaillée</label>
									<input
										id="address"
										type="text"
										value={formData.propertyAddress !== null && formData.propertyAddress !== undefined ? formData.propertyAddress : ''}
										onChange={e => handleChange('propertyAddress', e.target.value)}
										className="w-full rounded-lg border border-gray-200 px-4 !py-3 bg-transparent text-base focus:outline-none focus:border-orange-400"
									/>
									{formErrors.propertyAddress && (
										<div className="mt-2 flex items-center bg-red-50 border border-red-200 rounded px-3 py-2">
											<svg className="h-4 w-4 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" strokeWidth="2" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" /></svg>
											<span className="text-xs text-red-700 font-medium">{formErrors.propertyAddress}</span>
										</div>
									)}
								</div>
							</div>
						</motion.div>
					)}
				</AnimatePresence>
				{/* Map with overlayed search input */}
				<div className="relative w-full max-w-xl mx-auto">
					{/* Floating search input with icon */}
					<div className="absolute left-1/2 top-4 -translate-x-1/2 w-[90%] max-w-lg z-10 py-2">
						<div className="relative w-full">
							<MapPinIcon className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-orange-500 pointer-events-none" aria-hidden="true" />
							<input
								type="text"
								value={formData.propertyAddress !== null && formData.propertyAddress !== undefined ? formData.propertyAddress : ''}
								onChange={e => {
									const value = e.target.value;
									setFormData(prev => ({ ...prev, propertyAddress: value }));
									debouncedFetchSuggestions(value);
								}}
								placeholder="Recherchez une ville, une adresse..."
								className="w-full rounded-full border border-gray-200 bg-white !pl-11 !py-3 shadow focus:outline-none focus:border-orange-400 text-base"
							/>
						</div>
						{showSuggestions && suggestions.length > 0 && (
							<ul className="absolute left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg z-20 max-h-60 overflow-auto">
								{suggestions.map(({ place_id, description }) => (
									<li
										key={place_id}
										className="cursor-pointer px-5 py-3 hover:bg-gray-100 text-base text-neutral-700 first:rounded-t-xl last:rounded-b-xl"
										onClick={() => handleSelect(description)}
									>
										{description}
									</li>
								))}
							</ul>
						)}
					</div>
					<div className="overflow-hidden rounded-2xl border border-neutral-200">
						<Map
							style={{ width: "100%", height: 350 }}
							defaultZoom={10}
							mapId={process.env.NEXT_PUBLIC_GOOGLE_MAP_ID}
							defaultCenter={center}
							// uncontrolled center for smooth, continuous drag
							gestureHandling={"greedy"}
							disableDefaultUI={true}
							clickableIcons={false}
							onClick={ev => {
								if (isDragRef.current) {
									isDragRef.current = false;
									return;
								}
								if (ev && ev.detail && ev.detail.latLng) {
									const latLngObj = ev.detail.latLng;
									let lat: number = 0;
									let lng: number = 0;
									if (isLatLngObject(latLngObj)) {
										lat = latLngObj.lat();
										lng = latLngObj.lng();
									} else if (
										latLngObj &&
										typeof latLngObj === 'object' &&
										Object.prototype.hasOwnProperty.call(latLngObj, 'lat') &&
										Object.prototype.hasOwnProperty.call(latLngObj, 'lng')
									) {
										lat = typeof (latLngObj as any).lat === 'number' ? (latLngObj as any).lat : 0;
										lng = typeof (latLngObj as any).lng === 'number' ? (latLngObj as any).lng : 0;
									}
									const latLng = { lat, lng };
									setMarkerPosition(latLng);
									map?.panTo(latLng);
									setFormData(prev => ({
										...prev,
										latitude: lat,
										longitude: lng
									}));
									// Always reverse geocode to update city/state fields
									reverseGeocodeLatLng(lat, lng, (result) => {
										if (result.address) {
											applyGeocodedLocation({
												address: result.address,
												region: result.region,
												city: result.city,
												lat,
												lng,
											});
										}
									});
								}
							}}
						>
							<AdvancedMarker
								position={markerPosition}
								draggable
								onDragEnd={ev => {
									if (ev && ev.latLng) {
										const latLngObj = ev.latLng;
										let lat: number = 0;
										let lng: number = 0;
										if (isLatLngObject(latLngObj)) {
											lat = latLngObj.lat();
											lng = latLngObj.lng();
										} else if (
											latLngObj &&
											typeof latLngObj === 'object' &&
											Object.prototype.hasOwnProperty.call(latLngObj, 'lat') &&
											Object.prototype.hasOwnProperty.call(latLngObj, 'lng')
										) {
											lat = typeof (latLngObj as any).lat === 'number' ? (latLngObj as any).lat : 0;
											lng = typeof (latLngObj as any).lng === 'number' ? (latLngObj as any).lng : 0;
										}
										const latLng = { lat, lng };
										setMarkerPosition(latLng);
										map?.panTo(latLng);
										setFormData(prev => ({
											...prev,
											latitude: lat,
											longitude: lng
										}));
										// Always reverse geocode to update city/state fields
										reverseGeocodeLatLng(lat, lng, (result) => {
											if (result.address) {
												applyGeocodedLocation({
													address: result.address,
													region: result.region,
													city: result.city,
													lat,
													lng,
												});
											}
										});
										isDragRef.current = true;
									}
								}}
							>
								<MapPinIcon className="h-8 w-8 text-orange-500 drop-shadow" />
							</AdvancedMarker>
						</Map>
					</div>
					{(formErrors.latitude || formErrors.longitude) && (
						<div className="mt-4 flex items-center bg-red-50 border border-red-200 rounded px-3 py-2 max-w-xl mx-auto">
							<svg className="h-4 w-4 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" strokeWidth="2" /><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" /></svg>
							<span className="text-xs text-red-700 font-medium">
								{formErrors.latitude || formErrors.longitude}
							</span>
						</div>
					)}
				</div>
			</div>
		</CommonLayout>
	)
}

export default AddLocation
