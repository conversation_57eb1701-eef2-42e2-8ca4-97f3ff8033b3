'use client';
import React from 'react';
import { ChevronRight } from 'lucide-react';
import Image from 'next/image';

interface PropertyInfoCardProps {
  name?: string;
  location?: string;
  imageSrc?: string;
}

const PropertyInfoCard: React.FC<PropertyInfoCardProps> = ({ 
  name = 'Dar zyna',
  location = 'Hammamet Sud',
  imageSrc = '/placeholder-property.jpg'
}) => {
  return (
    <div className="flex items-center px-3 py-2 bg-white rounded-xl shadow-sm">
      <div className="w-20 h-14 bg-gray-200 rounded-md mr-3 overflow-hidden">
        <Image 
          src={imageSrc || '/placeholder-property.jpg'}
          alt={name}
          width={80}
          height={59}
          className="w-full h-full object-cover rounded-md"
        />
      </div>
      <div>
        <div className="font-semibold text-sm max-w-[180px] whitespace-nowrap overflow-hidden text-ellipsis">
          {name}
        </div>
        <div className="text-xs text-gray-500">{location}</div>
      </div>
      <div className="ml-auto">
        <ChevronRight size={20} className="text-gray-500" />
      </div>
    </div>
  );
};

export default PropertyInfoCard; 