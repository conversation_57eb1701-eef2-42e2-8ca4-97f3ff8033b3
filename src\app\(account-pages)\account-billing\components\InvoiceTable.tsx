"use client"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { formatDate, formatCurrency } from "../utils/formatters"
import { Badge } from "@/components/ui/badge"

// Define the invoice type
type Invoice = {
    id: string
    invoice_number: string
    start_date: string
    end_date: string
    subscription_plan: string
    payment_amount: number
    listings: {
        id: string
        title: string
        status?: string
    }
    invoice_status?: "pending" | "active"
    invoice_ready?: boolean
    is_activated?: boolean
}

type InvoiceTableProps = {
    invoices: Invoice[]
    isLoading: boolean
    onViewInvoice: (invoice: Invoice) => void
    onDownloadInvoice: (invoice: Invoice) => void
}

const InvoiceTable = ({ invoices, isLoading, onViewInvoice, onDownloadInvoice }: InvoiceTableProps) => {
    if (isLoading) {
        return (
            <div className="w-full py-8 text-center">
                <p className="text-sm text-gray-500">Chargement des factures...</p>
            </div>
        )
    }

    if (!invoices || invoices.length === 0) {
        return (
            <div className="w-full py-8 text-center">
                <p className="text-sm text-gray-500">Aucune facture trouvée.</p>
            </div>
        )
    }

    return (
        <div className="w-full overflow-auto">
            <Table>
                <TableHeader>
                    <TableRow className="bg-gray-50">
                        <TableHead className="font-medium">N° Facture</TableHead>
                        <TableHead className="font-medium">Date début</TableHead>
                        <TableHead className="font-medium">Date fin</TableHead>
                        <TableHead className="font-medium">Hébergement</TableHead>
                        <TableHead className="font-medium">Plan</TableHead>
                        <TableHead className="font-medium">Montant</TableHead>
                        <TableHead className="text-right font-medium">Actions</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {invoices.map((invoice) => (
                        <TableRow key={invoice.id} className={invoice.invoice_status === "pending" ? "opacity-70" : ""}>
                            <TableCell className="font-medium">
                                {invoice.invoice_number}
                                {invoice.invoice_status === "pending" && (
                                    <Badge variant="outline" className="ml-2 bg-amber-50 text-amber-700 border-amber-200">
                                        En attente
                                    </Badge>
                                )}
                            </TableCell>
                            <TableCell>
                                {invoice.invoice_status === "pending" ? (
                                    <span className="text-amber-600 text-sm italic">Après approbation</span>
                                ) : (
                                    formatDate(invoice.start_date)
                                )}
                            </TableCell>
                            <TableCell>
                                {invoice.invoice_status === "pending" ? (
                                    <span className="text-amber-600 text-sm italic">Après approbation</span>
                                ) : (
                                    formatDate(invoice.end_date)
                                )}
                            </TableCell>
                            <TableCell className="max-w-[200px] truncate" title={invoice.listings?.title || "N/A"}>
                                {invoice.listings?.title || "N/A"}
                                {invoice.listings?.status === "pending" && (
                                    <div className="mt-1">
                                        <Badge variant="outline" className="text-xs bg-amber-50 text-amber-700 border-amber-200">
                                            Hébergement en attente
                                        </Badge>
                                    </div>
                                )}
                            </TableCell>
                            <TableCell>{invoice.subscription_plan}</TableCell>
                            <TableCell>{formatCurrency(invoice.payment_amount)}</TableCell>
                            <TableCell className="text-right">
                                <div className="flex justify-end">
                                    {invoice.invoice_status === "pending" ? (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            disabled
                                            className="opacity-50 cursor-not-allowed"
                                            title="Facture disponible après approbation"
                                        >
                                            Details
                                        </Button>
                                    ) : (
                                        <Button variant="outline" size="sm" onClick={() => onViewInvoice(invoice)} title="Voir la facture">
                                            Details
                                        </Button>
                                    )}
                                </div>
                                {invoice.invoice_status === "pending" && (
                                    <div className="mt-2 text-xs text-amber-600 bg-amber-50 p-2 rounded text-center">
                                        Facture disponible après approbation
                                    </div>
                                )}
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </div>
    )
}

export default InvoiceTable
