'use client';
import React, { useState } from 'react';
import { Search, ChevronDown } from 'lucide-react';

interface TransactionFiltersProps {
    onSearch: (searchTerm: string) => void;
    onFilterChange: (filterType: string, value: string) => void;
}

const TransactionFilters: React.FC<TransactionFiltersProps> = ({ onSearch, onFilterChange }) => {
    const [searchTerm, setSearchTerm] = useState('');

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setSearchTerm(value);
        onSearch(value);
    };

    return (
        <div className="flex flex-col w-full gap-4">
            {/* Search Bar */}
            <div className="relative w-full">
                <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                    <Search className="text-[#EA5911]" size={18} />
                </div>
                <input
                    type="text"
                    className="w-full pl-10 pr-4 py-2.5 rounded-full border border-gray-200 focus:outline-none focus:ring-1 focus:ring-orange-500 placeholder:font-semibold"
                    placeholder="Recherche payante"
                    value={searchTerm}
                    onChange={handleSearchChange}
                />
            </div>

        </div>
    );
};

export default TransactionFilters; 