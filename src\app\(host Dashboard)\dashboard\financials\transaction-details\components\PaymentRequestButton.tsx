 'use client';
import React, { useState } from 'react';

interface PaymentRequestButtonProps {
  payoutId: string;
}

const PaymentRequestButton: React.FC<PaymentRequestButtonProps> = ({ payoutId }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleClick = async () => {
    if (isLoading || isSubmitted) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/financials/payment-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ payoutId }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `Error ${response.status}: Failed to submit request`);
      }

      console.log("Payment request submitted successfully:", result);
      setIsSubmitted(true);

    } catch (err: any) {
      console.error("Payment request error:", err);
      setError(err.message || "An unexpected error occurred.");

    } finally {
      setIsLoading(false);
    }
  };

  let buttonText = "Demander un paiement";
  if (isLoading) {
    buttonText = "Envoi...";
  } else if (isSubmitted) {
    buttonText = "Demande envoyée";
  }

  return (
    <div className="w-full">
      <button
        onClick={handleClick}
        disabled={isLoading || isSubmitted}
        className={`w-full py-3 rounded-lg border text-sm font-semibold transition 
          ${isSubmitted 
            ? 'bg-gray-200 border-gray-300 text-gray-500 cursor-not-allowed' 
            : isLoading 
              ? 'bg-gray-100 border-gray-300 text-gray-500 cursor-wait' 
              : 'bg-white border-black text-black hover:bg-gray-50 active:bg-gray-100'
          }`}
      >
        {buttonText}
      </button>
      {error && (
        <p className="mt-2 text-xs text-red-600">Erreur: {error}</p>
      )}
    </div>
  );
};

export default PaymentRequestButton; 