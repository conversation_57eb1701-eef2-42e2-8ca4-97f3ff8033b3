export function StatisticsSection() {
  // Mock statistics data
  const statistics = [
    {
      figure: "250K+",
      label: "Utilisateurs actifs",
    },
    {
      figure: "15K+",
      label: "Propriétés disponibles",
    },
    {
      figure: "98%",
      label: "Taux de satisfaction",
    },
  ]

  return (
    <div className="w-full">
      <div className="bg-[#f5f5f5] rounded-xl p-8">
        <div className="flex flex-col md:flex-row gap-8 justify-evenly">
          {statistics.map((stat, index) => (
            <div
              key={index}
              className="bg-[#ea580e] hover:bg-[#333333] rounded-lg py-8 w-full md:w-1/4 flex flex-col items-center justify-center text-white transition-colors duration-300 cursor-pointer"
            >
              <div className="text-4xl font-bold mb-3">{stat.figure}</div>
              <div className="text-base font-medium">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

