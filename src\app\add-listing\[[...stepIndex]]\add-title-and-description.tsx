"use client"

import React, { useEffect } from "react"
import CommonLayout from "./CommonLayout"
import Textarea from "@/shared/Textarea"
import Input from "@/shared/Input"
import { useFormContext } from "../FormContext"

const AddTitleAndDescription = () => {
    const { formData, setFormData, formErrors, setFormErrors, validateStep } = useFormContext();

    // Validate on mount if values exist (e.g., from draft)
    useEffect(() => {
        if (formData.listingTitle || formData.listingDescription) {
            validateStep("add-title-and-description");
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setFormData(prev => ({ ...prev, listingTitle: e.target.value }));
        // Optionally clear the error for this field as user types
        if (formErrors.listingTitle) {
            setFormErrors(prev => {
                const { listingTitle, ...rest } = prev;
                return rest;
            });
        }
    };

    const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setFormData(prev => ({ ...prev, listingDescription: e.target.value }));
        // Optionally clear the error for this field as user types
        if (formErrors.listingDescription) {
            setFormErrors(prev => {
                const { listingDescription, ...rest } = prev;
                return rest;
            });
        }
    };

    return (
        <CommonLayout params={{ stepIndex: "add-title-and-description" }}>
            <div className="max-w-3xl mx-auto px-4 2xl:pt-24 py-16">
                {/* Header Section */}
                <div className="mb-10">
                    <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-3 leading-tight">
                        Comment aimeriez–vous présenter votre logement aux voyageurs ?
                    </h2>
                    <span className="block text-base text-neutral-500 mb-2">
                        Inspirez vos voyageurs avec une description vivante et accueillante — soyez authentique et chaleureux.
                    </span>
                </div>

                {/* Form Section */}
                <div className="space-y-10">
                    <div>
                        <label className="block font-semibold text-base mb-2" htmlFor="listing-title">
                            Donnez un titre à votre annonce
                        </label>
                        <Input
                            id="listing-title"
                            className={`rounded-[8px] border ${formErrors.listingTitle ? 'border-red-500' : 'border-neutral-300'} w-full h-12 px-4 text-base focus:outline-none focus:border-booking-orange transition`}
                            placeholder="Maison cosy au bord de la mer, Appartement moderne au centre-ville..."
                            value={formData.listingTitle || ""}
                            onChange={handleTitleChange}
                        />
                        {formErrors.listingTitle && (
                            <div className="mt-2 text-red-500 text-sm">
                                {formErrors.listingTitle}
                            </div>
                        )}
                        <div className="mt-1 text-neutral-500 text-sm flex justify-end">
                            {formData.listingTitle?.length || 0}/100
                        </div>
                    </div>
                    <div>
                        <label className="block font-semibold text-base mb-2" htmlFor="listing-description">
                            Créez votre description
                        </label>
                        <Textarea
                            id="listing-description"
                            className={`rounded-[8px] border ${formErrors.listingDescription ? 'border-red-500' : 'border-neutral-300'} w-full px-4 py-3 text-base focus:outline-none focus:border-booking-orange transition min-h-[140px] resize-none`}
                            placeholder="Décrivez votre logement, ce qui le rend unique, l'environnement, les activités proches..."
                            rows={8}
                            value={formData.listingDescription || ""}
                            onChange={handleDescriptionChange}
                        />
                        {formErrors.listingDescription && (
                            <div className="mt-2 text-red-500 text-sm">
                                {formErrors.listingDescription}
                            </div>
                        )}
                        <div className="mt-1 text-neutral-500 text-sm flex justify-end">
                            {formData.listingDescription?.length || 0}/2000
                        </div>
                    </div>
                </div>
            </div>
        </CommonLayout>
    )
}

export default AddTitleAndDescription 