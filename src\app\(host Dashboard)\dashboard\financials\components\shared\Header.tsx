'use client';
import React from 'react';
import { ArrowLeft, Settings } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface HeaderProps {
  title: string;
}

const Header: React.FC<HeaderProps> = ({ title }) => {
  const router = useRouter();

  return (
    <div className="flex items-center justify-between py-4">
      <div className="flex items-center">
        <button
          onClick={() => router.back()}
          className="mr-3 text-gray-600 hover:text-gray-800"
        >
          <ArrowLeft size={20} />
        </button>
        <h1 className="text-xl font-semibold">{title}</h1>
      </div>
      <button className="text-gray-600 hover:text-gray-800">
        <Settings size={20} />
      </button>
    </div>
  );
};

export default Header; 