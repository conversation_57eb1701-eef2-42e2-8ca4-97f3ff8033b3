'use client';
import React from 'react';
import { ChevronRight } from 'lucide-react';
import StatusBadge from './StatusBadge';

export interface Transaction {
  id: string;
  reservation: string;
  status: string;
  amount: number;
  date?: string;
}

interface TransactionRowProps {
  transaction: Transaction;
  onClick: () => void;
  highlight?: boolean;
}

const TransactionRow: React.FC<TransactionRowProps> = ({ transaction, onClick, highlight = false }) => {
  return (
    <div
      className={`grid grid-cols-3 items-center px-6 py-3 border-b border-[#E8EAED] text-[12px] font-medium cursor-pointer transition ${
        highlight ? 'bg-[#FED8AB33]' : 'bg-white'
      }`}
      onClick={onClick}
    >
      {/* Reservation Number */}
      <div className="truncate text-left text-gray-600">
        {transaction.reservation?.toUpperCase()}
      </div>
      
      {/* Amount */}
      <div className="truncate text-center">
        {transaction.amount} TND
      </div>
      
      {/* Status with Chevron */}
      <div className="flex items-center justify-between">
        <StatusBadge status={transaction.status} />
        <ChevronRight className="text-gray-400" size={16} />
      </div>
    </div>
  );
};

export default TransactionRow; 