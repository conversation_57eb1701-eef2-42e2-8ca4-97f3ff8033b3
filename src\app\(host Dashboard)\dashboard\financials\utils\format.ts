export function formatTND(amount: number): string {
  if (typeof amount !== 'number' || isNaN(amount)) return '--';
  // Format with fr-FR, up to 2 decimals, but hide decimals if .00
  const formatted = amount % 1 === 0
    ? amount.toLocaleString('fr-FR', { minimumFractionDigits: 0, maximumFractionDigits: 0 })
    : amount.toLocaleString('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  return `${formatted} DT`;
}

export function formatDateRange(start?: string, end?: string): string {
  if (!start || !end) return '';
  const startDate = new Date(start);
  const endDate = new Date(end);
  const options: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric' };
  return `${startDate.toLocaleDateString('fr-FR', options)} - ${endDate.toLocaleDateString('fr-FR', options)}, ${endDate.getFullYear()}`;
} 