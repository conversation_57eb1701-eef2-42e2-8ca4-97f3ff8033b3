"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { Play, ChevronLeft, ChevronRight } from "lucide-react"

// Mock data for history videos
const historyVideos = [
  {
    id: 1,
    year: "2020",
    title: "Création de la plateforme",
    videoThumbnail: "/placeholder.svg?height=720&width=1280",
    videoUrl: "#",
  },
  {
    id: 2,
    year: "2022",
    title: "Expansion internationale",
    videoThumbnail: "/placeholder.svg?height=720&width=1280",
    videoUrl: "#",
  },
  {
    id: 3,
    year: "2024",
    title: "Intégration de l'IA",
    videoThumbnail: "/placeholder.svg?height=720&width=1280",
    videoUrl: "#",
  },
]

export function HistoryTimeline() {
  const [currentIndex, setCurrentIndex] = useState(1) // Start with middle video selected
  const [isMobile, setIsMobile] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)
  const [slideDirection, setSlideDirection] = useState("") // 'left' or 'right'

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkIfMobile()
    window.addEventListener("resize", checkIfMobile)

    return () => {
      window.removeEventListener("resize", checkIfMobile)
    }
  }, [])

  const handlePlayVideo = (index: number) => {
    if (isAnimating) return

    // Set slide direction
    setSlideDirection(index > currentIndex ? "right" : "left")
    setIsAnimating(true)

    // Add a small delay to allow animation to complete
    setTimeout(() => {
      setCurrentIndex(index)
      setIsAnimating(false)
    }, 300)

    // In a real implementation, this would trigger the video to play
    console.log(`Playing video ${index + 1}`)
  }

  const nextSlide = () => {
    if (isAnimating) return

    setSlideDirection("right")
    setIsAnimating(true)

    setTimeout(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % historyVideos.length)
      setIsAnimating(false)
    }, 300)
  }

  const prevSlide = () => {
    if (isAnimating) return

    setSlideDirection("left")
    setIsAnimating(true)

    setTimeout(() => {
      setCurrentIndex((prevIndex) => (prevIndex - 1 + historyVideos.length) % historyVideos.length)
      setIsAnimating(false)
    }, 300)
  }

  // Reorder videos to always show the selected one in the middle on desktop
  const getOrderedVideos = () => {
    if (isMobile) {
      return historyVideos
    }

    // For desktop, reorder so current is in the middle
    const result = [...historyVideos]

    // If the first video is selected, move it to the middle
    if (currentIndex === 0) {
      const first = result.shift()
      if (first) result.splice(1, 0, first)
    }

    // If the last video is selected, move it to the middle
    if (currentIndex === 2) {
      const last = result.pop()
      if (last) result.splice(1, 0, last)
    }

    return result
  }

  const orderedVideos = getOrderedVideos()

  return (
    <div className="w-full px-4">
      {/* Timeline connector - only visible on desktop */}
      <div className="hidden md:block absolute top-1/2 left-0 right-0 h-0.5 bg-[#d9d9d9] -translate-y-1/2 z-0"></div>

      {/* Mobile navigation controls */}
      <div className="flex md:hidden justify-between items-center mb-4">
        <button
          onClick={prevSlide}
          className="w-10 h-10 rounded-full border border-[#d9d9d9] flex items-center justify-center hover:bg-[#f5f5f5] transition-colors"
          aria-label="Vidéo précédente"
          disabled={isAnimating}
        >
          <ChevronLeft size={20} />
        </button>
        <div className="text-sm">
          {currentIndex + 1} / {historyVideos.length}
        </div>
        <button
          onClick={nextSlide}
          className="w-10 h-10 rounded-full border border-[#d9d9d9] flex items-center justify-center hover:bg-[#f5f5f5] transition-colors"
          aria-label="Vidéo suivante"
          disabled={isAnimating}
        >
          <ChevronRight size={20} />
        </button>
      </div>

      {/* Mobile view with animation */}
      {isMobile && (
        <div className="relative overflow-hidden">
          <div
            className={`transition-transform duration-300 ease-in-out ${
              isAnimating && slideDirection === "right"
                ? "-translate-x-full"
                : isAnimating && slideDirection === "left"
                  ? "translate-x-full"
                  : "translate-x-0"
            }`}
          >
            <div className="bg-[#d9d9d9] rounded-xl overflow-hidden relative z-10 h-64 w-full ring-2 ring-[#ea580e]">
              {/* Video thumbnail */}
              <div className="w-full h-full relative">
                <Image
                  src={historyVideos[currentIndex].videoThumbnail || "/placeholder.svg"}
                  alt={historyVideos[currentIndex].title}
                  fill
                  className="object-cover"
                />

                {/* Video overlay with play button */}
                <div
                  className="absolute inset-0 bg-black bg-opacity-30 flex flex-col items-center justify-center cursor-pointer hover:bg-opacity-40 transition-all"
                  onClick={() => console.log(`Playing video ${currentIndex + 1}`)}
                >
                  <button
                    className="w-12 h-12 bg-[#ea580e] rounded-full flex items-center justify-center text-white hover:bg-opacity-90 transition-all mb-2"
                    aria-label={`Lire la vidéo ${historyVideos[currentIndex].title}`}
                  >
                    <Play size={24} className="ml-1" />
                  </button>
                  <div className="text-white text-center px-4">
                    <div className="font-bold">{historyVideos[currentIndex].year}</div>
                    <div className="text-sm">{historyVideos[currentIndex].title}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Desktop view with animation */}
      {!isMobile && (
        <div className="flex flex-row justify-evenly gap-8 relative overflow-hidden">
          {orderedVideos.map((video, index) => {
            const originalIndex = historyVideos.findIndex((v) => v.id === video.id)
            const isSelected = originalIndex === currentIndex

            return (
              <div
                key={video.id}
                className={`bg-[#d9d9d9] rounded-xl overflow-hidden relative z-10 transition-all duration-500 ease-in-out transform hover:scale-105 ${
                  index === 1 ? "h-64 w-full md:w-1/3" : "h-48 w-full md:w-1/4"
                } ${isSelected ? "ring-2 ring-[#ea580e] md:ring-0" : ""}`}
                style={{
                  transition: "all 0.5s ease-in-out",
                }}
              >
                {/* Video thumbnail */}
                <div className="w-full h-full relative">
                  <Image
                    src={video.videoThumbnail || "/placeholder.svg"}
                    alt={video.title}
                    fill
                    className="object-cover"
                  />

                  {/* Video overlay with play button */}
                  <div
                    className="absolute inset-0 bg-black bg-opacity-30 flex flex-col items-center justify-center cursor-pointer hover:bg-opacity-40 transition-all"
                    onClick={() => handlePlayVideo(originalIndex)}
                  >
                    <button
                      className="w-12 h-12 bg-[#ea580e] rounded-full flex items-center justify-center text-white hover:bg-opacity-90 transition-all mb-2 hover:scale-110 active:scale-90"
                      aria-label={`Lire la vidéo ${video.title}`}
                    >
                      <Play size={24} className="ml-1" />
                    </button>
                    <div className="text-white text-center px-4">
                      <div className="font-bold">{video.year}</div>
                      <div className="text-sm">{video.title}</div>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      )}

      {/* Desktop navigation indicators */}
      <div className="hidden md:flex justify-center mt-12 gap-2">
        {historyVideos.map((_, index) => (
          <button
            key={index}
            onClick={() => handlePlayVideo(index)}
            className={`h-3 rounded-full transition-all duration-300 ${
              index === currentIndex ? "bg-[#ea580e] w-6" : "bg-[#d9d9d9] w-3"
            }`}
            aria-label={`Voir vidéo ${index + 1}`}
          />
        ))}
      </div>

      {/* Explorer illustration positioned at the bottom right of the section */}
      <div className="absolute bottom-0 right-0 w-40 h-40 md:w-48 md:h-48 z-10">
        <Image
          src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-Q9c1oudare8nInIDxWeU4n9OvmTJMA.png"
          alt="Explorer with binoculars"
          width={200}
          height={200}
          className="w-full h-auto"
        />
      </div>
    </div>
  )
}

