/**
 * Formats a date string to display in the format "1 August 2023"
 * @param dateString - Date string in any valid format
 * @returns Formatted date string
 */
export function formatDate(dateString: string): string {
    if (!dateString) return '';

    try {
        const date = new Date(dateString);

        // Check if date is valid
        if (isNaN(date.getTime())) {
            return dateString;
        }

        // Format the date using the Intl.DateTimeFormat API
        return date.toLocaleDateString('fr-FR', {
            day: 'numeric',
            month: 'short',
            year: 'numeric'
        });
    } catch (error) {
        console.error('Error formatting date:', error);
        return dateString;
    }
}

/**
 * Formats a number as currency in TND
 * @param amount - The amount to format
 * @returns Formatted amount string with TND
 */
export function formatCurrency(amount: number | null | undefined): string {
    if (amount === null || amount === undefined) return '';

    try {
        return `${amount.toFixed(2)} TND`;
    } catch (error) {
        console.error('Error formatting currency:', error);
        return `${amount} TND`;
    }
}