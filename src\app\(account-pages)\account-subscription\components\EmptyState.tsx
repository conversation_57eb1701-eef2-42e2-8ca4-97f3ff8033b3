import { Card, CardContent } from "@/components/ui/card"
import { AlertCircle } from "lucide-react"

const EmptyState = () => {
    return (
        <Card className="border-gray-200">
            <CardContent className="p-8 flex flex-col items-center justify-center">
                <div className="w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center mb-4">
                    <AlertCircle className="w-6 h-6 text-[#2D5BFF]" />
                </div>
                <h3 className="text-base font-semibold mb-2">Aucun abonnement trouvé</h3>
                <p className="text-sm text-gray-500 text-center max-w-md">
                    Vous n&apos;avez pas encore d&apos;abonnements actifs. Créez un hébergement et souscrivez à un abonnement pour
                    le publier.
                </p>
            </CardContent>
        </Card>
    )
}

export default EmptyState
