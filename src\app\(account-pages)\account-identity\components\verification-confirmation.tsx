"use client"

import Image from "next/image"

interface VerificationConfirmationProps {
    onComplete: () => void
}

export function VerificationConfirmation({ onComplete }: VerificationConfirmationProps) {
    return (
        <div className="w-full max-w-lg mx-auto text-center py-8">
            <div className="flex justify-center mb-6">
                <Image src="/hourglass.png" alt="Hourglass" width={186} height={186} priority />
            </div>

            <h2 className="text-2xl font-bold mb-4">Nous vous informerons dès que vous serez vérifié</h2>

            <p className="text-gray-600 mb-8">
                Nous vous enverrons un e-mail dans l&apos;heure pour vous informer si vous avez été vérifié avec succès ou si nous
                avons besoin de plus d&apos;informations de votre part.
            </p>

            <button
                onClick={onComplete}
                className="px-6 py-2 rounded-md bg-black text-white font-medium hover:bg-gray-800 mx-auto"
            >
                J&apos;ai compris
            </button>
        </div>
    )
}
