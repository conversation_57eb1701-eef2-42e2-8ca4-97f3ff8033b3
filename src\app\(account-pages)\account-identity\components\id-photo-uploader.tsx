"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Camera, Upload } from "lucide-react"
import Image from "next/image"

type IdType = "cin" | "passport" | "permit"
type UploadMethod = "photo" | "webcam"

interface IdPhotoUploaderProps {
    idType: IdType
    uploadMethod: UploadMethod
    onComplete: (frontFile: File, backFile: File | null, idNumber: string) => void
    onBack: () => void
    onIdNumberChange?: (value: string) => void
    idNumber?: string
}

export function IdPhotoUploader({
                                    idType,
                                    uploadMethod,
                                    onComplete,
                                    onBack,
                                    onIdNumberChange,
                                    idNumber = "",
                                }: IdPhotoUploaderProps) {
    // State for file uploads
    const [frontFile, setFrontFile] = useState<File | null>(null)
    const [backFile, setBackFile] = useState<File | null>(null)
    const [frontPreview, setFrontPreview] = useState<string | null>(null)
    const [backPreview, setBackPreview] = useState<string | null>(null)
    const [currentIdNumber, setCurrentIdNumber] = useState(idNumber)
    const [idNumberError, setIdNumberError] = useState<string | null>(null)

    // State for webcam
    const [isCapturing, setIsCapturing] = useState(false)
    const [currentCapture, setCurrentCapture] = useState<"front" | "back" | null>(null)
    const [cameraError, setCameraError] = useState<string | null>(null)

    // Refs
    const videoRef = useRef<HTMLVideoElement>(null)
    const canvasRef = useRef<HTMLCanvasElement>(null)
    const frontInputRef = useRef<HTMLInputElement>(null)
    const backInputRef = useRef<HTMLInputElement>(null)

    // Check if back image is required based on ID type
    const isBackRequired = idType !== "passport"

    // Get the title based on ID type
    const getTitle = () => {
        switch (idType) {
            case "cin":
                return "Importez des photos de votre carte d'identité"
            case "passport":
                return "Importez une photo de votre passeport"
            case "permit":
                return "Importez des photos de votre titre de séjour"
            default:
                return "Importez des photos de votre pièce d'identité"
        }
    }

    // Handle ID number change
    const handleIdNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value
        let formattedValue = value

        // Format based on ID type
        if (idType === "permit" && value.length > 0) {
            // Remove non-digits and slashes
            const cleaned = value.replace(/[^\d/]/g, "")

            // If we have more than 2 digits and no slash, add it
            if (cleaned.length > 2 && !cleaned.includes("/")) {
                formattedValue = `${cleaned.substring(0, 2)}/${cleaned.substring(2)}`
            } else {
                formattedValue = cleaned
            }
        } else if (idType === "passport" && value.length > 0) {
            // Make first character uppercase, remove non-alphanumeric
            const cleaned = value.replace(/[^a-zA-Z0-9]/g, "")
            if (cleaned.length > 0) {
                const firstChar = cleaned.charAt(0).toUpperCase()
                const rest = cleaned.substring(1).replace(/[^0-9]/g, "")
                formattedValue = `${firstChar}${rest}`
            }
        } else if (idType === "cin") {
            // Only digits for CIN
            formattedValue = value.replace(/\D/g, "")
        }

        setCurrentIdNumber(formattedValue)
        if (onIdNumberChange) {
            onIdNumberChange(formattedValue)
        }
        validateIdNumber(formattedValue)
    }

    // Validate ID number based on ID type
    const validateIdNumber = (value: string): boolean => {
        if (!value.trim()) {
            setIdNumberError("Le numéro d'identification est requis")
            return false
        }

        let isValid = false
        let errorMessage = null

        switch (idType) {
            case "cin":
                // CIN should be 8 digits
                isValid = /^\d{8}$/.test(value)
                errorMessage = isValid ? null : "Le numéro de CIN doit contenir 8 chiffres"
                break
            case "passport":
                // Passport should be 1 letter followed by 6 digits
                isValid = /^[A-Za-z]\d{6}$/.test(value)
                errorMessage = isValid ? null : "Le numéro de passeport doit être une lettre suivie de 6 chiffres"
                break
            case "permit":
                // Permit should be in format XX/XXXXXX (2 digits/6 digits)
                isValid = /^\d{2}\/\d{6}$/.test(value)
                errorMessage = isValid ? null : "Le numéro de permis doit être au format XX/XXXXXX"
                break
            default:
                isValid = value.trim().length > 0
                errorMessage = isValid ? null : "Le numéro d'identification est requis"
        }

        setIdNumberError(errorMessage)
        return isValid
    }

    // Handle file upload
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, side: "front" | "back") => {
        const file = e.target.files?.[0]
        if (!file) return

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            setCameraError(`Le fichier est trop volumineux. Taille maximale: 5 Mo`)
            return
        }

        // Validate file type
        if (!["image/jpeg", "image/png", "image/jpg"].includes(file.type)) {
            setCameraError(`Format de fichier non pris en charge. Utilisez JPG ou PNG`)
            return
        }

        const reader = new FileReader()
        reader.onload = () => {
            if (side === "front") {
                setFrontFile(file)
                setFrontPreview(reader.result as string)
            } else {
                setBackFile(file)
                setBackPreview(reader.result as string)
            }
            setCameraError(null)
        }
        reader.readAsDataURL(file)
    }

    // Start webcam capture
    const startCapture = async (side: "front" | "back") => {
        try {
            setCameraError(null)
            setCurrentCapture(side)
            setIsCapturing(true)

            // Wait for the next render cycle to ensure videoRef is available
            setTimeout(async () => {
                try {
                    if (!videoRef.current) {
                        console.error("Video element not found")
                        setCameraError("Élément vidéo non disponible")
                        setIsCapturing(false)
                        return
                    }

                    const stream = await navigator.mediaDevices.getUserMedia({
                        video: {
                            facingMode: "environment", // Use back camera if available
                            width: { ideal: 1280 },
                            height: { ideal: 720 },
                        },
                    })

                    // Assign stream to video element
                    videoRef.current.srcObject = stream

                    // Ensure video plays
                    try {
                        await videoRef.current.play()
                    } catch (playError) {
                        console.error("Error playing video:", playError)
                        setCameraError("Impossible de démarrer la caméra. Veuillez vérifier vos permissions.")
                        setIsCapturing(false)
                    }
                } catch (err) {
                    console.error("Error accessing camera:", err)
                    setCameraError("Impossible d'accéder à la caméra. Veuillez vérifier vos permissions.")
                    setIsCapturing(false)
                }
            }, 100)
        } catch (error) {
            console.error("Error in startCapture:", error)
            setCameraError("Une erreur s'est produite lors de l'initialisation de la caméra.")
            setIsCapturing(false)
        }
    }

    // Capture photo from webcam
    const capturePhoto = () => {
        if (!videoRef.current || !canvasRef.current || !currentCapture) {
            setCameraError("Impossible de capturer la photo. Veuillez réessayer.")
            return
        }

        try {
            const video = videoRef.current
            const canvas = canvasRef.current
            const context = canvas.getContext("2d")

            if (!context) {
                setCameraError("Impossible de capturer la photo. Contexte de canvas non disponible.")
                return
            }

            // Set canvas dimensions to match video
            canvas.width = video.videoWidth || 640
            canvas.height = video.videoHeight || 480

            // Draw video frame to canvas
            context.drawImage(video, 0, 0, canvas.width, canvas.height)

            // Convert canvas to blob
            canvas.toBlob(
                (blob) => {
                    if (!blob) {
                        setCameraError("Impossible de créer l'image. Veuillez réessayer.")
                        return
                    }

                    const file = new File([blob], `${currentCapture}-image.jpg`, { type: "image/jpeg" })

                    if (currentCapture === "front") {
                        setFrontFile(file)
                        setFrontPreview(canvas.toDataURL("image/jpeg"))
                    } else {
                        setBackFile(file)
                        setBackPreview(canvas.toDataURL("image/jpeg"))
                    }

                    // Stop webcam
                    stopCapture()
                },
                "image/jpeg",
                0.95,
            )
        } catch (error) {
            console.error("Error capturing photo:", error)
            setCameraError("Une erreur s'est produite lors de la capture de la photo.")
        }
    }

    // Stop webcam capture
    const stopCapture = () => {
        if (videoRef.current && videoRef.current.srcObject) {
            const stream = videoRef.current.srcObject as MediaStream
            stream.getTracks().forEach((track) => track.stop())
            videoRef.current.srcObject = null
        }

        setIsCapturing(false)
        setCurrentCapture(null)
    }

    // Clean up on unmount
    useEffect(() => {
        return () => {
            stopCapture()
        }
    }, [])

    // Handle continue button click
    const handleContinue = () => {
        // Validate ID number
        if (!validateIdNumber(currentIdNumber)) {
            return
        }

        // Check if front image is uploaded
        if (!frontFile) {
            setCameraError("Veuillez télécharger une photo du recto de votre pièce d'identité")
            return
        }

        // For CIN and Permit, back image is required
        if (isBackRequired && !backFile) {
            setCameraError("Veuillez télécharger une photo du verso de votre pièce d'identité")
            return
        }

        // Call onComplete with files
        onComplete(frontFile, backFile, currentIdNumber)
    }

    return (
        <div className="w-full max-w-lg mx-auto">
            <h2 className="text-2xl font-bold mb-4">{getTitle()}</h2>

            <p className="text-gray-600 mb-6">
                Assurez-vous que vos photos ne sont pas floues et que le document est bien visible.
            </p>

            {/* ID Number Input */}
            <div className="mb-6">
                <Label htmlFor="idNumber" className="block mb-2">
                    {idType === "cin"
                        ? "Numéro de CIN"
                        : idType === "passport"
                            ? "Numéro de passeport"
                            : "Numéro de titre de séjour"}
                </Label>
                <Input
                    id="idNumber"
                    type="text"
                    value={currentIdNumber}
                    onChange={handleIdNumberChange}
                    className={`w-full ${idNumberError ? "border-red-500" : ""}`}
                    placeholder={idType === "cin" ? "12345678" : idType === "passport" ? "A123456" : "12/123456"}
                />
                {idNumberError && <p className="text-red-500 text-sm mt-1">{idNumberError}</p>}
                <p className="text-sm text-gray-500 mt-1">
                    {idType === "cin" && "Format: 8 chiffres exactement"}
                    {idType === "passport" && "Format: 1 lettre suivie de 6 chiffres (ex: A123456)"}
                    {idType === "permit" && "Format: 2 chiffres/6 chiffres (ex: 12/123456)"}
                </p>
            </div>

            {/* Error message */}
            {cameraError && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-600 text-sm">{cameraError}</div>
            )}

            {/* Webcam capture modal */}
            {isCapturing && (
                <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-4 max-w-lg w-full">
                        <h3 className="text-xl font-bold mb-4">
                            Prenez une photo du {currentCapture === "front" ? "recto" : "verso"}
                        </h3>

                        <div className="relative aspect-video bg-black rounded-md overflow-hidden mb-4">
                            <video
                                ref={videoRef}
                                autoPlay
                                playsInline
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                    console.error("Video error:", e)
                                    setCameraError("Erreur lors du chargement de la caméra")
                                }}
                            />
                            <canvas ref={canvasRef} className="hidden" />
                        </div>

                        <div className="flex justify-between">
                            <Button variant="outline" onClick={stopCapture}>
                                Annuler
                            </Button>
                            <Button onClick={capturePhoto}>Prendre la photo</Button>
                        </div>
                    </div>
                </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                {/* Front ID upload */}
                <div className="border border-dashed rounded-md p-4">
                    {frontPreview ? (
                        <div className="space-y-4">
                            <div className="aspect-[4/3] relative">
                                <Image
                                    src={frontPreview || "/placeholder.svg"}
                                    alt="Recto de la pièce d'identité"
                                    fill
                                    className="object-contain"
                                />
                            </div>

                            <div className="flex justify-center space-x-2">
                                {uploadMethod === "photo" ? (
                                    <button onClick={() => frontInputRef.current?.click()} className="text-sm text-gray-600 underline">
                                        Changer la photo
                                    </button>
                                ) : (
                                    <button onClick={() => startCapture("front")} className="text-sm text-gray-600 underline">
                                        Reprendre la photo
                                    </button>
                                )}
                            </div>
                        </div>
                    ) : (
                        <div
                            className="h-full flex flex-col items-center justify-center py-8 cursor-pointer"
                            onClick={() => (uploadMethod === "photo" ? frontInputRef.current?.click() : startCapture("front"))}
                        >
                            <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-4">
                                {uploadMethod === "photo" ? (
                                    <Upload className="w-6 h-6 text-gray-500" />
                                ) : (
                                    <Camera className="w-6 h-6 text-gray-500" />
                                )}
                            </div>

                            <p className="font-medium text-center mb-1">
                                Importer le recto de la{" "}
                                {idType === "cin" ? "carte" : idType === "passport" ? "page principale" : "carte"}
                            </p>

                            <p className="text-sm text-gray-500 text-center">
                                {uploadMethod === "photo" ? "Cliquez pour télécharger" : "Cliquez pour prendre une photo"}
                            </p>
                        </div>
                    )}

                    {uploadMethod === "photo" && (
                        <input
                            type="file"
                            ref={frontInputRef}
                            className="hidden"
                            accept="image/jpeg,image/png"
                            onChange={(e) => handleFileChange(e, "front")}
                        />
                    )}
                </div>

                {/* Back ID upload - only show for CIN and Permit */}
                {isBackRequired && (
                    <div className="border border-dashed rounded-md p-4">
                        {backPreview ? (
                            <div className="space-y-4">
                                <div className="aspect-[4/3] relative">
                                    <Image
                                        src={backPreview || "/placeholder.svg"}
                                        alt="Verso de la pièce d'identité"
                                        fill
                                        className="object-contain"
                                    />
                                </div>

                                <div className="flex justify-center space-x-2">
                                    {uploadMethod === "photo" ? (
                                        <button onClick={() => backInputRef.current?.click()} className="text-sm text-gray-600 underline">
                                            Changer la photo
                                        </button>
                                    ) : (
                                        <button onClick={() => startCapture("back")} className="text-sm text-gray-600 underline">
                                            Reprendre la photo
                                        </button>
                                    )}
                                </div>
                            </div>
                        ) : (
                            <div
                                className="h-full flex flex-col items-center justify-center py-8 cursor-pointer"
                                onClick={() => (uploadMethod === "photo" ? backInputRef.current?.click() : startCapture("back"))}
                            >
                                <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-4">
                                    {uploadMethod === "photo" ? (
                                        <Upload className="w-6 h-6 text-gray-500" />
                                    ) : (
                                        <Camera className="w-6 h-6 text-gray-500" />
                                    )}
                                </div>

                                <p className="font-medium text-center mb-1">Importer le verso de la carte</p>

                                <p className="text-sm text-gray-500 text-center">
                                    {uploadMethod === "photo" ? "Cliquez pour télécharger" : "Cliquez pour prendre une photo"}
                                </p>
                            </div>
                        )}

                        {uploadMethod === "photo" && (
                            <input
                                type="file"
                                ref={backInputRef}
                                className="hidden"
                                accept="image/jpeg,image/png"
                                onChange={(e) => handleFileChange(e, "back")}
                            />
                        )}
                    </div>
                )}
            </div>

            <div className="bg-blue-50 rounded-lg p-4 border border-blue-100 mb-6">
                <p className="text-sm text-blue-700">
                    <strong>Prochaine étape :</strong> Après avoir téléchargé votre pièce d&apos;identité, vous devrez prendre un
                    selfie en tenant cette pièce d&apos;identité à côté de votre visage.
                </p>
            </div>

            <div className="flex justify-between">
                <Button variant="outline" onClick={onBack}>
                    Retour
                </Button>

                <Button
                    onClick={handleContinue}
                    disabled={!frontFile || (isBackRequired && !backFile) || !currentIdNumber || !!idNumberError}
                >
                    Continuer
                </Button>
            </div>
        </div>
    )
}
