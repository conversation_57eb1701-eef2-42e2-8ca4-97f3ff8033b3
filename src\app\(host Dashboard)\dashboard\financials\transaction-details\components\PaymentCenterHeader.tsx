'use client';
import React from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';

const PaymentCenterHeader: React.FC = () => {
  const router = useRouter();
  
  return (
    <div className="relative flex items-center justify-between w-full py-3 my-2">
      <span className="text-xl font-semibold">Centre de paiement</span>
      <button
        className=" w-[27px] h-[27px] rounded-full border border-black flex items-center justify-center p-0 bg-white hover:bg-gray-50 active:bg-gray-200 transition"
        aria-label="Fermer"
        onClick={() => router.push('/dashboard/financials')}
      >
        <XMarkIcon className="w-4 h-4 text-black" strokeWidth={2} />
      </button>
    </div>
  );
};

export default PaymentCenterHeader; 